(function(E){var C=function(h){var a=typeof globalThis<"u"&&globalThis||typeof E<"u"&&E||typeof global<"u"&&global||{},u={searchParams:"URLSearchParams"in a,iterable:"Symbol"in a&&"iterator"in Symbol,blob:"FileReader"in a&&"Blob"in a&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in a,arrayBuffer:"ArrayBuffer"in a};function O(e){return e&&DataView.prototype.isPrototypeOf(e)}if(u.arrayBuffer)var x=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],D=ArrayBuffer.isView||function(e){return e&&x.indexOf(Object.prototype.toString.call(e))>-1};function y(e){if(typeof e!="string"&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||e==="")throw new TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function b(e){return typeof e!="string"&&(e=String(e)),e}function w(e){var t={next:function(){var r=e.shift();return{done:r===void 0,value:r}}};return u.iterable&&(t[Symbol.iterator]=function(){return t}),t}function i(e){this.map={},e instanceof i?e.forEach(function(t,r){this.append(r,t)},this):Array.isArray(e)?e.forEach(function(t){if(t.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])},this):e&&Object.getOwnPropertyNames(e).forEach(function(t){this.append(t,e[t])},this)}i.prototype.append=function(e,t){e=y(e),t=b(t);var r=this.map[e];this.map[e]=r?r+", "+t:t},i.prototype.delete=function(e){delete this.map[y(e)]},i.prototype.get=function(e){return e=y(e),this.has(e)?this.map[e]:null},i.prototype.has=function(e){return this.map.hasOwnProperty(y(e))},i.prototype.set=function(e,t){this.map[y(e)]=b(t)},i.prototype.forEach=function(e,t){for(var r in this.map)this.map.hasOwnProperty(r)&&e.call(t,this.map[r],r,this)},i.prototype.keys=function(){var e=[];return this.forEach(function(t,r){e.push(r)}),w(e)},i.prototype.values=function(){var e=[];return this.forEach(function(t){e.push(t)}),w(e)},i.prototype.entries=function(){var e=[];return this.forEach(function(t,r){e.push([r,t])}),w(e)},u.iterable&&(i.prototype[Symbol.iterator]=i.prototype.entries);function m(e){if(!e._noBody){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}}function g(e){return new Promise(function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}})}function P(e){var t=new FileReader,r=g(t);return t.readAsArrayBuffer(e),r}function R(e){var t=new FileReader,r=g(t),n=/charset=([A-Za-z0-9_-]+)/.exec(e.type),s=n?n[1]:"utf-8";return t.readAsText(e,s),r}function U(e){for(var t=new Uint8Array(e),r=new Array(t.length),n=0;n<t.length;n++)r[n]=String.fromCharCode(t[n]);return r.join("")}function T(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function B(){return this.bodyUsed=!1,this._initBody=function(e){this.bodyUsed=this.bodyUsed,this._bodyInit=e,e?typeof e=="string"?this._bodyText=e:u.blob&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:u.formData&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:u.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():u.arrayBuffer&&u.blob&&O(e)?(this._bodyArrayBuffer=T(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):u.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(e)||D(e))?this._bodyArrayBuffer=T(e):this._bodyText=e=Object.prototype.toString.call(e):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof e=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):u.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},u.blob&&(this.blob=function(){var e=m(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var e=m(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(u.blob)return this.blob().then(P);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var e=m(this);if(e)return e;if(this._bodyBlob)return R(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(U(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},u.formData&&(this.formData=function(){return this.text().then(S)}),this.json=function(){return this.text().then(JSON.parse)},this}var j=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function H(e){var t=e.toUpperCase();return j.indexOf(t)>-1?t:e}function l(e,t){if(!(this instanceof l))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t=t||{};var r=t.body;if(e instanceof l){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new i(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,!r&&e._bodyInit!=null&&(r=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",(t.headers||!this.headers)&&(this.headers=new i(t.headers)),this.method=H(t.method||this.method||"GET"),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal||function(){if("AbortController"in a){var o=new AbortController;return o.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&r)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(r),(this.method==="GET"||this.method==="HEAD")&&(t.cache==="no-store"||t.cache==="no-cache")){var n=/([?&])_=[^&]*/;if(n.test(this.url))this.url=this.url.replace(n,"$1_="+new Date().getTime());else{var s=/\?/;this.url+=(s.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}l.prototype.clone=function(){return new l(this,{body:this._bodyInit})};function S(e){var t=new FormData;return e.trim().split("&").forEach(function(r){if(r){var n=r.split("="),s=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");t.append(decodeURIComponent(s),decodeURIComponent(o))}}),t}function F(e){var t=new i,r=e.replace(/\r?\n[\t ]+/g," ");return r.split("\r").map(function(n){return n.indexOf(`
`)===0?n.substr(1,n.length):n}).forEach(function(n){var s=n.split(":"),o=s.shift().trim();if(o){var p=s.join(":").trim();try{t.append(o,p)}catch(A){console.warn("Response "+A.message)}}}),t}B.call(l.prototype);function c(e,t){if(!(this instanceof c))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(t||(t={}),this.type="default",this.status=t.status===void 0?200:t.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=t.statusText===void 0?"":""+t.statusText,this.headers=new i(t.headers),this.url=t.url||"",this._initBody(e)}B.call(c.prototype),c.prototype.clone=function(){return new c(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new i(this.headers),url:this.url})},c.error=function(){var e=new c(null,{status:200,statusText:""});return e.ok=!1,e.status=0,e.type="error",e};var I=[301,302,303,307,308];c.redirect=function(e,t){if(I.indexOf(t)===-1)throw new RangeError("Invalid status code");return new c(null,{status:t,headers:{location:e}})},h.DOMException=a.DOMException;try{new h.DOMException}catch{h.DOMException=function(t,r){this.message=t,this.name=r;var n=Error(t);this.stack=n.stack},h.DOMException.prototype=Object.create(Error.prototype),h.DOMException.prototype.constructor=h.DOMException}function v(e,t){return new Promise(function(r,n){var s=new l(e,t);if(s.signal&&s.signal.aborted)return n(new h.DOMException("Aborted","AbortError"));var o=new XMLHttpRequest;function p(){o.abort()}o.onload=function(){var f={statusText:o.statusText,headers:F(o.getAllResponseHeaders()||"")};s.url.indexOf("file://")===0&&(o.status<200||o.status>599)?f.status=200:f.status=o.status,f.url="responseURL"in o?o.responseURL:f.headers.get("X-Request-URL");var d="response"in o?o.response:o.responseText;setTimeout(function(){r(new c(d,f))},0)},o.onerror=function(){setTimeout(function(){n(new TypeError("Network request failed"))},0)},o.ontimeout=function(){setTimeout(function(){n(new TypeError("Network request timed out"))},0)},o.onabort=function(){setTimeout(function(){n(new h.DOMException("Aborted","AbortError"))},0)};function A(f){try{return f===""&&a.location.href?a.location.href:f}catch{return f}}if(o.open(s.method,A(s.url),!0),s.credentials==="include"?o.withCredentials=!0:s.credentials==="omit"&&(o.withCredentials=!1),"responseType"in o&&(u.blob?o.responseType="blob":u.arrayBuffer&&(o.responseType="arraybuffer")),t&&typeof t.headers=="object"&&!(t.headers instanceof i||a.Headers&&t.headers instanceof a.Headers)){var _=[];Object.getOwnPropertyNames(t.headers).forEach(function(f){_.push(y(f)),o.setRequestHeader(f,b(t.headers[f]))}),s.headers.forEach(function(f,d){_.indexOf(d)===-1&&o.setRequestHeader(d,f)})}else s.headers.forEach(function(f,d){o.setRequestHeader(d,f)});s.signal&&(s.signal.addEventListener("abort",p),o.onreadystatechange=function(){o.readyState===4&&s.signal.removeEventListener("abort",p)}),o.send(typeof s._bodyInit>"u"?null:s._bodyInit)})}return v.polyfill=!0,a.fetch||(a.fetch=v,a.Headers=i,a.Request=l,a.Response=c),h.Headers=i,h.Request=l,h.Response=c,h.fetch=v,h}({})})(typeof self<"u"?self:this);
//# sourceMappingURL=cross-fetch.js.map
