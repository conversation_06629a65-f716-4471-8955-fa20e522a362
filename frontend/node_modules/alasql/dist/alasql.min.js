"use strict";//! AlaSQL v4.6.5 build: develop-f6e298b2 | © 2014-2024 <PERSON><PERSON> & <PERSON> | License: MIT
/*
@module alasql
@version 4.6.5

AlaSQL - JavaScript SQL database
© 2014-2024	<PERSON><PERSON> & <PERSON>

@license
The MIT License (MIT)

Copyright 2014-2024 <PERSON><PERSON> (<EMAIL>) & <PERSON> (<EMAIL>)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/(function(l,ht){typeof define=="function"&&define.amd?define([],ht):typeof exports=="object"?module.exports=ht():l.alasql=ht()})(this,function(){let l=function(e,n,t,r){if(n=n||[],typeof importScripts!="function"&&l.webworker){var i=l.lastid++;l.buffer[i]=t,l.webworker.postMessage({id:i,sql:e,params:n});return}return arguments.length===0?new T.Select({columns:[new T.Column({columnid:"*"})],from:[new T.ParamValue({param:0})]}):arguments.length===1&&e.constructor===Array?l.promise(e):(typeof n=="function"&&(r=t,t=n,n=[]),typeof n!="object"&&(n=[n]),typeof e=="string"&&e[0]==="#"&&typeof document=="object"?e=document.querySelector(e).textContent:typeof e=="object"&&e instanceof HTMLElement?e=e.textContent:typeof e=="function"&&(e=e.toString(),e=(/\/\*([\S\s]+)\*\//m.exec(e)||["","Function given as SQL. Plese Provide SQL string or have a /* ... */ syle comment with SQL in the function."])[1]),l.exec(e,n,t,r))};l.version="4.6.5",l.build="develop-f6e298b2",l.debug=void 0;var ht=function(){return null},ii="",Mt=function(){var e=function(Tt,b2,v2,E2){for(v2=v2||{},E2=Tt.length;E2--;v2[Tt[E2]]=b2);return v2},n=[2,13],t=[1,104],r=[1,102],i=[1,103],s=[1,6],u=[1,42],f=[1,79],h=[1,76],g=[1,94],m=[1,93],b=[1,69],c=[1,101],d=[1,85],v=[1,64],E=[1,71],O=[1,84],R=[1,66],A=[1,70],N=[1,68],w=[1,61],T1=[1,74],v1=[1,62],O1=[1,67],G1=[1,83],B1=[1,77],Y1=[1,86],k=[1,87],s2=[1,81],K1=[1,82],V1=[1,80],T2=[1,88],p2=[1,89],O2=[1,90],N2=[1,91],A2=[1,92],w2=[1,98],Z2=[1,65],X2=[1,78],g2=[1,72],D2=[1,96],Y2=[1,97],k2=[1,63],F2=[1,73],rt=[1,108],zt=[1,107],ut=[10,311,607,768],V=[10,311,315,607,768],U=[1,115],P2=[1,117],J=[1,116],y=[1,118],F=[1,119],P=[1,120],G=[1,121],Nr=[130,358,415],Or=[1,129],Cr=[1,128],Rr=[1,136],i1=[1,166],H=[1,178],B=[1,181],s1=[1,176],x=[1,184],r1=[1,188],a1=[1,162],L=[1,185],o1=[1,172],l1=[1,174],u1=[1,177],D=[1,186],j=[1,203],Y=[1,204],f1=[1,168],c1=[1,169],h1=[1,196],d1=[1,191],p1=[1,192],W=[1,197],X=[1,198],K=[1,199],Q=[1,200],z=[1,201],Z=[1,202],q=[1,205],$=[1,206],e1=[1,179],t1=[1,180],M=[1,182],n1=[1,183],b1=[1,189],E1=[1,195],_=[1,187],g1=[1,190],m1=[1,175],S1=[1,173],I=[1,194],C=[1,207],te=[2,4,5],J3=[2,480],H3=[1,210],m3=[1,215],Ut=[1,224],yt=[1,220],k4=[10,72,78,93,98,118,128,162,168,169,183,198,232,249,251,311,315,607,768],Ir=[2,4,5,10,72,76,77,78,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,183,185,187,198,244,245,285,286,287,288,289,290,291,292,311,315,425,429,607,768],a2=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],G2=[1,253],x4=[1,260],L4=[1,261],wr=[1,270],Nt=[1,275],Ot=[1,274],S3=[2,4,5,10,72,77,78,93,98,107,118,128,131,132,137,143,145,149,152,154,156,162,168,169,179,180,181,183,198,232,244,245,249,251,269,270,271,275,276,278,285,286,287,288,289,290,291,292,294,295,296,297,298,299,300,301,302,303,304,307,308,311,315,317,322,425,429,607,768],j3=[2,162],Y3=[1,286],kr=[10,74,78,311,315,510,607,768],S=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,193,198,206,208,222,223,224,225,226,227,228,229,230,231,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,307,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,349,361,373,374,375,378,379,391,394,401,405,406,407,408,409,410,411,413,414,422,423,425,429,431,438,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,519,520,521,522,607,768],xr=[2,4,5,10,53,72,89,124,146,156,189,271,272,294,311,340,343,344,401,405,406,409,411,413,414,422,423,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,522,607,768],Ft=[1,567],Lr=[1,569],D4=[1,570],W3=[2,512],X3=[1,576],Pt=[1,587],Ct=[1,590],pt=[1,591],Dr=[10,78,89,132,137,146,189,301,311,315,475,607,768],qe=[10,74,311,315,607,768],K3=[2,576],Q3=[1,609],z3=[2,4,5,156],r2=[1,647],W1=[1,619],A1=[1,653],N1=[1,654],w1=[1,627],_r=[1,638],k1=[1,625],R1=[1,633],x1=[1,626],n2=[1,634],i2=[1,636],U1=[1,628],y1=[1,629],o2=[1,648],h2=[1,645],d2=[1,646],D1=[1,622],L1=[1,624],J1=[1,616],I1=[1,617],H1=[1,618],j1=[1,620],C1=[1,621],_1=[1,623],F1=[1,630],P1=[1,631],Q1=[1,635],z1=[1,637],Z1=[1,639],q1=[1,640],$1=[1,641],e2=[1,642],t2=[1,643],l2=[1,649],u2=[1,650],X1=[1,651],c2=[1,652],Z3=[2,4,5,10,53,72,74,76,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],_4=[2,291],B2=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,231,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],Vr=[2,368],V4=[1,675],q3=[1,685],$e=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,231,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],T3=[1,701],Mr=[1,710],Ur=[1,709],yr=[2,4,5,10,72,74,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],U2=[10,72,74,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],Fr=[2,202],Pr=[1,732],Zt=[10,72,78,93,98,118,128,162,168,169,183,232,249,251,311,315,607,768],Gr=[2,163],Br=[1,735],Jr=[2,4,5,112],re=[1,748],ne=[1,767],ie=[1,747],se=[1,746],ae=[1,741],oe=[1,742],le=[1,744],ue=[1,745],fe=[1,749],ce=[1,750],he=[1,751],de=[1,752],pe=[1,753],be=[1,754],Ee=[1,755],ge=[1,756],me=[1,757],Se=[1,758],Te=[1,759],ve=[1,760],Ae=[1,761],Ne=[1,762],Oe=[1,763],Ce=[1,764],Re=[1,766],Ie=[1,768],we=[1,769],ke=[1,770],xe=[1,771],Le=[1,772],De=[1,773],_e=[1,774],Ve=[1,777],Me=[1,778],Ue=[1,779],ye=[1,780],Fe=[1,781],Pe=[1,782],Ge=[1,783],Be=[1,784],Je=[1,785],He=[1,786],je=[1,787],Ye=[1,788],M4=[74,89,189],x2=[10,74,78,154,187,230,302,311,315,348,361,373,374,378,379,607,768],We=[1,805],Hr=[10,74,78,305,311,315,607,768],f2=[1,806],jr=[1,812],Yr=[1,813],U4=[1,817],L2=[10,74,78,311,315,607,768],Gt=[2,4,5,77,131,132,137,143,145,149,152,154,156,179,180,181,244,245,269,270,271,275,276,278,285,286,287,288,289,290,291,292,294,295,296,297,298,299,300,301,302,303,304,307,308,317,322,425,429],qt=[10,72,78,93,98,107,118,128,162,168,169,183,198,232,249,251,311,315,607,768],Bt=[2,4,5,10,72,77,78,93,98,107,118,128,131,132,137,143,145,149,152,154,156,162,164,168,169,179,180,181,183,185,187,195,198,232,244,245,249,251,269,270,271,275,276,278,285,286,287,288,289,290,291,292,294,295,296,297,298,299,300,301,302,303,304,307,308,311,315,317,322,425,429,607,768],$3=[2,4,5,132,301],Wr=[1,853],Xr=[10,74,76,78,311,315,607,768],y4=[2,748],e4=[10,74,76,78,132,139,141,145,152,311,315,425,429,607,768],Kr=[2,1171],t4=[10,74,76,78,139,141,145,152,311,315,425,429,607,768],ft=[10,74,76,78,139,141,145,311,315,425,429,607,768],Qr=[10,74,78,139,141,311,315,607,768],F4=[10,78,89,132,146,189,301,311,315,475,607,768],$t=[340,343,344],zr=[2,774],Zr=[1,878],qr=[1,879],$r=[1,880],en=[1,881],e3=[1,890],t3=[1,889],Jt=[164,166,339],tn=[2,453],rn=[1,945],nn=[2,4,5,77,131,156,270,294,295,296,297,298],sn=[1,960],r4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,124,128,129,130,131,132,134,135,137,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,319,320,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],P4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],an=[2,384],on=[1,967],G4=[311,313,315],ln=[74,305],bt=[74,305,431],un=[1,974],v3=[74,431],A3=[1,987],N3=[1,986],Rt=[1,993],n4=[10,72,78,93,98,118,128,162,168,169,232,249,251,311,315,607,768],fn=[1,1020],et=[10,72,78,311,315,607,768],Xe=[1,1026],Ke=[1,1027],Qe=[1,1028],m2=[2,4,5,10,72,74,76,77,78,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,198,244,245,285,286,287,288,289,290,291,292,311,315,425,429,607,768],O3=[1,1078],C3=[1,1077],cn=[1,1091],hn=[1,1090],r3=[1,1098],It=[10,72,74,78,93,98,107,118,128,162,168,169,183,198,232,249,251,311,315,607,768],B4=[1,1130],dn=[10,78,89,146,189,311,315,475,607,768],pn=[1,1150],bn=[1,1149],En=[1,1148],n3=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],gn=[1,1164],i4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,319,320,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],mn=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,320,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],J4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,124,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,319,320,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],s4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,124,128,129,130,131,132,134,135,137,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,319,320,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],Et=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,319,325,326,327,328,329,330,331,335,336,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],Sn=[2,415],H4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,107,118,122,128,129,130,131,132,134,135,137,143,145,146,148,149,150,152,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,319,335,336,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],Tn=[2,289],j4=[2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],W2=[10,78,311,315,607,768],gt=[1,1200],vn=[10,77,78,143,145,152,181,307,311,315,425,429,607,768],i3=[10,74,78,311,313,315,469,607,768],An=[1,1211],mt=[10,72,78,118,128,162,168,169,232,249,251,311,315,607,768],a4=[10,72,74,78,93,98,118,128,162,168,169,183,198,232,249,251,311,315,607,768],q2=[2,4,5,72,76,77,78,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,185,187,244,245,285,286,287,288,289,290,291,292,425,429],wt=[2,4,5,72,74,76,77,78,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,185,187,244,245,285,286,287,288,289,290,291,292,425,429],s3=[2,1095],Nn=[2,4,5,72,74,76,77,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,185,187,244,245,285,286,287,288,289,290,291,292,425,429],o4=[1,1264],l4=[10,74,78,128,311,313,315,469,607,768],R3=[115,116,124],Y4=[2,593],W4=[1,1293],On=[76,139],Cn=[2,734],Rn=[1,1310],In=[1,1311],X4=[2,4,5,10,53,72,76,89,124,146,156,189,230,271,272,294,311,315,340,343,344,401,405,406,409,411,413,414,422,423,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,522,607,768],K4=[2,336],Q4=[1,1335],St=[1,1349],z4=[1,1351],wn=[2,496],K2=[74,78],Q2=[10,311,313,315,469,607,768],kn=[10,72,78,118,162,168,169,232,249,251,311,315,607,768],xn=[1,1368],u4=[1,1372],f4=[1,1373],c4=[1,1375],I3=[1,1376],w3=[1,1377],k3=[1,1378],x3=[1,1379],L3=[1,1380],D3=[1,1381],_3=[1,1382],h4=[10,72,74,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,232,249,251,311,315,607,768],a3=[1,1407],d4=[10,72,78,118,162,168,169,249,251,311,315,607,768],nt=[10,72,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,232,249,251,311,315,607,768],Ln=[1,1505],Dn=[1,1507],it=[2,4,5,77,143,145,152,156,181,270,294,295,296,297,298,307,425,429],V3=[1,1521],kt=[10,72,74,78,162,168,169,249,251,311,315,607,768],_n=[1,1539],Vn=[1,1541],Mn=[1,1542],Un=[1,1538],yn=[1,1537],Fn=[1,1536],Z4=[1,1543],Pn=[1,1533],Gn=[1,1534],Bn=[1,1535],Jn=[1,1561],q4=[2,4,5,10,53,72,89,124,146,156,189,271,272,294,311,315,340,343,344,401,405,406,409,411,413,414,422,423,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,522,607,768],Hn=[1,1572],$4=[1,1580],er=[1,1579],jn=[10,72,78,162,168,169,249,251,311,315,607,768],$2=[10,72,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],Yn=[2,4,5,10,72,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],Wn=[1,1640],Xn=[1,1642],Kn=[1,1639],Qn=[1,1641],p4=[187,193,373,374,375,378],tr=[2,524],rr=[1,1647],M3=[1,1666],Ht=[10,72,78,162,168,169,311,315,607,768],o3=[1,1676],l3=[1,1677],u3=[1,1678],zn=[1,1700],f3=[4,10,247,311,315,348,361,607,768],U3=[1,1748],c3=[10,72,74,78,118,162,168,169,239,249,251,311,315,607,768],Zn=[2,4,5,77],qn=[1,1844],nr=[1,1856],ir=[1,1875],$n=[10,72,78,162,168,169,311,315,420,607,768],sr=[10,74,78,230,311,315,607,768],b4={trace:function(){},yy:{},symbols_:{error:2,Literal:3,LITERAL:4,BRALITERAL:5,NonReserved:6,LiteralWithSpaces:7,main:8,Statements:9,EOF:10,Statements_group0:11,AStatement:12,ExplainStatement:13,EXPLAIN:14,QUERY:15,PLAN:16,Statement:17,AlterTable:18,AttachDatabase:19,Call:20,CreateDatabase:21,CreateIndex:22,CreateGraph:23,CreateTable:24,CreateView:25,CreateEdge:26,CreateVertex:27,Declare:28,Delete:29,DetachDatabase:30,DropDatabase:31,DropIndex:32,DropTable:33,DropView:34,If:35,Insert:36,Merge:37,Reindex:38,RenameTable:39,Select:40,ShowCreateTable:41,ShowColumns:42,ShowDatabases:43,ShowIndex:44,ShowTables:45,TruncateTable:46,WithSelect:47,CreateTrigger:48,DropTrigger:49,BeginTransaction:50,CommitTransaction:51,RollbackTransaction:52,EndTransaction:53,UseDatabase:54,Update:55,JavaScript:56,Source:57,Assert:58,While:59,Continue:60,Break:61,BeginEnd:62,Print:63,Require:64,SetVariable:65,ExpressionStatement:66,AddRule:67,Query:68,Echo:69,CreateFunction:70,CreateAggregate:71,WITH:72,WithTablesList:73,COMMA:74,WithTable:75,AS:76,LPAR:77,RPAR:78,SelectClause:79,Select_option0:80,IntoClause:81,FromClause:82,Select_option1:83,WhereClause:84,GroupClause:85,OrderClause:86,LimitClause:87,UnionClause:88,SEARCH:89,Select_repetition0:90,Select_option2:91,PivotClause:92,PIVOT:93,Expression:94,FOR:95,PivotClause_option0:96,PivotClause_option1:97,UNPIVOT:98,IN:99,ColumnsList:100,PivotClause_option2:101,PivotClause2:102,AsList:103,AsLiteral:104,AsPart:105,RemoveClause:106,REMOVE:107,RemoveClause_option0:108,RemoveColumnsList:109,RemoveColumn:110,Column:111,LIKE:112,StringValue:113,ArrowDot:114,ARROW:115,DOT:116,SearchSelector:117,ORDER:118,BY:119,OrderExpressionsList:120,SearchSelector_option0:121,DOTDOT:122,CARET:123,EQ:124,SearchSelector_repetition_plus0:125,SearchSelector_repetition_plus1:126,SearchSelector_option1:127,WHERE:128,OF:129,CLASS:130,NUMBER:131,STRING:132,SLASH:133,VERTEX:134,EDGE:135,EXCLAMATION:136,SHARP:137,MODULO:138,GT:139,LT:140,GTGT:141,LTLT:142,DOLLAR:143,Json:144,AT:145,SET:146,SetColumnsList:147,TO:148,VALUE:149,ROW:150,ExprList:151,COLON:152,PlusStar:153,NOT:154,SearchSelector_repetition2:155,IF:156,SearchSelector_repetition3:157,Aggregator:158,SearchSelector_repetition4:159,SearchSelector_group0:160,SearchSelector_repetition5:161,UNION:162,SearchSelectorList:163,ALL:164,SearchSelector_repetition6:165,ANY:166,SearchSelector_repetition7:167,INTERSECT:168,EXCEPT:169,AND:170,OR:171,PATH:172,RETURN:173,ResultColumns:174,REPEAT:175,SearchSelector_repetition8:176,SearchSelectorList_repetition0:177,SearchSelectorList_repetition1:178,PLUS:179,STAR:180,QUESTION:181,SearchFrom:182,FROM:183,SelectModifier:184,DISTINCT:185,TopClause:186,UNIQUE:187,SelectClause_option0:188,SELECT:189,COLUMN:190,MATRIX:191,TEXTSTRING:192,INDEX:193,RECORDSET:194,TOP:195,NumValue:196,TopClause_option0:197,INTO:198,Table:199,FuncValue:200,ParamValue:201,VarValue:202,FromTablesList:203,JoinTablesList:204,ApplyClause:205,CROSS:206,APPLY:207,OUTER:208,FromTable:209,FromTable_option0:210,FromTable_option1:211,INDEXED:212,INSERTED:213,FromString:214,JoinTable:215,JoinMode:216,JoinTableAs:217,OnClause:218,JoinTableAs_option0:219,JoinTableAs_option1:220,JoinModeMode:221,NATURAL:222,JOIN:223,INNER:224,LEFT:225,RIGHT:226,FULL:227,SEMI:228,ANTI:229,ON:230,USING:231,GROUP:232,GroupExpressionsList:233,HavingClause:234,GroupExpression:235,GROUPING:236,ROLLUP:237,CUBE:238,HAVING:239,CORRESPONDING:240,OrderExpression:241,NullsOrder:242,NULLS:243,FIRST:244,LAST:245,DIRECTION:246,COLLATE:247,NOCASE:248,LIMIT:249,OffsetClause:250,OFFSET:251,LimitClause_option0:252,FETCH:253,LimitClause_option1:254,LimitClause_option2:255,LimitClause_option3:256,ResultColumn:257,Star:258,AggrValue:259,Op:260,LogicValue:261,NullValue:262,ExistsValue:263,CaseValue:264,CastClause:265,ArrayValue:266,NewClause:267,Expression_group0:268,CURRENT_TIMESTAMP:269,CURRENT_DATE:270,JAVASCRIPT:271,CREATE:272,FUNCTION:273,AGGREGATE:274,NEW:275,CAST:276,ColumnType:277,CONVERT:278,PrimitiveValue:279,OverClause:280,OVER:281,OverPartitionClause:282,OverOrderByClause:283,PARTITION:284,SUM:285,TOTAL:286,COUNT:287,MIN:288,MAX:289,AVG:290,AGGR:291,ARRAY:292,FuncValue_option0:293,REPLACE:294,DATEADD:295,DATEDIFF:296,TIMESTAMPDIFF:297,INTERVAL:298,TRUE:299,FALSE:300,NSTRING:301,NULL:302,EXISTS:303,ARRAYLBRA:304,RBRA:305,ParamValue_group0:306,BRAQUESTION:307,CASE:308,WhensList:309,ElseClause:310,END:311,When:312,WHEN:313,THEN:314,ELSE:315,REGEXP:316,TILDA:317,GLOB:318,ESCAPE:319,NOT_LIKE:320,BARBAR:321,MINUS:322,AMPERSAND:323,BAR:324,GE:325,LE:326,EQEQ:327,EQEQEQ:328,NE:329,NEEQEQ:330,NEEQEQEQ:331,CondOp:332,AllSome:333,ColFunc:334,BETWEEN:335,NOT_BETWEEN:336,IS:337,DOUBLECOLON:338,SOME:339,UPDATE:340,SetColumn:341,SetColumn_group0:342,DELETE:343,INSERT:344,Into:345,Values:346,ValuesListsList:347,DEFAULT:348,VALUES:349,ValuesList:350,Value:351,DateValue:352,TemporaryClause:353,TableClass:354,IfNotExists:355,CreateTableDefClause:356,CreateTableOptionsClause:357,TABLE:358,CreateTableOptions:359,CreateTableOption:360,IDENTITY:361,TEMP:362,ColumnDefsList:363,ConstraintsList:364,Constraint:365,ConstraintName:366,PrimaryKey:367,ForeignKey:368,UniqueKey:369,IndexKey:370,Check:371,CONSTRAINT:372,CHECK:373,PRIMARY:374,KEY:375,PrimaryKey_option0:376,ColsList:377,FOREIGN:378,REFERENCES:379,ForeignKey_option0:380,OnForeignKeyClause:381,ParColsList:382,OnDeleteClause:383,OnUpdateClause:384,NO:385,ACTION:386,UniqueKey_option0:387,UniqueKey_option1:388,ColumnDef:389,ColumnConstraintsClause:390,ColumnConstraints:391,SingularColumnType:392,NumberMax:393,ENUM:394,MAXNUM:395,ColumnConstraintsList:396,ColumnConstraint:397,ParLiteral:398,ColumnConstraint_option0:399,ColumnConstraint_option1:400,DROP:401,DropTable_group0:402,IfExists:403,TablesList:404,ALTER:405,RENAME:406,ADD:407,MODIFY:408,ATTACH:409,DATABASE:410,DETACH:411,AsClause:412,USE:413,SHOW:414,VIEW:415,CreateView_option0:416,CreateView_option1:417,SubqueryRestriction:418,READ:419,ONLY:420,OPTION:421,SOURCE:422,ASSERT:423,JsonObject:424,ATLBRA:425,JsonArray:426,JsonValue:427,JsonPrimitiveValue:428,LCUR:429,JsonPropertiesList:430,RCUR:431,JsonElementsList:432,JsonProperty:433,OnOff:434,SetPropsList:435,AtDollar:436,SetProp:437,OFF:438,COMMIT:439,TRANSACTION:440,ROLLBACK:441,BEGIN:442,ElseStatement:443,WHILE:444,CONTINUE:445,BREAK:446,PRINT:447,REQUIRE:448,StringValuesList:449,PluginsList:450,Plugin:451,ECHO:452,DECLARE:453,DeclaresList:454,DeclareItem:455,TRUNCATE:456,MERGE:457,MergeInto:458,MergeUsing:459,MergeOn:460,MergeMatchedList:461,OutputClause:462,MergeMatched:463,MergeNotMatched:464,MATCHED:465,MergeMatchedAction:466,MergeNotMatchedAction:467,TARGET:468,OUTPUT:469,CreateVertex_option0:470,CreateVertex_option1:471,CreateVertex_option2:472,CreateVertexSet:473,SharpValue:474,CONTENT:475,CreateEdge_option0:476,GRAPH:477,GraphList:478,GraphVertexEdge:479,GraphElement:480,GraphVertexEdge_option0:481,GraphVertexEdge_option1:482,GraphElementVar:483,GraphVertexEdge_option2:484,GraphVertexEdge_option3:485,GraphVertexEdge_option4:486,GraphVar:487,GraphAsClause:488,GraphAtClause:489,GraphElement2:490,GraphElement2_option0:491,GraphElement2_option1:492,GraphElement2_option2:493,GraphElement2_option3:494,GraphElement_option0:495,GraphElement_option1:496,GraphElement_option2:497,SharpLiteral:498,GraphElement_option3:499,GraphElement_option4:500,GraphElement_option5:501,ColonLiteral:502,DeleteVertex:503,DeleteVertex_option0:504,DeleteEdge:505,DeleteEdge_option0:506,DeleteEdge_option1:507,DeleteEdge_option2:508,Term:509,COLONDASH:510,TermsList:511,QUESTIONDASH:512,CALL:513,TRIGGER:514,BeforeAfter:515,InsertDeleteUpdate:516,CreateTrigger_option0:517,CreateTrigger_option1:518,BEFORE:519,AFTER:520,INSTEAD:521,REINDEX:522,A:523,ABSENT:524,ABSOLUTE:525,ACCORDING:526,ADA:527,ADMIN:528,ALWAYS:529,ASC:530,ASSERTION:531,ASSIGNMENT:532,ATTRIBUTE:533,ATTRIBUTES:534,BASE64:535,BERNOULLI:536,BLOCKED:537,BOM:538,BREADTH:539,C:540,CASCADE:541,CATALOG:542,CATALOG_NAME:543,CHAIN:544,CHARACTERISTICS:545,CHARACTERS:546,CHARACTER_SET_CATALOG:547,CHARACTER_SET_NAME:548,CHARACTER_SET_SCHEMA:549,CLASS_ORIGIN:550,COBOL:551,COLLATION:552,COLLATION_CATALOG:553,COLLATION_NAME:554,COLLATION_SCHEMA:555,COLUMNS:556,COLUMN_NAME:557,COMMAND_FUNCTION:558,COMMAND_FUNCTION_CODE:559,COMMITTED:560,CONDITION_NUMBER:561,CONNECTION:562,CONNECTION_NAME:563,CONSTRAINTS:564,CONSTRAINT_CATALOG:565,CONSTRAINT_NAME:566,CONSTRAINT_SCHEMA:567,CONSTRUCTOR:568,CONTROL:569,CURSOR_NAME:570,DATA:571,DATETIME_INTERVAL_CODE:572,DATETIME_INTERVAL_PRECISION:573,DB:574,DEFAULTS:575,DEFERRABLE:576,DEFERRED:577,DEFINED:578,DEFINER:579,DEGREE:580,DEPTH:581,DERIVED:582,DESC:583,DESCRIPTOR:584,DIAGNOSTICS:585,DISPATCH:586,DOCUMENT:587,DOMAIN:588,DYNAMIC_FUNCTION:589,DYNAMIC_FUNCTION_CODE:590,EMPTY:591,ENCODING:592,ENFORCED:593,EXCLUDE:594,EXCLUDING:595,EXPRESSION:596,FILE:597,FINAL:598,FLAG:599,FOLLOWING:600,FORTRAN:601,FOUND:602,FS:603,G:604,GENERAL:605,GENERATED:606,GO:607,GOTO:608,GRANTED:609,HEX:610,HIERARCHY:611,ID:612,IGNORE:613,IMMEDIATE:614,IMMEDIATELY:615,IMPLEMENTATION:616,INCLUDING:617,INCREMENT:618,INDENT:619,INITIALLY:620,INPUT:621,INSTANCE:622,INSTANTIABLE:623,INTEGRITY:624,INVOKER:625,ISOLATION:626,K:627,KEY_MEMBER:628,KEY_TYPE:629,LENGTH:630,LEVEL:631,LIBRARY:632,LINK:633,LOCATION:634,LOCATOR:635,M:636,MAP:637,MAPPING:638,MAXVALUE:639,MESSAGE_LENGTH:640,MESSAGE_OCTET_LENGTH:641,MESSAGE_TEXT:642,MINVALUE:643,MORE:644,MUMPS:645,NAME:646,NAMES:647,NAMESPACE:648,NESTING:649,NEXT:650,NFC:651,NFD:652,NFKC:653,NFKD:654,NIL:655,NORMALIZED:656,NULLABLE:657,OBJECT:658,OCTETS:659,OPTIONS:660,ORDERING:661,ORDINALITY:662,OTHERS:663,OVERRIDING:664,P:665,PAD:666,PARAMETER_MODE:667,PARAMETER_NAME:668,PARAMETER_ORDINAL_POSITION:669,PARAMETER_SPECIFIC_CATALOG:670,PARAMETER_SPECIFIC_NAME:671,PARAMETER_SPECIFIC_SCHEMA:672,PARTIAL:673,PASCAL:674,PASSING:675,PASSTHROUGH:676,PERMISSION:677,PLACING:678,PLI:679,PRECEDING:680,PRESERVE:681,PRIOR:682,PRIVILEGES:683,PUBLIC:684,RECOVERY:685,RELATIVE:686,REPEATABLE:687,REQUIRING:688,RESPECT:689,RESTART:690,RESTORE:691,RESTRICT:692,RETURNED_CARDINALITY:693,RETURNED_LENGTH:694,RETURNED_OCTET_LENGTH:695,RETURNED_SQLSTATE:696,RETURNING:697,ROLE:698,ROUTINE:699,ROUTINE_CATALOG:700,ROUTINE_NAME:701,ROUTINE_SCHEMA:702,ROW_COUNT:703,SCALE:704,SCHEMA:705,SCHEMA_NAME:706,SCOPE_CATALOG:707,SCOPE_NAME:708,SCOPE_SCHEMA:709,SECTION:710,SECURITY:711,SELECTIVE:712,SELF:713,SEQUENCE:714,SERIALIZABLE:715,SERVER:716,SERVER_NAME:717,SESSION:718,SETS:719,SIMPLE:720,SIZE:721,SPACE:722,SPECIFIC_NAME:723,STANDALONE:724,STATE:725,STATEMENT:726,STRIP:727,STRUCTURE:728,STYLE:729,SUBCLASS_ORIGIN:730,T:731,TABLE_NAME:732,TEMPORARY:733,TIES:734,TOKEN:735,TOP_LEVEL_COUNT:736,TRANSACTIONS_COMMITTED:737,TRANSACTIONS_ROLLED_BACK:738,TRANSACTION_ACTIVE:739,TRANSFORM:740,TRANSFORMS:741,TRIGGER_CATALOG:742,TRIGGER_NAME:743,TRIGGER_SCHEMA:744,TYPE:745,UNBOUNDED:746,UNCOMMITTED:747,UNDER:748,UNLINK:749,UNNAMED:750,UNTYPED:751,URI:752,USAGE:753,USER_DEFINED_TYPE_CATALOG:754,USER_DEFINED_TYPE_CODE:755,USER_DEFINED_TYPE_NAME:756,USER_DEFINED_TYPE_SCHEMA:757,VALID:758,VERSION:759,WHITESPACE:760,WORK:761,WRAPPER:762,WRITE:763,XMLDECLARATION:764,XMLSCHEMA:765,YES:766,ZONE:767,SEMICOLON:768,PERCENT:769,ROWS:770,FuncValue_option0_group0:771,$accept:0,$end:1},terminals_:{2:"error",4:"LITERAL",5:"BRALITERAL",10:"EOF",14:"EXPLAIN",15:"QUERY",16:"PLAN",53:"EndTransaction",72:"WITH",74:"COMMA",76:"AS",77:"LPAR",78:"RPAR",89:"SEARCH",93:"PIVOT",95:"FOR",98:"UNPIVOT",99:"IN",107:"REMOVE",112:"LIKE",115:"ARROW",116:"DOT",118:"ORDER",119:"BY",122:"DOTDOT",123:"CARET",124:"EQ",128:"WHERE",129:"OF",130:"CLASS",131:"NUMBER",132:"STRING",133:"SLASH",134:"VERTEX",135:"EDGE",136:"EXCLAMATION",137:"SHARP",138:"MODULO",139:"GT",140:"LT",141:"GTGT",142:"LTLT",143:"DOLLAR",145:"AT",146:"SET",148:"TO",149:"VALUE",150:"ROW",152:"COLON",154:"NOT",156:"IF",162:"UNION",164:"ALL",166:"ANY",168:"INTERSECT",169:"EXCEPT",170:"AND",171:"OR",172:"PATH",173:"RETURN",175:"REPEAT",179:"PLUS",180:"STAR",181:"QUESTION",183:"FROM",185:"DISTINCT",187:"UNIQUE",189:"SELECT",190:"COLUMN",191:"MATRIX",192:"TEXTSTRING",193:"INDEX",194:"RECORDSET",195:"TOP",198:"INTO",206:"CROSS",207:"APPLY",208:"OUTER",212:"INDEXED",213:"INSERTED",222:"NATURAL",223:"JOIN",224:"INNER",225:"LEFT",226:"RIGHT",227:"FULL",228:"SEMI",229:"ANTI",230:"ON",231:"USING",232:"GROUP",236:"GROUPING",237:"ROLLUP",238:"CUBE",239:"HAVING",240:"CORRESPONDING",243:"NULLS",244:"FIRST",245:"LAST",246:"DIRECTION",247:"COLLATE",248:"NOCASE",249:"LIMIT",251:"OFFSET",253:"FETCH",269:"CURRENT_TIMESTAMP",270:"CURRENT_DATE",271:"JAVASCRIPT",272:"CREATE",273:"FUNCTION",274:"AGGREGATE",275:"NEW",276:"CAST",278:"CONVERT",281:"OVER",284:"PARTITION",285:"SUM",286:"TOTAL",287:"COUNT",288:"MIN",289:"MAX",290:"AVG",291:"AGGR",292:"ARRAY",294:"REPLACE",295:"DATEADD",296:"DATEDIFF",297:"TIMESTAMPDIFF",298:"INTERVAL",299:"TRUE",300:"FALSE",301:"NSTRING",302:"NULL",303:"EXISTS",304:"ARRAYLBRA",305:"RBRA",307:"BRAQUESTION",308:"CASE",311:"END",313:"WHEN",314:"THEN",315:"ELSE",316:"REGEXP",317:"TILDA",318:"GLOB",319:"ESCAPE",320:"NOT_LIKE",321:"BARBAR",322:"MINUS",323:"AMPERSAND",324:"BAR",325:"GE",326:"LE",327:"EQEQ",328:"EQEQEQ",329:"NE",330:"NEEQEQ",331:"NEEQEQEQ",335:"BETWEEN",336:"NOT_BETWEEN",337:"IS",338:"DOUBLECOLON",339:"SOME",340:"UPDATE",343:"DELETE",344:"INSERT",348:"DEFAULT",349:"VALUES",352:"DateValue",358:"TABLE",361:"IDENTITY",362:"TEMP",372:"CONSTRAINT",373:"CHECK",374:"PRIMARY",375:"KEY",378:"FOREIGN",379:"REFERENCES",385:"NO",386:"ACTION",391:"ColumnConstraints",394:"ENUM",395:"MAXNUM",401:"DROP",405:"ALTER",406:"RENAME",407:"ADD",408:"MODIFY",409:"ATTACH",410:"DATABASE",411:"DETACH",413:"USE",414:"SHOW",415:"VIEW",419:"READ",420:"ONLY",421:"OPTION",422:"SOURCE",423:"ASSERT",425:"ATLBRA",429:"LCUR",431:"RCUR",438:"OFF",439:"COMMIT",440:"TRANSACTION",441:"ROLLBACK",442:"BEGIN",444:"WHILE",445:"CONTINUE",446:"BREAK",447:"PRINT",448:"REQUIRE",452:"ECHO",453:"DECLARE",456:"TRUNCATE",457:"MERGE",465:"MATCHED",468:"TARGET",469:"OUTPUT",475:"CONTENT",477:"GRAPH",510:"COLONDASH",512:"QUESTIONDASH",513:"CALL",514:"TRIGGER",519:"BEFORE",520:"AFTER",521:"INSTEAD",522:"REINDEX",523:"A",524:"ABSENT",525:"ABSOLUTE",526:"ACCORDING",527:"ADA",528:"ADMIN",529:"ALWAYS",530:"ASC",531:"ASSERTION",532:"ASSIGNMENT",533:"ATTRIBUTE",534:"ATTRIBUTES",535:"BASE64",536:"BERNOULLI",537:"BLOCKED",538:"BOM",539:"BREADTH",540:"C",541:"CASCADE",542:"CATALOG",543:"CATALOG_NAME",544:"CHAIN",545:"CHARACTERISTICS",546:"CHARACTERS",547:"CHARACTER_SET_CATALOG",548:"CHARACTER_SET_NAME",549:"CHARACTER_SET_SCHEMA",550:"CLASS_ORIGIN",551:"COBOL",552:"COLLATION",553:"COLLATION_CATALOG",554:"COLLATION_NAME",555:"COLLATION_SCHEMA",556:"COLUMNS",557:"COLUMN_NAME",558:"COMMAND_FUNCTION",559:"COMMAND_FUNCTION_CODE",560:"COMMITTED",561:"CONDITION_NUMBER",562:"CONNECTION",563:"CONNECTION_NAME",564:"CONSTRAINTS",565:"CONSTRAINT_CATALOG",566:"CONSTRAINT_NAME",567:"CONSTRAINT_SCHEMA",568:"CONSTRUCTOR",569:"CONTROL",570:"CURSOR_NAME",571:"DATA",572:"DATETIME_INTERVAL_CODE",573:"DATETIME_INTERVAL_PRECISION",574:"DB",575:"DEFAULTS",576:"DEFERRABLE",577:"DEFERRED",578:"DEFINED",579:"DEFINER",580:"DEGREE",581:"DEPTH",582:"DERIVED",583:"DESC",584:"DESCRIPTOR",585:"DIAGNOSTICS",586:"DISPATCH",587:"DOCUMENT",588:"DOMAIN",589:"DYNAMIC_FUNCTION",590:"DYNAMIC_FUNCTION_CODE",591:"EMPTY",592:"ENCODING",593:"ENFORCED",594:"EXCLUDE",595:"EXCLUDING",596:"EXPRESSION",597:"FILE",598:"FINAL",599:"FLAG",600:"FOLLOWING",601:"FORTRAN",602:"FOUND",603:"FS",604:"G",605:"GENERAL",606:"GENERATED",607:"GO",608:"GOTO",609:"GRANTED",610:"HEX",611:"HIERARCHY",612:"ID",613:"IGNORE",614:"IMMEDIATE",615:"IMMEDIATELY",616:"IMPLEMENTATION",617:"INCLUDING",618:"INCREMENT",619:"INDENT",620:"INITIALLY",621:"INPUT",622:"INSTANCE",623:"INSTANTIABLE",624:"INTEGRITY",625:"INVOKER",626:"ISOLATION",627:"K",628:"KEY_MEMBER",629:"KEY_TYPE",630:"LENGTH",631:"LEVEL",632:"LIBRARY",633:"LINK",634:"LOCATION",635:"LOCATOR",636:"M",637:"MAP",638:"MAPPING",639:"MAXVALUE",640:"MESSAGE_LENGTH",641:"MESSAGE_OCTET_LENGTH",642:"MESSAGE_TEXT",643:"MINVALUE",644:"MORE",645:"MUMPS",646:"NAME",647:"NAMES",648:"NAMESPACE",649:"NESTING",650:"NEXT",651:"NFC",652:"NFD",653:"NFKC",654:"NFKD",655:"NIL",656:"NORMALIZED",657:"NULLABLE",658:"OBJECT",659:"OCTETS",660:"OPTIONS",661:"ORDERING",662:"ORDINALITY",663:"OTHERS",664:"OVERRIDING",665:"P",666:"PAD",667:"PARAMETER_MODE",668:"PARAMETER_NAME",669:"PARAMETER_ORDINAL_POSITION",670:"PARAMETER_SPECIFIC_CATALOG",671:"PARAMETER_SPECIFIC_NAME",672:"PARAMETER_SPECIFIC_SCHEMA",673:"PARTIAL",674:"PASCAL",675:"PASSING",676:"PASSTHROUGH",677:"PERMISSION",678:"PLACING",679:"PLI",680:"PRECEDING",681:"PRESERVE",682:"PRIOR",683:"PRIVILEGES",684:"PUBLIC",685:"RECOVERY",686:"RELATIVE",687:"REPEATABLE",688:"REQUIRING",689:"RESPECT",690:"RESTART",691:"RESTORE",692:"RESTRICT",693:"RETURNED_CARDINALITY",694:"RETURNED_LENGTH",695:"RETURNED_OCTET_LENGTH",696:"RETURNED_SQLSTATE",697:"RETURNING",698:"ROLE",699:"ROUTINE",700:"ROUTINE_CATALOG",701:"ROUTINE_NAME",702:"ROUTINE_SCHEMA",703:"ROW_COUNT",704:"SCALE",705:"SCHEMA",706:"SCHEMA_NAME",707:"SCOPE_CATALOG",708:"SCOPE_NAME",709:"SCOPE_SCHEMA",710:"SECTION",711:"SECURITY",712:"SELECTIVE",713:"SELF",714:"SEQUENCE",715:"SERIALIZABLE",716:"SERVER",717:"SERVER_NAME",718:"SESSION",719:"SETS",720:"SIMPLE",721:"SIZE",722:"SPACE",723:"SPECIFIC_NAME",724:"STANDALONE",725:"STATE",726:"STATEMENT",727:"STRIP",728:"STRUCTURE",729:"STYLE",730:"SUBCLASS_ORIGIN",731:"T",732:"TABLE_NAME",733:"TEMPORARY",734:"TIES",735:"TOKEN",736:"TOP_LEVEL_COUNT",737:"TRANSACTIONS_COMMITTED",738:"TRANSACTIONS_ROLLED_BACK",739:"TRANSACTION_ACTIVE",740:"TRANSFORM",741:"TRANSFORMS",742:"TRIGGER_CATALOG",743:"TRIGGER_NAME",744:"TRIGGER_SCHEMA",745:"TYPE",746:"UNBOUNDED",747:"UNCOMMITTED",748:"UNDER",749:"UNLINK",750:"UNNAMED",751:"UNTYPED",752:"URI",753:"USAGE",754:"USER_DEFINED_TYPE_CATALOG",755:"USER_DEFINED_TYPE_CODE",756:"USER_DEFINED_TYPE_NAME",757:"USER_DEFINED_TYPE_SCHEMA",758:"VALID",759:"VERSION",760:"WHITESPACE",761:"WORK",762:"WRAPPER",763:"WRITE",764:"XMLDECLARATION",765:"XMLSCHEMA",766:"YES",767:"ZONE",768:"SEMICOLON",769:"PERCENT",770:"ROWS"},productions_:[0,[3,1],[3,1],[3,2],[7,1],[7,2],[8,2],[9,3],[9,1],[9,1],[13,2],[13,4],[12,1],[17,0],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[47,3],[73,3],[73,1],[75,5],[40,10],[40,4],[92,8],[92,11],[102,4],[104,2],[104,1],[103,3],[103,1],[105,1],[105,3],[106,3],[109,3],[109,1],[110,1],[110,2],[114,1],[114,1],[117,1],[117,5],[117,5],[117,1],[117,2],[117,1],[117,2],[117,2],[117,3],[117,4],[117,4],[117,4],[117,4],[117,4],[117,1],[117,1],[117,1],[117,1],[117,1],[117,1],[117,2],[117,2],[117,2],[117,1],[117,1],[117,1],[117,1],[117,1],[117,1],[117,2],[117,3],[117,4],[117,3],[117,1],[117,4],[117,2],[117,2],[117,4],[117,4],[117,4],[117,4],[117,4],[117,5],[117,4],[117,4],[117,4],[117,4],[117,4],[117,4],[117,4],[117,4],[117,6],[163,3],[163,1],[153,1],[153,1],[153,1],[182,2],[79,4],[79,4],[79,4],[79,3],[184,1],[184,2],[184,2],[184,2],[184,2],[184,2],[184,2],[184,2],[186,3],[186,4],[186,0],[81,0],[81,2],[81,2],[81,2],[81,2],[81,2],[82,2],[82,3],[82,5],[82,0],[205,6],[205,7],[205,6],[205,7],[203,1],[203,3],[209,4],[209,5],[209,3],[209,3],[209,2],[209,3],[209,1],[209,3],[209,2],[209,3],[209,1],[209,1],[209,2],[209,3],[209,1],[209,1],[209,2],[209,3],[209,1],[209,2],[209,3],[214,1],[199,3],[199,1],[204,2],[204,2],[204,1],[204,1],[215,3],[217,1],[217,2],[217,3],[217,3],[217,2],[217,3],[217,4],[217,5],[217,1],[217,2],[217,3],[217,1],[217,2],[217,3],[216,1],[216,2],[221,1],[221,2],[221,2],[221,3],[221,2],[221,3],[221,2],[221,3],[221,2],[221,2],[221,2],[218,2],[218,2],[218,4],[218,0],[84,0],[84,2],[85,0],[85,4],[233,1],[233,3],[235,5],[235,4],[235,4],[235,1],[234,0],[234,2],[88,0],[88,2],[88,3],[88,2],[88,2],[88,3],[88,4],[88,3],[88,3],[86,0],[86,3],[120,1],[120,3],[242,2],[242,2],[241,1],[241,2],[241,3],[241,3],[241,4],[87,0],[87,3],[87,8],[250,0],[250,2],[174,3],[174,1],[257,3],[257,2],[257,3],[257,2],[257,3],[257,2],[257,1],[258,5],[258,3],[258,1],[111,5],[111,3],[111,3],[111,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,1],[94,3],[94,3],[94,3],[94,1],[94,1],[94,1],[56,1],[70,5],[71,5],[267,2],[267,2],[265,6],[265,8],[265,6],[265,8],[279,1],[279,1],[279,1],[279,1],[279,1],[279,1],[279,1],[279,1],[259,5],[259,6],[259,6],[280,0],[280,4],[280,4],[280,5],[282,3],[283,3],[158,1],[158,1],[158,1],[158,1],[158,1],[158,1],[158,1],[158,1],[158,1],[158,1],[200,5],[200,3],[200,4],[200,4],[200,3],[200,8],[200,8],[200,8],[200,8],[200,8],[200,3],[151,1],[151,3],[196,1],[261,1],[261,1],[113,1],[113,1],[262,1],[202,2],[263,4],[266,3],[201,2],[201,2],[201,1],[201,1],[264,5],[264,4],[309,2],[309,1],[312,4],[310,2],[310,0],[260,3],[260,3],[260,3],[260,3],[260,5],[260,3],[260,5],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,5],[260,3],[260,3],[260,3],[260,5],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,3],[260,6],[260,6],[260,3],[260,3],[260,2],[260,2],[260,2],[260,2],[260,2],[260,3],[260,5],[260,6],[260,5],[260,6],[260,4],[260,5],[260,3],[260,4],[260,3],[260,4],[260,3],[260,3],[260,3],[260,3],[260,3],[334,1],[334,1],[334,4],[332,1],[332,1],[332,1],[332,1],[332,1],[332,1],[333,1],[333,1],[333,1],[55,6],[55,4],[147,1],[147,3],[341,3],[341,4],[29,5],[29,3],[36,5],[36,4],[36,7],[36,6],[36,5],[36,4],[36,5],[36,8],[36,7],[36,4],[36,6],[36,7],[346,1],[346,1],[345,0],[345,1],[347,3],[347,1],[347,1],[347,5],[347,3],[347,3],[350,1],[350,3],[351,1],[351,1],[351,1],[351,1],[351,1],[351,1],[100,1],[100,3],[24,9],[24,5],[354,1],[354,1],[357,0],[357,1],[359,2],[359,1],[360,1],[360,3],[360,3],[360,3],[353,0],[353,1],[355,0],[355,3],[356,3],[356,1],[356,2],[364,1],[364,3],[365,2],[365,2],[365,2],[365,2],[365,2],[366,0],[366,2],[371,4],[367,6],[368,9],[382,3],[381,0],[381,2],[383,4],[384,4],[369,6],[370,5],[370,5],[377,1],[377,1],[377,3],[377,3],[363,1],[363,3],[389,3],[389,2],[389,1],[392,6],[392,4],[392,1],[392,4],[277,2],[277,1],[393,1],[393,1],[390,0],[390,1],[396,2],[396,1],[398,3],[397,2],[397,5],[397,3],[397,6],[397,1],[397,2],[397,4],[397,2],[397,1],[397,2],[397,1],[397,1],[397,3],[397,5],[33,4],[404,3],[404,1],[403,0],[403,2],[18,6],[18,6],[18,6],[18,8],[18,6],[39,5],[19,4],[19,7],[19,6],[19,9],[30,3],[21,4],[21,6],[21,9],[21,6],[412,0],[412,2],[54,3],[54,2],[31,4],[31,5],[31,5],[22,8],[22,9],[32,3],[43,2],[43,4],[43,3],[43,5],[45,2],[45,4],[45,4],[45,6],[42,4],[42,6],[44,4],[44,6],[41,4],[41,6],[25,11],[25,8],[418,3],[418,3],[418,5],[34,4],[66,2],[57,2],[58,2],[58,2],[58,4],[144,4],[144,2],[144,2],[144,2],[144,2],[144,1],[144,2],[144,2],[427,1],[427,1],[428,1],[428,1],[428,1],[428,1],[428,1],[428,1],[428,1],[428,3],[424,3],[424,4],[424,2],[426,2],[426,3],[426,1],[430,3],[430,1],[433,3],[433,3],[433,3],[432,3],[432,1],[65,4],[65,3],[65,4],[65,5],[65,5],[65,6],[436,1],[436,1],[435,3],[435,2],[437,1],[437,1],[437,3],[434,1],[434,1],[51,2],[52,2],[50,2],[35,4],[35,3],[443,2],[59,3],[60,1],[61,1],[62,3],[63,2],[63,2],[64,2],[64,2],[451,1],[451,1],[69,2],[449,3],[449,1],[450,3],[450,1],[28,2],[454,1],[454,3],[455,3],[455,4],[455,5],[455,6],[46,3],[37,6],[458,1],[458,2],[459,2],[459,4],[460,2],[461,2],[461,2],[461,1],[461,1],[463,4],[463,6],[466,1],[466,3],[464,5],[464,7],[464,7],[464,9],[464,7],[464,9],[467,3],[467,6],[467,3],[467,6],[462,0],[462,2],[462,5],[462,4],[462,7],[27,6],[474,2],[473,0],[473,2],[473,2],[473,1],[26,8],[23,3],[23,4],[478,3],[478,1],[479,3],[479,7],[479,6],[479,3],[479,4],[483,1],[483,1],[487,2],[488,3],[489,2],[490,4],[480,4],[480,3],[480,2],[480,1],[502,2],[498,2],[498,2],[503,4],[505,6],[67,3],[67,2],[511,3],[511,1],[509,1],[509,4],[68,2],[20,2],[48,9],[48,8],[48,9],[515,0],[515,1],[515,1],[515,1],[515,2],[516,1],[516,1],[516,1],[49,3],[38,2],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[11,1],[11,1],[80,0],[80,1],[83,0],[83,1],[90,0],[90,2],[91,0],[91,1],[96,0],[96,1],[97,0],[97,1],[101,0],[101,1],[108,0],[108,1],[121,0],[121,1],[125,1],[125,2],[126,1],[126,2],[127,0],[127,1],[155,0],[155,2],[157,0],[157,2],[159,0],[159,2],[160,1],[160,1],[161,0],[161,2],[165,0],[165,2],[167,0],[167,2],[176,0],[176,2],[177,0],[177,2],[178,0],[178,2],[188,0],[188,1],[197,0],[197,1],[210,0],[210,1],[211,0],[211,1],[219,0],[219,1],[220,0],[220,1],[252,0],[252,1],[254,0],[254,1],[255,0],[255,1],[256,0],[256,1],[268,1],[268,1],[771,1],[771,1],[293,0],[293,1],[306,1],[306,1],[342,1],[342,1],[376,0],[376,1],[380,0],[380,1],[387,0],[387,1],[388,0],[388,1],[399,0],[399,1],[400,0],[400,1],[402,1],[402,1],[416,0],[416,1],[417,0],[417,1],[470,0],[470,1],[471,0],[471,1],[472,0],[472,1],[476,0],[476,1],[481,0],[481,1],[482,0],[482,1],[484,0],[484,1],[485,0],[485,1],[486,0],[486,1],[491,0],[491,1],[492,0],[492,1],[493,0],[493,1],[494,0],[494,1],[495,0],[495,1],[496,0],[496,1],[497,0],[497,1],[499,0],[499,1],[500,0],[500,1],[501,0],[501,1],[504,0],[504,2],[506,0],[506,2],[507,0],[507,2],[508,0],[508,2],[517,0],[517,1],[518,0],[518,1]],performAction:function(b2,v2,E2,p,M2,a,_t){var o=a.length-1;switch(M2){case 1:l.options.casesensitive?this.$=a[o]:this.$=a[o].toLowerCase();break;case 2:this.$=si(a[o].substr(1,a[o].length-2));break;case 3:this.$=a[o].toLowerCase();break;case 4:this.$=a[o];break;case 5:this.$=a[o]?a[o-1]+" "+a[o]:a[o-1];break;case 6:return new p.Statements({statements:a[o-1]});case 7:this.$=a[o-2],a[o]&&a[o-2].push(a[o]);break;case 8:case 9:case 70:case 80:case 85:case 143:case 177:case 205:case 206:case 243:case 262:case 277:case 363:case 381:case 460:case 483:case 484:case 488:case 496:case 537:case 538:case 575:case 658:case 668:case 692:case 694:case 696:case 711:case 712:case 742:case 766:this.$=[a[o]];break;case 10:this.$=a[o],a[o].explain=!0;break;case 11:this.$=a[o],a[o].explain=!0;break;case 12:this.$=a[o],p.exists&&(this.$.exists=p.exists),delete p.exists,p.queries&&(this.$.queries=p.queries),delete p.queries;break;case 13:case 162:case 172:case 238:case 239:case 241:case 249:case 251:case 260:case 271:case 274:case 384:case 500:case 510:case 512:case 524:case 530:case 531:case 576:this.$=void 0;break;case 68:this.$=new p.WithSelect({withs:a[o-1],select:a[o]});break;case 69:case 574:a[o-2].push(a[o]),this.$=a[o-2];break;case 71:this.$={name:a[o-4],select:a[o-1]};break;case 72:p.extend(this.$,a[o-9]),p.extend(this.$,a[o-8]),p.extend(this.$,a[o-7]),p.extend(this.$,a[o-6]),p.extend(this.$,a[o-5]),p.extend(this.$,a[o-4]),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-2]),p.extend(this.$,a[o-1]),p.extend(this.$,a[o]),this.$=a[o-9],p.exists&&(this.$.exists=p.exists.slice());break;case 73:this.$=new p.Search({selectors:a[o-2],from:a[o]}),p.extend(this.$,a[o-1]);break;case 74:this.$={pivot:{expr:a[o-5],columnid:a[o-3],inlist:a[o-2],as:a[o]}};break;case 75:this.$={unpivot:{tocolumnid:a[o-8],forcolumnid:a[o-6],inlist:a[o-3],as:a[o]}};break;case 76:case 529:case 558:case 594:case 628:case 645:case 646:case 649:case 671:this.$=a[o-1];break;case 77:case 78:case 86:case 147:case 185:case 248:case 284:case 292:case 293:case 294:case 295:case 296:case 297:case 298:case 299:case 300:case 301:case 302:case 303:case 304:case 305:case 308:case 309:case 325:case 326:case 327:case 328:case 329:case 330:case 383:case 449:case 450:case 451:case 452:case 453:case 454:case 525:case 551:case 555:case 557:case 632:case 633:case 634:case 635:case 636:case 637:case 641:case 643:case 644:case 653:case 669:case 670:case 733:case 748:case 749:case 751:case 752:case 758:case 759:this.$=a[o];break;case 79:case 84:case 741:case 765:this.$=a[o-2],this.$.push(a[o]);break;case 81:this.$={expr:a[o]};break;case 82:this.$={expr:a[o-2],as:a[o]};break;case 83:this.$={removecolumns:a[o]};break;case 87:this.$={like:a[o]};break;case 90:case 104:this.$={srchid:"PROP",args:[a[o]]};break;case 91:this.$={srchid:"ORDERBY",args:a[o-1]};break;case 92:var xt=a[o-1];xt||(xt="ASC"),this.$={srchid:"ORDERBY",args:[{expression:new p.Column({columnid:"_"}),direction:xt}]};break;case 93:this.$={srchid:"PARENT"};break;case 94:this.$={srchid:"APROP",args:[a[o]]};break;case 95:this.$={selid:"ROOT"};break;case 96:this.$={srchid:"EQ",args:[a[o]]};break;case 97:this.$={srchid:"LIKE",args:[a[o]]};break;case 98:case 99:this.$={selid:"WITH",args:a[o-1]};break;case 100:this.$={srchid:a[o-3].toUpperCase(),args:a[o-1]};break;case 101:this.$={srchid:"WHERE",args:[a[o-1]]};break;case 102:this.$={selid:"OF",args:[a[o-1]]};break;case 103:this.$={srchid:"CLASS",args:[a[o-1]]};break;case 105:this.$={srchid:"NAME",args:[a[o].substr(1,a[o].length-2)]};break;case 106:this.$={srchid:"CHILD"};break;case 107:this.$={srchid:"VERTEX"};break;case 108:this.$={srchid:"EDGE"};break;case 109:this.$={srchid:"REF"};break;case 110:this.$={srchid:"SHARP",args:[a[o]]};break;case 111:this.$={srchid:"ATTR",args:typeof a[o]>"u"?void 0:[a[o]]};break;case 112:this.$={srchid:"ATTR"};break;case 113:this.$={srchid:"OUT"};break;case 114:this.$={srchid:"IN"};break;case 115:this.$={srchid:"OUTOUT"};break;case 116:this.$={srchid:"ININ"};break;case 117:this.$={srchid:"CONTENT"};break;case 118:this.$={srchid:"EX",args:[new p.Json({value:a[o]})]};break;case 119:this.$={srchid:"AT",args:[a[o]]};break;case 120:this.$={srchid:"AS",args:[a[o]]};break;case 121:this.$={srchid:"SET",args:a[o-1]};break;case 122:this.$={selid:"TO",args:[a[o]]};break;case 123:this.$={srchid:"VALUE"};break;case 124:this.$={srchid:"ROW",args:a[o-1]};break;case 125:this.$={srchid:"CLASS",args:[a[o]]};break;case 126:this.$={selid:a[o],args:[a[o-1]]};break;case 127:this.$={selid:"NOT",args:a[o-1]};break;case 128:this.$={selid:"IF",args:a[o-1]};break;case 129:this.$={selid:a[o-3],args:a[o-1]};break;case 130:this.$={selid:"DISTINCT",args:a[o-1]};break;case 131:this.$={selid:"UNION",args:a[o-1]};break;case 132:this.$={selid:"UNIONALL",args:a[o-1]};break;case 133:this.$={selid:"ALL",args:[a[o-1]]};break;case 134:this.$={selid:"ANY",args:[a[o-1]]};break;case 135:this.$={selid:"INTERSECT",args:a[o-1]};break;case 136:this.$={selid:"EXCEPT",args:a[o-1]};break;case 137:this.$={selid:"AND",args:a[o-1]};break;case 138:this.$={selid:"OR",args:a[o-1]};break;case 139:this.$={selid:"PATH",args:[a[o-1]]};break;case 140:this.$={srchid:"RETURN",args:a[o-1]};break;case 141:this.$={selid:"REPEAT",sels:a[o-3],args:a[o-1]};break;case 142:this.$=a[o-2],this.$.push(a[o]);break;case 144:this.$="PLUS";break;case 145:this.$="STAR";break;case 146:this.$="QUESTION";break;case 148:this.$=new p.Select({columns:a[o],distinct:!0}),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-1]);break;case 149:this.$=new p.Select({columns:a[o],distinct:!0}),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-1]);break;case 150:this.$=new p.Select({columns:a[o],all:!0}),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-1]);break;case 151:a[o]?(this.$=new p.Select({columns:a[o]}),p.extend(this.$,a[o-2]),p.extend(this.$,a[o-1])):this.$=new p.Select({columns:[new p.Column({columnid:"_"})],modifier:"COLUMN"});break;case 152:a[o]=="SELECT"?this.$=void 0:this.$={modifier:a[o]};break;case 153:this.$={modifier:"VALUE"};break;case 154:this.$={modifier:"ROW"};break;case 155:this.$={modifier:"COLUMN"};break;case 156:this.$={modifier:"MATRIX"};break;case 157:this.$={modifier:"TEXTSTRING"};break;case 158:this.$={modifier:"INDEX"};break;case 159:this.$={modifier:"RECORDSET"};break;case 160:this.$={top:a[o-1],percent:typeof a[o]<"u"?!0:void 0};break;case 161:this.$={top:a[o-1]};break;case 163:case 336:case 532:case 533:case 734:this.$=void 0;break;case 164:case 165:case 166:case 167:this.$={into:a[o]};break;case 168:var y2=a[o];y2=y2.substr(1,y2.length-2);var ze=y2.substr(-3).toUpperCase(),st=y2.substr(-4).toUpperCase();y2[0]=="#"?this.$={into:new p.FuncValue({funcid:"HTML",args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]})}:ze=="XLS"||ze=="CSV"||ze=="TAB"?this.$={into:new p.FuncValue({funcid:ze,args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]})}:(st=="XLSX"||st=="JSON")&&(this.$={into:new p.FuncValue({funcid:st,args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]})});break;case 169:this.$={from:a[o]};break;case 170:this.$={from:a[o-1],joins:a[o]};break;case 171:this.$={from:a[o-2],joins:a[o-1]};break;case 173:this.$=new p.Apply({select:a[o-2],applymode:"CROSS",as:a[o]});break;case 174:this.$=new p.Apply({select:a[o-3],applymode:"CROSS",as:a[o]});break;case 175:this.$=new p.Apply({select:a[o-2],applymode:"OUTER",as:a[o]});break;case 176:this.$=new p.Apply({select:a[o-3],applymode:"OUTER",as:a[o]});break;case 178:case 244:case 461:case 539:case 540:this.$=a[o-2],a[o-2].push(a[o]);break;case 179:this.$=a[o-2],this.$.as=a[o];break;case 180:this.$=a[o-3],this.$.as=a[o];break;case 181:this.$=a[o-1],this.$.as="default";break;case 182:this.$=new p.Json({value:a[o-2]}),a[o-2].as=a[o];break;case 183:this.$=a[o-1],a[o-1].as=a[o];break;case 184:this.$=a[o-2],a[o-2].as=a[o];break;case 186:case 647:case 650:this.$=a[o-2];break;case 187:case 191:case 195:case 198:this.$=a[o-1],a[o-1].as=a[o];break;case 188:case 192:case 196:case 199:this.$=a[o-2],a[o-2].as=a[o];break;case 189:case 190:case 194:case 197:this.$=a[o],a[o].as="default";break;case 193:this.$={inserted:!0};break;case 200:var y2=a[o];y2=y2.substr(1,y2.length-2);var ze=y2.substr(-3).toUpperCase(),st=y2.substr(-4).toUpperCase(),vt;if(y2[0]=="#")vt=new p.FuncValue({funcid:"HTML",args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]});else if(ze=="XLS"||ze=="CSV"||ze=="TAB")vt=new p.FuncValue({funcid:ze,args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]});else if(st=="XLSX"||st=="JSON")vt=new p.FuncValue({funcid:st,args:[new p.StringValue({value:y2}),new p.Json({value:{headers:!0}})]});else throw new Error("Unknown string in FROM clause");this.$=vt;break;case 201:a[o-2]=="INFORMATION_SCHEMA"?this.$=new p.FuncValue({funcid:a[o-2],args:[new p.StringValue({value:a[o]})]}):this.$=new p.Table({databaseid:a[o-2],tableid:a[o]});break;case 202:this.$=new p.Table({tableid:a[o]});break;case 203:case 204:this.$=a[o-1],a[o-1].push(a[o]);break;case 207:this.$=new p.Join(a[o-2]),p.extend(this.$,a[o-1]),p.extend(this.$,a[o]);break;case 208:this.$={table:a[o]};break;case 209:this.$={table:a[o-1],as:a[o]};break;case 210:this.$={table:a[o-2],as:a[o]};break;case 211:this.$={json:new p.Json({value:a[o-2],as:a[o]})};break;case 212:this.$={param:a[o-1],as:a[o]};break;case 213:this.$={param:a[o-2],as:a[o]};break;case 214:this.$={select:a[o-2],as:a[o]};break;case 215:this.$={select:a[o-3],as:a[o]};break;case 216:this.$={func:a[o],as:"default"};break;case 217:this.$={func:a[o-1],as:a[o]};break;case 218:this.$={func:a[o-2],as:a[o]};break;case 219:this.$={variable:a[o],as:"default"};break;case 220:this.$={variable:a[o-1],as:a[o]};break;case 221:this.$={variable:a[o-2],as:a[o]};break;case 222:this.$={joinmode:a[o]};break;case 223:this.$={joinmode:a[o-1],natural:!0};break;case 224:case 225:this.$="INNER";break;case 226:case 227:this.$="LEFT";break;case 228:case 229:this.$="RIGHT";break;case 230:case 231:this.$="OUTER";break;case 232:this.$="SEMI";break;case 233:this.$="ANTI";break;case 234:this.$="CROSS";break;case 235:this.$={on:a[o]};break;case 236:case 706:this.$={using:a[o]};break;case 237:case 707:this.$={using:a[o-1]};break;case 240:this.$={where:new p.Expression({expression:a[o]})};break;case 242:this.$={group:a[o-1]},p.extend(this.$,a[o]);break;case 245:this.$=new p.GroupExpression({type:"GROUPING SETS",group:a[o-1]});break;case 246:this.$=new p.GroupExpression({type:"ROLLUP",group:a[o-1]});break;case 247:this.$=new p.GroupExpression({type:"CUBE",group:a[o-1]});break;case 250:this.$={having:a[o]};break;case 252:this.$={union:a[o]};break;case 253:this.$={unionall:a[o]};break;case 254:this.$={except:a[o]};break;case 255:this.$={intersect:a[o]};break;case 256:this.$={union:a[o],corresponding:!0};break;case 257:this.$={unionall:a[o],corresponding:!0};break;case 258:this.$={except:a[o],corresponding:!0};break;case 259:this.$={intersect:a[o],corresponding:!0};break;case 261:this.$={order:a[o]};break;case 263:this.$=a[o-2],a[o-2].push(a[o]);break;case 264:this.$={nullsOrder:"FIRST"};break;case 265:this.$={nullsOrder:"LAST"};break;case 266:this.$=new p.Expression({expression:a[o],direction:"ASC"});break;case 267:this.$=new p.Expression({expression:a[o-1],direction:a[o].toUpperCase()});break;case 268:this.$=new p.Expression({expression:a[o-2],direction:a[o-1].toUpperCase()}),p.extend(this.$,a[o]);break;case 269:this.$=new p.Expression({expression:a[o-2],direction:"ASC",nocase:!0});break;case 270:this.$=new p.Expression({expression:a[o-3],direction:a[o].toUpperCase(),nocase:!0});break;case 272:this.$={limit:a[o-1]},p.extend(this.$,a[o]);break;case 273:this.$={limit:a[o-2],offset:a[o-6]};break;case 275:this.$={offset:a[o]};break;case 276:case 518:case 542:case 657:case 667:case 691:case 693:case 697:a[o-2].push(a[o]),this.$=a[o-2];break;case 278:case 280:case 282:a[o-2].as=a[o],this.$=a[o-2];break;case 279:case 281:case 283:a[o-1].as=a[o],this.$=a[o-1];break;case 285:this.$=new p.Column({columid:a[o],tableid:a[o-2],databaseid:a[o-4]});break;case 286:this.$=new p.Column({columnid:a[o],tableid:a[o-2]});break;case 287:this.$=new p.Column({columnid:a[o]});break;case 288:this.$=new p.Column({columnid:a[o],tableid:a[o-2],databaseid:a[o-4]});break;case 289:case 290:this.$=new p.Column({columnid:a[o],tableid:a[o-2]});break;case 291:this.$=new p.Column({columnid:a[o]});break;case 306:this.$=new p.DomainValueValue;break;case 307:this.$=new p.Json({value:a[o]});break;case 310:case 311:case 312:p.queries||(p.queries=[]),p.queries.push(a[o-1]),a[o-1].queriesidx=p.queries.length,this.$=a[o-1];break;case 313:this.$=a[o];break;case 314:this.$=new p.FuncValue({funcid:"CURRENT_TIMESTAMP"});break;case 315:this.$=new p.FuncValue({funcid:"CURRENT_DATE"});break;case 316:this.$=new p.JavaScript({value:a[o].substr(2,a[o].length-4)});break;case 317:this.$=new p.JavaScript({value:'alasql.fn["'+a[o-2]+'"] = '+a[o].substr(2,a[o].length-4)});break;case 318:this.$=new p.JavaScript({value:'alasql.aggr["'+a[o-2]+'"] = '+a[o].substr(2,a[o].length-4)});break;case 319:this.$=new p.FuncValue({funcid:a[o],newid:!0});break;case 320:this.$=a[o],p.extend(this.$,{newid:!0});break;case 321:this.$=new p.Convert({expression:a[o-3]}),p.extend(this.$,a[o-1]);break;case 322:this.$=new p.Convert({expression:a[o-5],style:a[o-1]}),p.extend(this.$,a[o-3]);break;case 323:this.$=new p.Convert({expression:a[o-1]}),p.extend(this.$,a[o-3]);break;case 324:this.$=new p.Convert({expression:a[o-3],style:a[o-1]}),p.extend(this.$,a[o-5]);break;case 331:this.$=new p.FuncValue({funcid:"CURRENT_TIMESTAMP"});break;case 332:this.$=new p.FuncValue({funcid:"CURRENT_DATE"});break;case 333:a[o-2].length>1&&(a[o-4].toUpperCase()=="MAX"||a[o-4].toUpperCase()=="MIN")?this.$=new p.FuncValue({funcid:a[o-4],args:a[o-2]}):this.$=new p.AggrValue({aggregatorid:a[o-4].toUpperCase(),expression:a[o-2].pop(),over:a[o]});break;case 334:this.$=new p.AggrValue({aggregatorid:a[o-5].toUpperCase(),expression:a[o-2],distinct:!0,over:a[o]});break;case 335:this.$=new p.AggrValue({aggregatorid:a[o-5].toUpperCase(),expression:a[o-2],over:a[o]});break;case 337:case 338:this.$=new p.Over,p.extend(this.$,a[o-1]);break;case 339:this.$=new p.Over,p.extend(this.$,a[o-2]),p.extend(this.$,a[o-1]);break;case 340:this.$={partition:a[o]};break;case 341:this.$={order:a[o]};break;case 342:this.$="SUM";break;case 343:this.$="TOTAL";break;case 344:this.$="COUNT";break;case 345:this.$="MIN";break;case 346:case 553:this.$="MAX";break;case 347:this.$="AVG";break;case 348:this.$="FIRST";break;case 349:this.$="LAST";break;case 350:this.$="AGGR";break;case 351:this.$="ARRAY";break;case 352:var jt=a[o-4],_2=a[o-1];_2.length>1&&(jt.toUpperCase()=="MIN"||jt.toUpperCase()=="MAX")?this.$=new p.FuncValue({funcid:jt,args:_2}):l.aggr[a[o-4]]?this.$=new p.AggrValue({aggregatorid:"REDUCE",funcid:jt,expression:_2.pop(),distinct:a[o-2]=="DISTINCT"}):this.$=new p.FuncValue({funcid:jt,args:_2});break;case 353:case 356:this.$=new p.FuncValue({funcid:a[o-2]});break;case 354:this.$=new p.FuncValue({funcid:"IIF",args:a[o-1]});break;case 355:this.$=new p.FuncValue({funcid:"REPLACE",args:a[o-1]});break;case 357:this.$=new p.FuncValue({funcid:"DATEADD",args:[new p.StringValue({value:a[o-5]}),a[o-3],a[o-1]]});break;case 358:this.$=new p.FuncValue({funcid:"DATEADD",args:[a[o-5],a[o-3],a[o-1]]});break;case 359:this.$=new p.FuncValue({funcid:"DATEDIFF",args:[new p.StringValue({value:a[o-5]}),a[o-3],a[o-1]]});break;case 360:this.$=new p.FuncValue({funcid:"DATEDIFF",args:[a[o-5],a[o-3],a[o-1]]});break;case 361:this.$=new p.FuncValue({funcid:"TIMESTAMPDIFF",args:[new p.StringValue({value:a[o-5]}),a[o-3],a[o-1]]});break;case 362:this.$=new p.FuncValue({funcid:"INTERVAL",args:[a[o-1],new p.StringValue({value:a[o].toLowerCase()})]});break;case 364:a[o-2].push(a[o]),this.$=a[o-2];break;case 365:this.$=new p.NumValue({value:+a[o]});break;case 366:this.$=new p.LogicValue({value:!0});break;case 367:this.$=new p.LogicValue({value:!1});break;case 368:this.$=new p.StringValue({value:a[o].substr(1,a[o].length-2).replace(/(\\\')/g,"'").replace(/(\'\')/g,"'")});break;case 369:this.$=new p.StringValue({value:a[o].substr(2,a[o].length-3).replace(/(\\\')/g,"'").replace(/(\'\')/g,"'")});break;case 370:this.$=new p.NullValue({value:void 0});break;case 371:this.$=new p.VarValue({variable:a[o]});break;case 372:p.exists||(p.exists=[]),this.$=new p.ExistsValue({value:a[o-1],existsidx:p.exists.length}),p.exists.push(a[o-1]);break;case 373:this.$=new p.ArrayValue({value:a[o-1]});break;case 374:case 375:this.$=new p.ParamValue({param:a[o]});break;case 376:typeof p.question>"u"&&(p.question=0),this.$=new p.ParamValue({param:p.question++});break;case 377:typeof p.question>"u"&&(p.question=0),this.$=new p.ParamValue({param:p.question++,array:!0});break;case 378:this.$=new p.CaseValue({expression:a[o-3],whens:a[o-2],elses:a[o-1]});break;case 379:this.$=new p.CaseValue({whens:a[o-2],elses:a[o-1]});break;case 380:case 709:case 710:this.$=a[o-1],this.$.push(a[o]);break;case 382:this.$={when:a[o-2],then:a[o]};break;case 385:case 386:this.$=new p.Op({left:a[o-2],op:"REGEXP",right:a[o]});break;case 387:this.$=new p.Op({left:a[o-2],op:"GLOB",right:a[o]});break;case 388:this.$=new p.Op({left:a[o-2],op:"LIKE",right:a[o]});break;case 389:this.$=new p.Op({left:a[o-4],op:"LIKE",right:a[o-2],escape:a[o]});break;case 390:this.$=new p.Op({left:a[o-2],op:"NOT LIKE",right:a[o]});break;case 391:this.$=new p.Op({left:a[o-4],op:"NOT LIKE",right:a[o-2],escape:a[o]});break;case 392:this.$=new p.Op({left:a[o-2],op:"||",right:a[o]});break;case 393:this.$=new p.Op({left:a[o-2],op:"+",right:a[o]});break;case 394:this.$=new p.Op({left:a[o-2],op:"-",right:a[o]});break;case 395:this.$=new p.Op({left:a[o-2],op:"*",right:a[o]});break;case 396:this.$=new p.Op({left:a[o-2],op:"/",right:a[o]});break;case 397:this.$=new p.Op({left:a[o-2],op:"%",right:a[o]});break;case 398:this.$=new p.Op({left:a[o-2],op:"^",right:a[o]});break;case 399:this.$=new p.Op({left:a[o-2],op:">>",right:a[o]});break;case 400:this.$=new p.Op({left:a[o-2],op:"<<",right:a[o]});break;case 401:this.$=new p.Op({left:a[o-2],op:"&",right:a[o]});break;case 402:this.$=new p.Op({left:a[o-2],op:"|",right:a[o]});break;case 403:case 404:case 406:this.$=new p.Op({left:a[o-2],op:"->",right:a[o]});break;case 405:this.$=new p.Op({left:a[o-4],op:"->",right:a[o-1]});break;case 407:case 408:case 410:this.$=new p.Op({left:a[o-2],op:"!",right:a[o]});break;case 409:this.$=new p.Op({left:a[o-4],op:"!",right:a[o-1]});break;case 411:this.$=new p.Op({left:a[o-2],op:">",right:a[o]});break;case 412:this.$=new p.Op({left:a[o-2],op:">=",right:a[o]});break;case 413:this.$=new p.Op({left:a[o-2],op:"<",right:a[o]});break;case 414:this.$=new p.Op({left:a[o-2],op:"<=",right:a[o]});break;case 415:this.$=new p.Op({left:a[o-2],op:"=",right:a[o]});break;case 416:this.$=new p.Op({left:a[o-2],op:"==",right:a[o]});break;case 417:this.$=new p.Op({left:a[o-2],op:"===",right:a[o]});break;case 418:this.$=new p.Op({left:a[o-2],op:"!=",right:a[o]});break;case 419:this.$=new p.Op({left:a[o-2],op:"!==",right:a[o]});break;case 420:this.$=new p.Op({left:a[o-2],op:"!===",right:a[o]});break;case 421:p.queries||(p.queries=[]),this.$=new p.Op({left:a[o-5],op:a[o-4],allsome:a[o-3],right:a[o-1],queriesidx:p.queries.length}),p.queries.push(a[o-1]);break;case 422:this.$=new p.Op({left:a[o-5],op:a[o-4],allsome:a[o-3],right:a[o-1]});break;case 423:a[o-2].op=="BETWEEN1"?a[o-2].left.op=="AND"?this.$=new p.Op({left:a[o-2].left.left,op:"AND",right:new p.Op({left:a[o-2].left.right,op:"BETWEEN",right1:a[o-2].right,right2:a[o]})}):this.$=new p.Op({left:a[o-2].left,op:"BETWEEN",right1:a[o-2].right,right2:a[o]}):a[o-2].op=="NOT BETWEEN1"?a[o-2].left.op=="AND"?this.$=new p.Op({left:a[o-2].left.left,op:"AND",right:new p.Op({left:a[o-2].left.right,op:"NOT BETWEEN",right1:a[o-2].right,right2:a[o]})}):this.$=new p.Op({left:a[o-2].left,op:"NOT BETWEEN",right1:a[o-2].right,right2:a[o]}):this.$=new p.Op({left:a[o-2],op:"AND",right:a[o]});break;case 424:this.$=new p.Op({left:a[o-2],op:"OR",right:a[o]});break;case 425:this.$=new p.UniOp({op:"NOT",right:a[o]});break;case 426:this.$=new p.UniOp({op:"-",right:a[o]});break;case 427:this.$=new p.UniOp({op:"+",right:a[o]});break;case 428:this.$=new p.UniOp({op:"~",right:a[o]});break;case 429:this.$=new p.UniOp({op:"#",right:a[o]});break;case 430:this.$=new p.UniOp({right:a[o-1]});break;case 431:p.queries||(p.queries=[]),this.$=new p.Op({left:a[o-4],op:"IN",right:a[o-1],queriesidx:p.queries.length}),p.queries.push(a[o-1]);break;case 432:p.queries||(p.queries=[]),this.$=new p.Op({left:a[o-5],op:"NOT IN",right:a[o-1],queriesidx:p.queries.length}),p.queries.push(a[o-1]);break;case 433:this.$=new p.Op({left:a[o-4],op:"IN",right:a[o-1]});break;case 434:this.$=new p.Op({left:a[o-5],op:"NOT IN",right:a[o-1]});break;case 435:this.$=new p.Op({left:a[o-3],op:"IN",right:[]});break;case 436:this.$=new p.Op({left:a[o-4],op:"NOT IN",right:[]});break;case 437:case 439:this.$=new p.Op({left:a[o-2],op:"IN",right:a[o]});break;case 438:case 440:this.$=new p.Op({left:a[o-3],op:"NOT IN",right:a[o]});break;case 441:this.$=new p.Op({left:a[o-2],op:"BETWEEN1",right:a[o]});break;case 442:this.$=new p.Op({left:a[o-2],op:"NOT BETWEEN1",right:a[o]});break;case 443:this.$=new p.Op({op:"IS",left:a[o-2],right:a[o]});break;case 444:this.$=new p.Op({op:"IS",left:a[o-2],right:new p.UniOp({op:"NOT",right:new p.NullValue({value:void 0})})});break;case 445:this.$=new p.Convert({expression:a[o-2]}),p.extend(this.$,a[o]);break;case 446:case 447:this.$=a[o];break;case 448:this.$=a[o-1];break;case 455:this.$="ALL";break;case 456:this.$="SOME";break;case 457:this.$="ANY";break;case 458:this.$=new p.Update({table:a[o-4],columns:a[o-2],where:a[o]});break;case 459:this.$=new p.Update({table:a[o-2],columns:a[o]});break;case 462:this.$=new p.SetColumn({column:a[o-2],expression:a[o]});break;case 463:this.$=new p.SetColumn({variable:a[o-2],expression:a[o],method:a[o-3]});break;case 464:this.$=new p.Delete({table:a[o-2],where:a[o]});break;case 465:this.$=new p.Delete({table:a[o]});break;case 466:this.$=new p.Insert({into:a[o-2],values:a[o]});break;case 467:this.$=new p.Insert({into:a[o-1],values:a[o]});break;case 468:case 470:this.$=new p.Insert({into:a[o-2],values:a[o],orreplace:!0});break;case 469:case 471:this.$=new p.Insert({into:a[o-1],values:a[o],orreplace:!0});break;case 472:this.$=new p.Insert({into:a[o-2],default:!0});break;case 473:this.$=new p.Insert({into:a[o-5],columns:a[o-3],values:a[o]});break;case 474:this.$=new p.Insert({into:a[o-4],columns:a[o-2],values:a[o]});break;case 475:this.$=new p.Insert({into:a[o-1],select:a[o]});break;case 476:this.$=new p.Insert({into:a[o-1],select:a[o],orreplace:!0});break;case 477:this.$=new p.Insert({into:a[o-4],columns:a[o-2],select:a[o]});break;case 482:this.$=[a[o-1]];break;case 485:this.$=a[o-4],a[o-4].push(a[o-1]);break;case 486:case 487:case 489:case 497:this.$=a[o-2],a[o-2].push(a[o]);break;case 498:this.$=new p.CreateTable({table:a[o-4]}),p.extend(this.$,a[o-7]),p.extend(this.$,a[o-6]),p.extend(this.$,a[o-5]),p.extend(this.$,a[o-2]),p.extend(this.$,a[o]);break;case 499:this.$=new p.CreateTable({table:a[o]}),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-2]),p.extend(this.$,a[o-1]);break;case 501:this.$={class:!0};break;case 511:this.$={temporary:!0};break;case 513:this.$={ifnotexists:!0};break;case 514:this.$={columns:a[o-2],constraints:a[o]};break;case 515:this.$={columns:a[o]};break;case 516:this.$={as:a[o]};break;case 517:case 541:this.$=[a[o]];break;case 519:case 520:case 521:case 522:case 523:a[o].constraintid=a[o-1],this.$=a[o];break;case 526:this.$={type:"CHECK",expression:a[o-1]};break;case 527:this.$={type:"PRIMARY KEY",columns:a[o-1],clustered:(a[o-3]+"").toUpperCase()};break;case 528:this.$={type:"FOREIGN KEY",columns:a[o-5],fktable:a[o-2],fkcolumns:a[o-1]};break;case 534:this.$={type:"UNIQUE",columns:a[o-1],clustered:(a[o-3]+"").toUpperCase()};break;case 543:this.$=new p.ColumnDef({columnid:a[o-2]}),p.extend(this.$,a[o-1]),p.extend(this.$,a[o]);break;case 544:this.$=new p.ColumnDef({columnid:a[o-1]}),p.extend(this.$,a[o]);break;case 545:this.$=new p.ColumnDef({columnid:a[o],dbtypeid:""});break;case 546:this.$={dbtypeid:a[o-5],dbsize:a[o-3],dbprecision:+a[o-1]};break;case 547:this.$={dbtypeid:a[o-3],dbsize:a[o-1]};break;case 548:this.$={dbtypeid:a[o]};break;case 549:this.$={dbtypeid:"ENUM",enumvalues:a[o-1]};break;case 550:this.$=a[o-1],a[o-1].dbtypeid+="["+a[o]+"]";break;case 552:case 760:this.$=+a[o];break;case 554:this.$=void 0;break;case 556:p.extend(a[o-1],a[o]),this.$=a[o-1];break;case 559:this.$={primarykey:!0};break;case 560:case 561:this.$={foreignkey:{table:a[o-1],columnid:a[o]}};break;case 562:this.$={identity:{value:a[o-3],step:a[o-1]}};break;case 563:this.$={identity:{value:1,step:1}};break;case 564:case 566:this.$={default:a[o]};break;case 565:this.$={default:a[o-1]};break;case 567:this.$={null:!0};break;case 568:this.$={notnull:!0};break;case 569:this.$={check:a[o]};break;case 570:this.$={unique:!0};break;case 571:this.$={onupdate:a[o]};break;case 572:this.$={onupdate:a[o-1]};break;case 573:this.$=new p.DropTable({tables:a[o],type:a[o-2]}),p.extend(this.$,a[o-1]);break;case 577:this.$={ifexists:!0};break;case 578:this.$=new p.AlterTable({table:a[o-3],renameto:a[o]});break;case 579:this.$=new p.AlterTable({table:a[o-3],addcolumn:a[o]});break;case 580:this.$=new p.AlterTable({table:a[o-3],modifycolumn:a[o]});break;case 581:this.$=new p.AlterTable({table:a[o-5],renamecolumn:a[o-2],to:a[o]});break;case 582:this.$=new p.AlterTable({table:a[o-3],dropcolumn:a[o]});break;case 583:this.$=new p.AlterTable({table:a[o-2],renameto:a[o]});break;case 584:this.$=new p.AttachDatabase({databaseid:a[o],engineid:a[o-2].toUpperCase()});break;case 585:this.$=new p.AttachDatabase({databaseid:a[o-3],engineid:a[o-5].toUpperCase(),args:a[o-1]});break;case 586:this.$=new p.AttachDatabase({databaseid:a[o-2],engineid:a[o-4].toUpperCase(),as:a[o]});break;case 587:this.$=new p.AttachDatabase({databaseid:a[o-5],engineid:a[o-7].toUpperCase(),as:a[o],args:a[o-3]});break;case 588:this.$=new p.DetachDatabase({databaseid:a[o]});break;case 589:this.$=new p.CreateDatabase({databaseid:a[o]}),p.extend(this.$,a[o]);break;case 590:this.$=new p.CreateDatabase({engineid:a[o-4].toUpperCase(),databaseid:a[o-1],as:a[o]}),p.extend(this.$,a[o-2]);break;case 591:this.$=new p.CreateDatabase({engineid:a[o-7].toUpperCase(),databaseid:a[o-4],args:a[o-2],as:a[o]}),p.extend(this.$,a[o-5]);break;case 592:this.$=new p.CreateDatabase({engineid:a[o-4].toUpperCase(),as:a[o],args:[a[o-1]]}),p.extend(this.$,a[o-2]);break;case 593:this.$=void 0;break;case 595:case 596:this.$=new p.UseDatabase({databaseid:a[o]});break;case 597:this.$=new p.DropDatabase({databaseid:a[o]}),p.extend(this.$,a[o-1]);break;case 598:case 599:this.$=new p.DropDatabase({databaseid:a[o],engineid:a[o-3].toUpperCase()}),p.extend(this.$,a[o-1]);break;case 600:this.$=new p.CreateIndex({indexid:a[o-5],table:a[o-3],columns:a[o-1]});break;case 601:this.$=new p.CreateIndex({indexid:a[o-5],table:a[o-3],columns:a[o-1],unique:!0});break;case 602:this.$=new p.DropIndex({indexid:a[o]});break;case 603:this.$=new p.ShowDatabases;break;case 604:this.$=new p.ShowDatabases({like:a[o]});break;case 605:this.$=new p.ShowDatabases({engineid:a[o-1].toUpperCase()});break;case 606:this.$=new p.ShowDatabases({engineid:a[o-3].toUpperCase(),like:a[o]});break;case 607:this.$=new p.ShowTables;break;case 608:this.$=new p.ShowTables({like:a[o]});break;case 609:this.$=new p.ShowTables({databaseid:a[o]});break;case 610:this.$=new p.ShowTables({like:a[o],databaseid:a[o-2]});break;case 611:this.$=new p.ShowColumns({table:a[o]});break;case 612:this.$=new p.ShowColumns({table:a[o-2],databaseid:a[o]});break;case 613:this.$=new p.ShowIndex({table:a[o]});break;case 614:this.$=new p.ShowIndex({table:a[o-2],databaseid:a[o]});break;case 615:this.$=new p.ShowCreateTable({table:a[o]});break;case 616:this.$=new p.ShowCreateTable({table:a[o-2],databaseid:a[o]});break;case 617:this.$=new p.CreateTable({table:a[o-6],view:!0,select:a[o-1],viewcolumns:a[o-4]}),p.extend(this.$,a[o-9]),p.extend(this.$,a[o-7]);break;case 618:this.$=new p.CreateTable({table:a[o-3],view:!0,select:a[o-1]}),p.extend(this.$,a[o-6]),p.extend(this.$,a[o-4]);break;case 622:this.$=new p.DropTable({tables:a[o],view:!0}),p.extend(this.$,a[o-1]);break;case 623:case 770:this.$=new p.ExpressionStatement({expression:a[o]});break;case 624:this.$=new p.Source({url:a[o].value});break;case 625:this.$=new p.Assert({value:a[o]});break;case 626:this.$=new p.Assert({value:a[o].value});break;case 627:this.$=new p.Assert({value:a[o],message:a[o-2]});break;case 629:case 640:case 642:this.$=a[o].value;break;case 630:case 638:this.$=+a[o].value;break;case 631:this.$=!!a[o].value;break;case 639:this.$=""+a[o].value;break;case 648:this.$={};break;case 651:this.$=[];break;case 652:p.extend(a[o-2],a[o]),this.$=a[o-2];break;case 654:this.$={},this.$[a[o-2].substr(1,a[o-2].length-2)]=a[o];break;case 655:case 656:this.$={},this.$[a[o-2]]=a[o];break;case 659:this.$=new p.SetVariable({variable:a[o-2].toLowerCase(),value:a[o]});break;case 660:this.$=new p.SetVariable({variable:a[o-1].toLowerCase(),value:a[o]});break;case 661:this.$=new p.SetVariable({variable:a[o-2],expression:a[o]});break;case 662:this.$=new p.SetVariable({variable:a[o-3],props:a[o-2],expression:a[o]});break;case 663:this.$=new p.SetVariable({variable:a[o-2],expression:a[o],method:a[o-3]});break;case 664:this.$=new p.SetVariable({variable:a[o-3],props:a[o-2],expression:a[o],method:a[o-4]});break;case 665:this.$="@";break;case 666:this.$="$";break;case 672:this.$=!0;break;case 673:this.$=!1;break;case 674:this.$=new p.CommitTransaction;break;case 675:this.$=new p.RollbackTransaction;break;case 676:this.$=new p.BeginTransaction;break;case 677:this.$=new p.If({expression:a[o-2],thenstat:a[o-1],elsestat:a[o]}),a[o-1].exists&&(this.$.exists=a[o-1].exists),a[o-1].queries&&(this.$.queries=a[o-1].queries);break;case 678:this.$=new p.If({expression:a[o-1],thenstat:a[o]}),a[o].exists&&(this.$.exists=a[o].exists),a[o].queries&&(this.$.queries=a[o].queries);break;case 679:this.$=a[o];break;case 680:this.$=new p.While({expression:a[o-1],loopstat:a[o]}),a[o].exists&&(this.$.exists=a[o].exists),a[o].queries&&(this.$.queries=a[o].queries);break;case 681:this.$=new p.Continue;break;case 682:this.$=new p.Break;break;case 683:this.$=new p.BeginEnd({statements:a[o-1]});break;case 684:this.$=new p.Print({exprs:a[o]});break;case 685:this.$=new p.Print({select:a[o]});break;case 686:this.$=new p.Require({paths:a[o]});break;case 687:this.$=new p.Require({plugins:a[o]});break;case 688:case 689:this.$=a[o].toUpperCase();break;case 690:this.$=new p.Echo({expr:a[o]});break;case 695:this.$=new p.Declare({declares:a[o]});break;case 698:this.$={variable:a[o-1]},p.extend(this.$,a[o]);break;case 699:this.$={variable:a[o-2]},p.extend(this.$,a[o]);break;case 700:this.$={variable:a[o-3],expression:a[o]},p.extend(this.$,a[o-2]);break;case 701:this.$={variable:a[o-4],expression:a[o]},p.extend(this.$,a[o-2]);break;case 702:this.$=new p.TruncateTable({table:a[o]});break;case 703:this.$=new p.Merge,p.extend(this.$,a[o-4]),p.extend(this.$,a[o-3]),p.extend(this.$,a[o-2]),p.extend(this.$,{matches:a[o-1]}),p.extend(this.$,a[o]);break;case 704:case 705:this.$={into:a[o]};break;case 708:this.$={on:a[o]};break;case 713:this.$={matched:!0,action:a[o]};break;case 714:this.$={matched:!0,expr:a[o-2],action:a[o]};break;case 715:this.$={delete:!0};break;case 716:this.$={update:a[o]};break;case 717:case 718:this.$={matched:!1,bytarget:!0,action:a[o]};break;case 719:case 720:this.$={matched:!1,bytarget:!0,expr:a[o-2],action:a[o]};break;case 721:this.$={matched:!1,bysource:!0,action:a[o]};break;case 722:this.$={matched:!1,bysource:!0,expr:a[o-2],action:a[o]};break;case 723:this.$={insert:!0,values:a[o]};break;case 724:this.$={insert:!0,values:a[o],columns:a[o-3]};break;case 725:this.$={insert:!0,defaultvalues:!0};break;case 726:this.$={insert:!0,defaultvalues:!0,columns:a[o-3]};break;case 728:this.$={output:{columns:a[o]}};break;case 729:this.$={output:{columns:a[o-3],intovar:a[o],method:a[o-1]}};break;case 730:this.$={output:{columns:a[o-2],intotable:a[o]}};break;case 731:this.$={output:{columns:a[o-5],intotable:a[o-3],intocolumns:a[o-1]}};break;case 732:this.$=new p.CreateVertex({class:a[o-3],sharp:a[o-2],name:a[o-1]}),p.extend(this.$,a[o]);break;case 735:this.$={sets:a[o]};break;case 736:this.$={content:a[o]};break;case 737:this.$={select:a[o]};break;case 738:this.$=new p.CreateEdge({from:a[o-3],to:a[o-1],name:a[o-5]}),p.extend(this.$,a[o]);break;case 739:this.$=new p.CreateGraph({graph:a[o]});break;case 740:this.$=new p.CreateGraph({from:a[o]});break;case 743:this.$=a[o-2],a[o-1]&&(this.$.json=new p.Json({value:a[o-1]})),a[o]&&(this.$.as=a[o]);break;case 744:this.$={source:a[o-6],target:a[o]},a[o-3]&&(this.$.json=new p.Json({value:a[o-3]})),a[o-2]&&(this.$.as=a[o-2]),p.extend(this.$,a[o-4]);break;case 745:this.$={source:a[o-5],target:a[o]},a[o-2]&&(this.$.json=new p.Json({value:a[o-3]})),a[o-1]&&(this.$.as=a[o-2]);break;case 746:this.$={source:a[o-2],target:a[o]};break;case 750:this.$={vars:a[o],method:a[o-1]};break;case 753:case 754:var ct=a[o-1];this.$={prop:a[o-3],sharp:a[o-2],name:typeof ct>"u"?void 0:ct.substr(1,ct.length-2),class:a[o]};break;case 755:var Yt=a[o-1];this.$={sharp:a[o-2],name:typeof Yt>"u"?void 0:Yt.substr(1,Yt.length-2),class:a[o]};break;case 756:var Vt=a[o-1];this.$={name:typeof Vt>"u"?void 0:Vt.substr(1,Vt.length-2),class:a[o]};break;case 757:this.$={class:a[o]};break;case 763:this.$=new p.AddRule({left:a[o-2],right:a[o]});break;case 764:this.$=new p.AddRule({right:a[o]});break;case 767:this.$={termid:a[o]};break;case 768:this.$={termid:a[o-3],args:a[o-1]};break;case 771:this.$=new p.CreateTrigger({trigger:a[o-6],when:a[o-5],action:a[o-4],table:a[o-2],statement:a[o]}),a[o].exists&&(this.$.exists=a[o].exists),a[o].queries&&(this.$.queries=a[o].queries);break;case 772:this.$=new p.CreateTrigger({trigger:a[o-5],when:a[o-4],action:a[o-3],table:a[o-1],funcid:a[o]});break;case 773:this.$=new p.CreateTrigger({trigger:a[o-6],when:a[o-4],action:a[o-3],table:a[o-5],statement:a[o]}),a[o].exists&&(this.$.exists=a[o].exists),a[o].queries&&(this.$.queries=a[o].queries);break;case 774:case 775:case 777:this.$="AFTER";break;case 776:this.$="BEFORE";break;case 778:this.$="INSTEADOF";break;case 779:this.$="INSERT";break;case 780:this.$="DELETE";break;case 781:this.$="UPDATE";break;case 782:this.$=new p.DropTrigger({trigger:a[o]});break;case 783:this.$=new p.Reindex({indexid:a[o]});break;case 1057:case 1077:case 1079:case 1081:case 1085:case 1087:case 1089:case 1091:case 1093:case 1095:this.$=[];break;case 1058:case 1072:case 1074:case 1078:case 1080:case 1082:case 1086:case 1088:case 1090:case 1092:case 1094:case 1096:a[o-1].push(a[o]);break;case 1071:case 1073:this.$=[a[o]];break}},table:[e([10,607,768],n,{8:1,9:2,12:3,13:4,17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,2:t,4:r,5:i,14:s,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),{1:[3]},{10:[1,105],11:106,607:rt,768:zt},e(ut,[2,8]),e(ut,[2,9]),e(V,[2,12]),e(ut,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:109,2:t,4:r,5:i,15:[1,110],53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(V,[2,14]),e(V,[2,15]),e(V,[2,16]),e(V,[2,17]),e(V,[2,18]),e(V,[2,19]),e(V,[2,20]),e(V,[2,21]),e(V,[2,22]),e(V,[2,23]),e(V,[2,24]),e(V,[2,25]),e(V,[2,26]),e(V,[2,27]),e(V,[2,28]),e(V,[2,29]),e(V,[2,30]),e(V,[2,31]),e(V,[2,32]),e(V,[2,33]),e(V,[2,34]),e(V,[2,35]),e(V,[2,36]),e(V,[2,37]),e(V,[2,38]),e(V,[2,39]),e(V,[2,40]),e(V,[2,41]),e(V,[2,42]),e(V,[2,43]),e(V,[2,44]),e(V,[2,45]),e(V,[2,46]),e(V,[2,47]),e(V,[2,48]),e(V,[2,49]),e(V,[2,50]),e(V,[2,51]),e(V,[2,52]),e(V,[2,53]),e(V,[2,54]),e(V,[2,55]),e(V,[2,56]),e(V,[2,57]),e(V,[2,58]),e(V,[2,59]),e(V,[2,60]),e(V,[2,61]),e(V,[2,62]),e(V,[2,63]),e(V,[2,64]),e(V,[2,65]),e(V,[2,66]),e(V,[2,67]),{358:[1,111]},{2:t,3:112,4:r,5:i},{2:t,3:114,4:r,5:i,156:U,200:113,270:P2,294:J,295:y,296:F,297:P,298:G},e(Nr,[2,510],{3:123,353:127,2:t,4:r,5:i,134:Or,135:Cr,187:[1,125],193:[1,124],273:[1,131],274:[1,132],362:[1,133],410:[1,122],477:[1,126],514:[1,130]}),{145:Rr,454:134,455:135},{183:[1,137]},{410:[1,138]},{2:t,3:140,4:r,5:i,130:[1,146],193:[1,141],358:[1,145],402:142,410:[1,139],415:[1,143],514:[1,144]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:147,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(te,J3,{345:208,171:[1,209],198:H3}),e(te,J3,{345:211,198:H3}),{2:t,3:223,4:r,5:i,77:m3,132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,198:[1,214],199:217,200:219,201:218,202:221,209:213,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C,458:212},{2:t,3:225,4:r,5:i},{358:[1,226]},e(k4,[2,1053],{80:227,106:228,107:[1,229]}),e(Ir,[2,1057],{90:230}),{2:t,3:234,4:r,5:i,190:[1,232],193:[1,235],272:[1,231],358:[1,236],410:[1,233]},{358:[1,237]},{2:t,3:240,4:r,5:i,73:238,75:239},e([311,607,768],n,{12:3,13:4,17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,9:242,2:t,4:r,5:i,14:s,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,440:[1,241],441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),{440:[1,243]},{440:[1,244]},{2:t,3:246,4:r,5:i,410:[1,245]},{2:t,3:248,4:r,5:i,199:247},e(a2,[2,316]),{113:249,132:B,301:M},{2:t,3:114,4:r,5:i,113:255,131:H,132:[1,252],143:x,144:250,145:G2,152:L,156:U,181:D,196:254,200:259,201:258,261:256,262:257,269:x4,270:L4,279:251,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:262,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(V,[2,681]),e(V,[2,682]),{2:t,3:171,4:r,5:i,40:264,56:167,77:i1,79:75,89:h,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:263,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,184:99,189:c,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:271,4:r,5:i,113:268,132:B,301:M,449:266,450:267,451:269,452:wr},{2:t,3:272,4:r,5:i,143:Nt,145:Ot,436:273},{2:t,3:171,4:r,5:i,56:167,77:i1,94:276,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{510:[1,277]},{2:t,3:100,4:r,5:i,509:279,511:278},{2:t,3:114,4:r,5:i,156:U,200:280,270:P2,294:J,295:y,296:F,297:P,298:G},{2:t,3:171,4:r,5:i,56:167,77:i1,94:281,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(S3,j3,{186:285,164:[1,284],185:[1,282],187:[1,283],195:Y3}),e(kr,[2,767],{77:[1,287]}),e([2,4,5,10,72,77,78,93,98,107,118,128,131,132,137,143,145,152,154,156,162,164,168,169,179,180,181,183,185,187,195,198,232,244,245,249,251,269,270,271,275,276,278,285,286,287,288,289,290,291,292,294,295,296,297,298,299,300,301,302,303,304,307,308,311,315,317,322,425,429,607,768],[2,152],{149:[1,288],150:[1,289],190:[1,290],191:[1,291],192:[1,292],193:[1,293],194:[1,294]}),e(S,[2,1]),e(S,[2,2]),{6:295,131:[1,444],172:[1,467],243:[1,443],244:[1,378],245:[1,412],249:[1,416],375:[1,409],386:[1,300],407:[1,302],415:[1,554],419:[1,476],421:[1,448],422:[1,514],438:[1,447],440:[1,530],445:[1,347],465:[1,423],469:[1,453],475:[1,346],519:[1,312],520:[1,304],521:[1,404],523:[1,296],524:[1,297],525:[1,298],526:[1,299],527:[1,301],528:[1,303],529:[1,305],530:[1,306],531:[1,307],532:[1,308],533:[1,309],534:[1,310],535:[1,311],536:[1,313],537:[1,314],538:[1,315],539:[1,316],540:[1,317],541:[1,318],542:[1,319],543:[1,320],544:[1,321],545:[1,322],546:[1,323],547:[1,324],548:[1,325],549:[1,326],550:[1,327],551:[1,328],552:[1,329],553:[1,330],554:[1,331],555:[1,332],556:[1,333],557:[1,334],558:[1,335],559:[1,336],560:[1,337],561:[1,338],562:[1,339],563:[1,340],564:[1,341],565:[1,342],566:[1,343],567:[1,344],568:[1,345],569:[1,348],570:[1,349],571:[1,350],572:[1,351],573:[1,352],574:[1,353],575:[1,354],576:[1,355],577:[1,356],578:[1,357],579:[1,358],580:[1,359],581:[1,360],582:[1,361],583:[1,362],584:[1,363],585:[1,364],586:[1,365],587:[1,366],588:[1,367],589:[1,368],590:[1,369],591:[1,370],592:[1,371],593:[1,372],594:[1,373],595:[1,374],596:[1,375],597:[1,376],598:[1,377],599:[1,379],600:[1,380],601:[1,381],602:[1,382],603:[1,383],604:[1,384],605:[1,385],606:[1,386],607:[1,387],608:[1,388],609:[1,389],610:[1,390],611:[1,391],612:[1,392],613:[1,393],614:[1,394],615:[1,395],616:[1,396],617:[1,397],618:[1,398],619:[1,399],620:[1,400],621:[1,401],622:[1,402],623:[1,403],624:[1,405],625:[1,406],626:[1,407],627:[1,408],628:[1,410],629:[1,411],630:[1,413],631:[1,414],632:[1,415],633:[1,417],634:[1,418],635:[1,419],636:[1,420],637:[1,421],638:[1,422],639:[1,424],640:[1,425],641:[1,426],642:[1,427],643:[1,428],644:[1,429],645:[1,430],646:[1,431],647:[1,432],648:[1,433],649:[1,434],650:[1,435],651:[1,436],652:[1,437],653:[1,438],654:[1,439],655:[1,440],656:[1,441],657:[1,442],658:[1,445],659:[1,446],660:[1,449],661:[1,450],662:[1,451],663:[1,452],664:[1,454],665:[1,455],666:[1,456],667:[1,457],668:[1,458],669:[1,459],670:[1,460],671:[1,461],672:[1,462],673:[1,463],674:[1,464],675:[1,465],676:[1,466],677:[1,468],678:[1,469],679:[1,470],680:[1,471],681:[1,472],682:[1,473],683:[1,474],684:[1,475],685:[1,477],686:[1,478],687:[1,479],688:[1,480],689:[1,481],690:[1,482],691:[1,483],692:[1,484],693:[1,485],694:[1,486],695:[1,487],696:[1,488],697:[1,489],698:[1,490],699:[1,491],700:[1,492],701:[1,493],702:[1,494],703:[1,495],704:[1,496],705:[1,497],706:[1,498],707:[1,499],708:[1,500],709:[1,501],710:[1,502],711:[1,503],712:[1,504],713:[1,505],714:[1,506],715:[1,507],716:[1,508],717:[1,509],718:[1,510],719:[1,511],720:[1,512],721:[1,513],722:[1,515],723:[1,516],724:[1,517],725:[1,518],726:[1,519],727:[1,520],728:[1,521],729:[1,522],730:[1,523],731:[1,524],732:[1,525],733:[1,526],734:[1,527],735:[1,528],736:[1,529],737:[1,531],738:[1,532],739:[1,533],740:[1,534],741:[1,535],742:[1,536],743:[1,537],744:[1,538],745:[1,539],746:[1,540],747:[1,541],748:[1,542],749:[1,543],750:[1,544],751:[1,545],752:[1,546],753:[1,547],754:[1,548],755:[1,549],756:[1,550],757:[1,551],758:[1,552],759:[1,553],760:[1,555],761:[1,556],762:[1,557],763:[1,558],764:[1,559],765:[1,560],766:[1,561],767:[1,562]},{1:[2,6]},e(ut,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:563,2:t,4:r,5:i,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(xr,[2,1051]),e(xr,[2,1052]),e(ut,[2,10]),{16:[1,564]},{2:t,3:248,4:r,5:i,199:565},{410:[1,566]},e(V,[2,770]),{77:Ft},{77:[1,568]},{77:Lr},{77:D4},{77:[1,571]},{77:[1,572]},{77:[1,573]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:574,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(te,W3,{355:575,156:X3}),{410:[1,577]},{2:t,3:578,4:r,5:i},{193:[1,579]},{2:t,3:585,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,183:[1,581],436:592,478:580,479:582,480:583,483:584,487:589,498:586,502:588},{130:[1,596],354:593,358:[1,595],415:[1,594]},{113:598,132:B,183:[2,1151],301:M,476:597},e(Dr,[2,1145],{470:599,3:600,2:t,4:r,5:i}),{2:t,3:601,4:r,5:i},{4:[1,602]},{4:[1,603]},e(Nr,[2,511]),e(V,[2,695],{74:[1,604]}),e(qe,[2,696]),{2:t,3:605,4:r,5:i},{2:t,3:248,4:r,5:i,199:606},{2:t,3:607,4:r,5:i},e(te,K3,{403:608,156:Q3}),{410:[1,610]},{2:t,3:611,4:r,5:i},e(te,K3,{403:612,156:Q3}),e(te,K3,{403:613,156:Q3}),{2:t,3:614,4:r,5:i},e(z3,[2,1139]),e(z3,[2,1140]),e(V,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:615,114:632,332:644,2:t,4:r,5:i,53:u,72:f,89:h,99:r2,112:W1,115:A1,116:N1,123:w1,124:_r,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,146:m,154:o2,156:b,170:h2,171:d2,179:D1,180:L1,189:c,271:d,272:v,294:E,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(a2,[2,292]),e(a2,[2,293]),e(a2,[2,294]),e(a2,[2,295]),e(a2,[2,296]),e(a2,[2,297]),e(a2,[2,298]),e(a2,[2,299]),e(a2,[2,300]),e(a2,[2,301]),e(a2,[2,302]),e(a2,[2,303]),e(a2,[2,304]),e(a2,[2,305]),e(a2,[2,306]),e(a2,[2,307]),e(a2,[2,308]),e(a2,[2,309]),{2:t,3:171,4:r,5:i,26:661,27:660,36:656,40:655,56:167,77:i1,79:75,89:h,94:658,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,184:99,189:c,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,268:657,269:f1,270:c1,271:d,272:[1,662],275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:[1,659],295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,344:A,424:193,425:I,429:C},e(a2,[2,313]),e(a2,[2,314]),e(Z3,[2,315],{77:D4}),{77:[1,663]},e([2,4,5,10,53,72,74,76,78,89,93,95,98,99,107,112,115,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],_4,{77:Ft,116:[1,664]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:665,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:666,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:667,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:668,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:669,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,287]),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,231,232,239,244,245,246,247,249,251,253,269,270,271,272,275,276,278,285,286,287,288,289,290,291,292,294,295,296,297,298,299,300,301,302,303,304,305,307,308,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,420,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768,769,770],[2,365]),e(B2,[2,366]),e(B2,[2,367]),e(B2,Vr),e(B2,[2,369]),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,370]),{2:t,3:671,4:r,5:i,131:[1,672],306:670},{2:t,3:673,4:r,5:i},e(B2,[2,376]),e(B2,[2,377]),{2:t,3:674,4:r,5:i,77:V4,113:676,131:H,132:B,143:x,152:L,181:D,196:677,201:679,261:678,299:e1,300:t1,301:M,307:_,424:680,429:C},{77:[1,681]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:682,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,309:683,312:684,313:q3,317:m1,322:S1,424:193,425:I,429:C},{77:[1,686]},{77:[1,687]},e($e,[2,633]),{2:t,3:702,4:r,5:i,77:T3,111:697,113:695,131:H,132:B,143:x,144:692,145:G2,152:L,156:U,181:D,196:694,200:700,201:699,261:696,262:698,270:P2,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,305:[1,690],307:_,424:193,425:I,426:688,427:691,428:693,429:C,432:689},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:703,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:704,4:r,5:i,156:U,200:705,270:P2,294:J,295:y,296:F,297:P,298:G},{77:[2,342]},{77:[2,343]},{77:[2,344]},{77:[2,345]},{77:[2,346]},{77:[2,347]},{77:[2,348]},{77:[2,349]},{77:[2,350]},{77:[2,351]},{2:t,3:711,4:r,5:i,131:Mr,132:Ur,430:706,431:[1,707],433:708},{2:t,3:248,4:r,5:i,199:712},{294:[1,713]},e(te,[2,481]),{2:t,3:248,4:r,5:i,199:714},{231:[1,716],459:715},{231:[2,704]},{2:t,3:223,4:r,5:i,77:m3,132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,199:217,200:219,201:218,202:221,209:717,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},{40:718,79:75,89:h,184:99,189:c},e(yr,[2,1101],{210:719,76:[1,720]}),e(U2,[2,185],{3:721,2:t,4:r,5:i,76:[1,722],154:[1,723]}),e(U2,[2,189],{3:724,2:t,4:r,5:i,76:[1,725]}),e(U2,[2,190],{3:726,2:t,4:r,5:i,76:[1,727]}),e(U2,[2,193]),e(U2,[2,194],{3:728,2:t,4:r,5:i,76:[1,729]}),e(U2,[2,197],{3:730,2:t,4:r,5:i,76:[1,731]}),e([2,4,5,10,72,74,76,78,93,98,118,128,154,162,168,169,183,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],Fr,{77:Ft,116:Pr}),e([2,4,5,10,72,74,76,78,93,98,118,128,162,168,169,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,311,315,607,768],[2,200]),e(V,[2,783]),{2:t,3:248,4:r,5:i,199:733},e(Zt,Gr,{81:734,198:Br}),e(k4,[2,1054]),e(Jr,[2,1067],{108:736,190:[1,737]}),e([10,78,183,311,315,607,768],Gr,{424:193,81:738,117:739,3:740,114:743,144:765,158:775,160:776,2:t,4:r,5:i,72:re,76:ne,77:ie,112:se,115:A1,116:N1,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,198:Br,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,425:I,429:C}),{358:[1,789]},{183:[1,790]},e(V,[2,603],{112:[1,791]}),{410:[1,792]},{183:[1,793]},e(V,[2,607],{112:[1,794],183:[1,795]}),{2:t,3:248,4:r,5:i,199:796},{40:797,74:[1,798],79:75,89:h,184:99,189:c},e(M4,[2,70]),{76:[1,799]},e(V,[2,676]),{11:106,311:[1,800],607:rt,768:zt},e(V,[2,674]),e(V,[2,675]),{2:t,3:801,4:r,5:i},e(V,[2,596]),{146:[1,802]},e([2,4,5,10,53,72,74,76,77,78,89,95,124,128,143,145,146,148,149,152,154,156,181,183,187,189,230,271,272,294,302,307,311,315,340,343,344,348,349,361,373,374,378,379,401,405,406,407,408,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,519,520,521,522,607,768],Fr,{116:Pr}),e(V,[2,624]),e(V,[2,625]),e(V,[2,626]),e(V,Vr,{74:[1,803]}),{77:V4,113:676,131:H,132:B,143:x,152:L,181:D,196:677,201:679,261:678,299:e1,300:t1,301:M,307:_,424:680,429:C},e(x2,[2,325]),e(x2,[2,326]),e(x2,[2,327]),e(x2,[2,328]),e(x2,[2,329]),e(x2,[2,330]),e(x2,[2,331]),e(x2,[2,332],{77:D4}),e(V,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,114:632,332:644,12:804,2:t,4:r,5:i,53:u,72:f,89:h,99:r2,112:W1,115:A1,116:N1,123:w1,124:_r,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,146:m,154:o2,156:b,170:h2,171:d2,179:D1,180:L1,189:c,271:d,272:v,294:E,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(V,[2,684],{74:We}),e(V,[2,685]),e(Hr,[2,363],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(V,[2,686],{74:[1,807]}),e(V,[2,687],{74:[1,808]}),e(qe,[2,692]),e(qe,[2,694]),e(qe,[2,688]),e(qe,[2,689]),{114:814,115:A1,116:N1,124:[1,809],230:jr,434:810,435:811,438:Yr},{2:t,3:815,4:r,5:i},e(te,[2,665]),e(te,[2,666]),e(V,[2,623],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:100,4:r,5:i,509:279,511:816},e(V,[2,764],{74:U4}),e(L2,[2,766]),e(V,[2,769]),e(V,[2,690],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(Gt,j3,{186:818,195:Y3}),e(Gt,j3,{186:819,195:Y3}),e(Gt,j3,{186:820,195:Y3}),e(qt,[2,1097],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,188:821,174:822,257:823,94:824,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),{77:[1,826],131:H,196:825},{2:t,3:100,4:r,5:i,509:279,511:827},e(Bt,[2,153]),e(Bt,[2,154]),e(Bt,[2,155]),e(Bt,[2,156]),e(Bt,[2,157]),e(Bt,[2,158]),e(Bt,[2,159]),e(S,[2,3]),e(S,[2,784]),e(S,[2,785]),e(S,[2,786]),e(S,[2,787]),e(S,[2,788]),e(S,[2,789]),e(S,[2,790]),e(S,[2,791]),e(S,[2,792]),e(S,[2,793]),e(S,[2,794]),e(S,[2,795]),e(S,[2,796]),e(S,[2,797]),e(S,[2,798]),e(S,[2,799]),e(S,[2,800]),e(S,[2,801]),e(S,[2,802]),e(S,[2,803]),e(S,[2,804]),e(S,[2,805]),e(S,[2,806]),e(S,[2,807]),e(S,[2,808]),e(S,[2,809]),e(S,[2,810]),e(S,[2,811]),e(S,[2,812]),e(S,[2,813]),e(S,[2,814]),e(S,[2,815]),e(S,[2,816]),e(S,[2,817]),e(S,[2,818]),e(S,[2,819]),e(S,[2,820]),e(S,[2,821]),e(S,[2,822]),e(S,[2,823]),e(S,[2,824]),e(S,[2,825]),e(S,[2,826]),e(S,[2,827]),e(S,[2,828]),e(S,[2,829]),e(S,[2,830]),e(S,[2,831]),e(S,[2,832]),e(S,[2,833]),e(S,[2,834]),e(S,[2,835]),e(S,[2,836]),e(S,[2,837]),e(S,[2,838]),e(S,[2,839]),e(S,[2,840]),e(S,[2,841]),e(S,[2,842]),e(S,[2,843]),e(S,[2,844]),e(S,[2,845]),e(S,[2,846]),e(S,[2,847]),e(S,[2,848]),e(S,[2,849]),e(S,[2,850]),e(S,[2,851]),e(S,[2,852]),e(S,[2,853]),e(S,[2,854]),e(S,[2,855]),e(S,[2,856]),e(S,[2,857]),e(S,[2,858]),e(S,[2,859]),e(S,[2,860]),e(S,[2,861]),e(S,[2,862]),e(S,[2,863]),e(S,[2,864]),e(S,[2,865]),e(S,[2,866]),e(S,[2,867]),e(S,[2,868]),e(S,[2,869]),e(S,[2,870]),e(S,[2,871]),e(S,[2,872]),e(S,[2,873]),e(S,[2,874]),e(S,[2,875]),e(S,[2,876]),e(S,[2,877]),e(S,[2,878]),e(S,[2,879]),e(S,[2,880]),e(S,[2,881]),e(S,[2,882]),e(S,[2,883]),e(S,[2,884]),e(S,[2,885]),e(S,[2,886]),e(S,[2,887]),e(S,[2,888]),e(S,[2,889]),e(S,[2,890]),e(S,[2,891]),e(S,[2,892]),e(S,[2,893]),e(S,[2,894]),e(S,[2,895]),e(S,[2,896]),e(S,[2,897]),e(S,[2,898]),e(S,[2,899]),e(S,[2,900]),e(S,[2,901]),e(S,[2,902]),e(S,[2,903]),e(S,[2,904]),e(S,[2,905]),e(S,[2,906]),e(S,[2,907]),e(S,[2,908]),e(S,[2,909]),e(S,[2,910]),e(S,[2,911]),e(S,[2,912]),e(S,[2,913]),e(S,[2,914]),e(S,[2,915]),e(S,[2,916]),e(S,[2,917]),e(S,[2,918]),e(S,[2,919]),e(S,[2,920]),e(S,[2,921]),e(S,[2,922]),e(S,[2,923]),e(S,[2,924]),e(S,[2,925]),e(S,[2,926]),e(S,[2,927]),e(S,[2,928]),e(S,[2,929]),e(S,[2,930]),e(S,[2,931]),e(S,[2,932]),e(S,[2,933]),e(S,[2,934]),e(S,[2,935]),e(S,[2,936]),e(S,[2,937]),e(S,[2,938]),e(S,[2,939]),e(S,[2,940]),e(S,[2,941]),e(S,[2,942]),e(S,[2,943]),e(S,[2,944]),e(S,[2,945]),e(S,[2,946]),e(S,[2,947]),e(S,[2,948]),e(S,[2,949]),e(S,[2,950]),e(S,[2,951]),e(S,[2,952]),e(S,[2,953]),e(S,[2,954]),e(S,[2,955]),e(S,[2,956]),e(S,[2,957]),e(S,[2,958]),e(S,[2,959]),e(S,[2,960]),e(S,[2,961]),e(S,[2,962]),e(S,[2,963]),e(S,[2,964]),e(S,[2,965]),e(S,[2,966]),e(S,[2,967]),e(S,[2,968]),e(S,[2,969]),e(S,[2,970]),e(S,[2,971]),e(S,[2,972]),e(S,[2,973]),e(S,[2,974]),e(S,[2,975]),e(S,[2,976]),e(S,[2,977]),e(S,[2,978]),e(S,[2,979]),e(S,[2,980]),e(S,[2,981]),e(S,[2,982]),e(S,[2,983]),e(S,[2,984]),e(S,[2,985]),e(S,[2,986]),e(S,[2,987]),e(S,[2,988]),e(S,[2,989]),e(S,[2,990]),e(S,[2,991]),e(S,[2,992]),e(S,[2,993]),e(S,[2,994]),e(S,[2,995]),e(S,[2,996]),e(S,[2,997]),e(S,[2,998]),e(S,[2,999]),e(S,[2,1e3]),e(S,[2,1001]),e(S,[2,1002]),e(S,[2,1003]),e(S,[2,1004]),e(S,[2,1005]),e(S,[2,1006]),e(S,[2,1007]),e(S,[2,1008]),e(S,[2,1009]),e(S,[2,1010]),e(S,[2,1011]),e(S,[2,1012]),e(S,[2,1013]),e(S,[2,1014]),e(S,[2,1015]),e(S,[2,1016]),e(S,[2,1017]),e(S,[2,1018]),e(S,[2,1019]),e(S,[2,1020]),e(S,[2,1021]),e(S,[2,1022]),e(S,[2,1023]),e(S,[2,1024]),e(S,[2,1025]),e(S,[2,1026]),e(S,[2,1027]),e(S,[2,1028]),e(S,[2,1029]),e(S,[2,1030]),e(S,[2,1031]),e(S,[2,1032]),e(S,[2,1033]),e(S,[2,1034]),e(S,[2,1035]),e(S,[2,1036]),e(S,[2,1037]),e(S,[2,1038]),e(S,[2,1039]),e(S,[2,1040]),e(S,[2,1041]),e(S,[2,1042]),e(S,[2,1043]),e(S,[2,1044]),e(S,[2,1045]),e(S,[2,1046]),e(S,[2,1047]),e(S,[2,1048]),e(S,[2,1049]),e(S,[2,1050]),e(ut,[2,7]),e(ut,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:828,2:t,4:r,5:i,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),{401:[1,832],406:[1,829],407:[1,830],408:[1,831]},{2:t,3:833,4:r,5:i},e(Gt,[2,1121],{293:834,771:836,78:[1,835],164:[1,838],185:[1,837]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:839,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:840,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{78:[1,841]},{2:t,3:842,4:r,5:i,132:[1,843]},{2:t,3:844,4:r,5:i,132:[1,845]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:846,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:847,4:r,5:i,99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:848,4:r,5:i},{154:[1,849]},e($3,W3,{355:850,156:X3}),{230:[1,851]},{2:t,3:852,4:r,5:i},e(V,[2,739],{74:Wr}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:854,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(L2,[2,742]),e(Xr,[2,1153],{424:193,481:855,144:856,139:y4,141:y4,145:G2,425:I,429:C}),{139:[1,857],141:[1,858]},e(e4,Kr,{495:860,498:861,77:[1,859],137:Ct}),e(t4,[2,1177],{499:862,132:[1,863]}),e(ft,[2,1181],{501:864,502:865,152:pt}),e(ft,[2,757]),e(Qr,[2,749]),{2:t,3:866,4:r,5:i,131:[1,867]},{2:t,3:868,4:r,5:i},{2:t,3:869,4:r,5:i},e(te,W3,{355:870,156:X3}),e(te,W3,{355:871,156:X3}),e(z3,[2,500]),e(z3,[2,501]),{183:[1,872]},{183:[2,1152]},e(F4,[2,1147],{471:873,474:874,137:[1,875]}),e(Dr,[2,1146]),e($t,zr,{515:876,95:Zr,230:[1,877],519:qr,520:$r,521:en}),{76:[1,882]},{76:[1,883]},{145:Rr,455:884},{4:e3,7:888,76:[1,886],277:885,392:887,394:t3},e(V,[2,465],{128:[1,891]}),e(V,[2,588]),{2:t,3:892,4:r,5:i},{303:[1,893]},e($3,K3,{403:894,156:Q3}),e(V,[2,602]),{2:t,3:248,4:r,5:i,199:896,404:895},{2:t,3:248,4:r,5:i,199:896,404:897},e(V,[2,782]),e(ut,[2,678],{443:898,315:[1,899]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:900,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:901,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:902,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:903,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:904,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:905,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:906,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:907,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:908,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:909,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:910,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:911,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:912,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:913,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:914,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:915,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:916,4:r,5:i,77:[1,918],131:H,156:U,196:917,200:919,270:P2,294:J,295:y,296:F,297:P,298:G},{2:t,3:920,4:r,5:i,77:[1,922],131:H,156:U,196:921,200:923,270:P2,294:J,295:y,296:F,297:P,298:G},e(Jt,[2,449],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:924,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),e(Jt,[2,450],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:925,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),e(Jt,[2,451],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:926,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),e(Jt,[2,452],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:927,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),e(Jt,tn,{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:928,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:929,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:930,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(Jt,[2,454],{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:931,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:932,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:933,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{164:[1,935],166:[1,937],333:934,339:[1,936]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:938,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:939,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:702,4:r,5:i,77:[1,940],111:943,145:rn,156:U,200:944,202:942,270:P2,294:J,295:y,296:F,297:P,298:G,334:941},{99:[1,946],302:[1,947]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:948,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:949,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:950,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{4:e3,7:888,277:951,392:887,394:t3},e(nn,[2,88]),e(nn,[2,89]),{78:[1,952]},{78:[1,953]},{78:[1,954]},{78:[1,955],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(te,J3,{345:211,77:Lr,198:H3}),{78:[2,1117]},{78:[2,1118]},{134:Or,135:Cr},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:956,152:L,154:o1,156:U,158:170,164:[1,958],179:l1,180:u1,181:D,185:[1,957],196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:959,4:r,5:i,149:sn,180:[1,961]},e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,118,122,128,129,130,131,132,134,135,137,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,319,335,336,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,425],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,337:X1}),e(r4,[2,426],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,180:L1,317:I1,321:C1}),e(r4,[2,427],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,180:L1,317:I1,321:C1}),e(P4,[2,428],{114:632,332:644,321:C1}),e(P4,[2,429],{114:632,332:644,321:C1}),e(B2,[2,374]),e(B2,[2,1123]),e(B2,[2,1124]),e(B2,[2,375]),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,231,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,371]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:962,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e($e,[2,629]),e($e,[2,630]),e($e,[2,631]),e($e,[2,632]),e($e,[2,634]),{40:963,79:75,89:h,184:99,189:c},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,309:964,312:684,313:q3,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{310:965,311:an,312:966,313:q3,315:on},e(G4,[2,381]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:968,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:969,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{4:e3,7:888,277:970,392:887,394:t3},e($e,[2,635]),{74:[1,972],305:[1,971]},e($e,[2,651]),e(ln,[2,658]),e(bt,[2,636]),e(bt,[2,637]),e(bt,[2,638]),e(bt,[2,639]),e(bt,[2,640]),e(bt,[2,641]),e(bt,[2,642]),e(bt,[2,643]),e(bt,[2,644]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:973,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e([2,4,5,10,53,72,74,76,78,89,93,95,98,99,107,112,115,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,431,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],_4,{77:Ft,116:un}),{74:We,305:[1,975]},e(Z3,[2,319],{77:Ft}),e(a2,[2,320]),{74:[1,977],431:[1,976]},e($e,[2,648]),e(v3,[2,653]),{152:[1,978]},{152:[1,979]},{152:[1,980]},{40:985,77:[1,984],79:75,89:h,143:x,144:988,145:G2,149:A3,152:L,181:D,184:99,189:c,201:989,307:_,346:981,347:982,348:[1,983],349:N3,424:193,425:I,429:C},e(te,J3,{345:990,198:H3}),{77:Rt,143:x,144:988,145:G2,149:A3,152:L,181:D,201:989,307:_,346:991,347:992,349:N3,424:193,425:I,429:C},{230:[1,995],460:994},{2:t,3:223,4:r,5:i,77:[1,997],132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,199:217,200:219,201:218,202:221,209:996,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},{231:[2,705]},{78:[1,998]},e(U2,[2,1103],{211:999,3:1e3,2:t,4:r,5:i}),e(yr,[2,1102]),e(U2,[2,183]),{2:t,3:1001,4:r,5:i},{212:[1,1002]},e(U2,[2,187]),{2:t,3:1003,4:r,5:i},e(U2,[2,191]),{2:t,3:1004,4:r,5:i},e(U2,[2,195]),{2:t,3:1005,4:r,5:i},e(U2,[2,198]),{2:t,3:1006,4:r,5:i},{2:t,3:1007,4:r,5:i},{148:[1,1008]},e(n4,[2,172],{82:1009,183:[1,1010]}),{2:t,3:223,4:r,5:i,132:[1,1015],143:x,145:[1,1016],152:L,156:U,181:D,199:1011,200:1012,201:1013,202:1014,270:P2,294:J,295:y,296:F,297:P,298:G,307:_},{2:t,3:1021,4:r,5:i,109:1017,110:1018,111:1019,112:fn},e(Jr,[2,1068]),e(et,[2,1059],{91:1022,182:1023,183:[1,1024]}),e(Ir,[2,1058],{153:1025,179:Xe,180:Ke,181:Qe}),e([2,4,5,10,72,74,76,78,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,198,244,245,285,286,287,288,289,290,291,292,311,315,425,429,607,768],[2,90],{77:[1,1029]}),{119:[1,1030]},e(m2,[2,93]),{2:t,3:1031,4:r,5:i},e(m2,[2,95]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1032,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1033,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,112:se,114:743,115:A1,116:N1,117:1035,118:ae,122:oe,123:le,124:ue,125:1034,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{77:[1,1036]},{77:[1,1037]},{77:[1,1038]},{77:[1,1039]},e(m2,[2,104]),e(m2,[2,105]),e(m2,[2,106]),e(m2,[2,107]),e(m2,[2,108]),e(m2,[2,109]),{2:t,3:1040,4:r,5:i},{2:t,3:1041,4:r,5:i,133:[1,1042]},e(m2,[2,113]),e(m2,[2,114]),e(m2,[2,115]),e(m2,[2,116]),e(m2,[2,117]),e(m2,[2,118]),{2:t,3:1043,4:r,5:i,77:V4,113:676,131:H,132:B,143:x,152:L,181:D,196:677,201:679,261:678,299:e1,300:t1,301:M,307:_,424:680,429:C},{145:[1,1044]},{77:[1,1045]},{145:[1,1046]},e(m2,[2,123]),{77:[1,1047]},{2:t,3:1048,4:r,5:i},{77:[1,1049]},{77:[1,1050]},{77:[1,1051]},{77:[1,1052]},{77:[1,1053],164:[1,1054]},{77:[1,1055]},{77:[1,1056]},{77:[1,1057]},{77:[1,1058]},{77:[1,1059]},{77:[1,1060]},{77:[1,1061]},{77:[1,1062]},{77:[1,1063]},{77:[2,1083]},{77:[2,1084]},{2:t,3:248,4:r,5:i,199:1064},{2:t,3:248,4:r,5:i,199:1065},{113:1066,132:B,301:M},e(V,[2,605],{112:[1,1067]}),{2:t,3:248,4:r,5:i,199:1068},{113:1069,132:B,301:M},{2:t,3:1070,4:r,5:i},e(V,[2,702]),e(V,[2,68]),{2:t,3:240,4:r,5:i,75:1071},{77:[1,1072]},e(V,[2,683]),e(V,[2,595]),{2:t,3:1021,4:r,5:i,111:1075,143:O3,145:C3,147:1073,341:1074,342:1076},{144:1079,145:G2,424:193,425:I,429:C},e(V,[2,680]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1080,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(Jt,tn,{259:148,200:149,260:150,111:151,258:152,196:153,261:154,113:155,262:156,201:157,202:158,263:159,264:160,265:161,144:163,266:164,267:165,56:167,158:170,3:171,424:193,94:1081,2:t,4:r,5:i,77:i1,131:H,132:B,137:s1,143:x,145:r1,149:a1,152:L,154:o1,156:U,179:l1,180:u1,181:D,244:j,245:Y,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,425:I,429:C}),{113:1082,132:B,301:M},{2:t,3:271,4:r,5:i,451:1083,452:wr},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1085,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,230:jr,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C,434:1084,438:Yr},e(V,[2,660]),{114:1087,115:A1,116:N1,124:[1,1086]},e(V,[2,672]),e(V,[2,673]),{2:t,3:1089,4:r,5:i,77:cn,131:hn,437:1088},{114:814,115:A1,116:N1,124:[1,1092],435:1093},e(V,[2,763],{74:U4}),{2:t,3:100,4:r,5:i,509:1094},{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,174:1095,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:823,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,174:1096,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:823,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,174:1097,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:823,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(qt,[2,151]),e(qt,[2,1098],{74:r3}),e(It,[2,277]),e(It,[2,284],{114:632,332:644,3:1100,113:1102,2:t,4:r,5:i,76:[1,1099],99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,131:[1,1101],132:B,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,301:M,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(S3,[2,1099],{197:1103,769:[1,1104]}),{131:H,196:1105},{74:U4,78:[1,1106]},e(ut,[2,11]),{148:[1,1107],190:[1,1108]},{190:[1,1109]},{190:[1,1110]},{190:[1,1111]},e(V,[2,584],{76:[1,1113],77:[1,1112]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1114,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(B2,[2,353]),e(Gt,[2,1122]),e(Gt,[2,1119]),e(Gt,[2,1120]),{74:We,78:[1,1115]},{74:We,78:[1,1116]},e(B2,[2,356]),{74:[1,1117]},{74:[1,1118]},{74:[1,1119]},{74:[1,1120]},{74:[1,1121],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(B2,[2,362]),e(V,[2,589]),{303:[1,1122]},{2:t,3:1123,4:r,5:i,113:1124,132:B,301:M},{2:t,3:248,4:r,5:i,199:1125},{230:[1,1126]},{2:t,3:585,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,436:592,479:1127,480:583,483:584,487:589,498:586,502:588},e(V,[2,740],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(L2,[2,1155],{482:1128,488:1129,76:B4}),e(Xr,[2,1154]),{2:t,3:1133,4:r,5:i,132:Pt,137:Ct,144:1132,145:G2,152:pt,424:193,425:I,429:C,480:1131,498:586,502:588},{2:t,3:1133,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,436:592,480:1135,483:1134,487:589,498:586,502:588},{2:t,3:585,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,436:592,478:1136,479:582,480:583,483:584,487:589,498:586,502:588},e(t4,[2,1173],{496:1137,132:[1,1138]}),e(e4,[2,1172]),e(ft,[2,1179],{500:1139,502:1140,152:pt}),e(t4,[2,1178]),e(ft,[2,756]),e(ft,[2,1182]),e(e4,[2,759]),e(e4,[2,760]),e(ft,[2,758]),e(Qr,[2,750]),{2:t,3:248,4:r,5:i,199:1141},{2:t,3:248,4:r,5:i,199:1142},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1143,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(dn,[2,1149],{472:1144,113:1145,132:B,301:M}),e(F4,[2,1148]),{2:t,3:1146,4:r,5:i},{340:pn,343:bn,344:En,516:1147},{2:t,3:248,4:r,5:i,199:1151},e($t,[2,775]),e($t,[2,776]),e($t,[2,777]),{129:[1,1152]},{271:[1,1153]},{271:[1,1154]},e(qe,[2,697]),e(qe,[2,698],{124:[1,1155]}),{4:e3,7:888,277:1156,392:887,394:t3},e([2,4,10,53,72,74,76,77,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,551],{5:[1,1157]}),e([2,5,10,53,72,74,76,78,89,93,95,98,99,107,112,115,116,118,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,230,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,302,305,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,348,361,373,374,378,379,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,548],{4:[1,1159],77:[1,1158]}),{77:[1,1160]},e(n3,[2,4]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1161,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(V,[2,597]),e($3,[2,577]),{2:t,3:1162,4:r,5:i,113:1163,132:B,301:M},e(V,[2,573],{74:gn}),e(qe,[2,575]),e(V,[2,622],{74:gn}),e(V,[2,677]),e(V,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:1165,2:t,4:r,5:i,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(i4,[2,385],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,321:C1,322:_1,323:F1,324:P1}),e(P4,[2,386],{114:632,332:644,321:C1}),e(i4,[2,387],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,321:C1,322:_1,323:F1,324:P1}),e(mn,[2,388],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,319:[1,1166],321:C1,322:_1,323:F1,324:P1}),e(mn,[2,390],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,319:[1,1167],321:C1,322:_1,323:F1,324:P1}),e(a2,[2,392],{114:632,332:644}),e(r4,[2,393],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,180:L1,317:I1,321:C1}),e(r4,[2,394],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,180:L1,317:I1,321:C1}),e(J4,[2,395],{114:632,332:644,115:A1,116:N1,123:w1,136:R1,317:I1,321:C1}),e(J4,[2,396],{114:632,332:644,115:A1,116:N1,123:w1,136:R1,317:I1,321:C1}),e(J4,[2,397],{114:632,332:644,115:A1,116:N1,123:w1,136:R1,317:I1,321:C1}),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,112,118,122,123,124,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,179,180,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,316,318,319,320,322,323,324,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,398],{114:632,332:644,115:A1,116:N1,136:R1,317:I1,321:C1}),e(s4,[2,399],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,179:D1,180:L1,317:I1,321:C1,322:_1}),e(s4,[2,400],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,179:D1,180:L1,317:I1,321:C1,322:_1}),e(s4,[2,401],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,179:D1,180:L1,317:I1,321:C1,322:_1}),e(s4,[2,402],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,179:D1,180:L1,317:I1,321:C1,322:_1}),e(Z3,[2,403],{77:Ft}),e(a2,[2,404]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1168,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,406]),e(Z3,[2,407],{77:Ft}),e(a2,[2,408]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1169,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,410]),e(Et,[2,411],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,412],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,413],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,414],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e([2,4,5,10,53,72,89,99,124,139,140,146,154,156,170,171,189,271,272,294,311,315,325,326,327,328,329,330,331,335,336,338,340,343,344,401,405,406,409,411,413,414,422,423,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,522,607,768],Sn,{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,416],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,417],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,418],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,419],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(Et,[2,420],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),{77:[1,1170]},{77:[2,455]},{77:[2,456]},{77:[2,457]},e(H4,[2,423],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,337:X1}),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,107,118,122,128,129,130,131,132,134,135,137,143,145,146,148,149,150,152,156,162,164,166,168,169,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,319,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,424],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1}),{2:t,3:171,4:r,5:i,40:1171,56:167,77:i1,78:[1,1173],79:75,89:h,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1172,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,184:99,189:c,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,437]),e(a2,[2,439]),e(a2,[2,446]),e(a2,[2,447]),{2:t,3:674,4:r,5:i,77:[1,1174]},{2:t,3:702,4:r,5:i,77:[1,1175],111:943,145:rn,156:U,200:944,202:1177,270:P2,294:J,295:y,296:F,297:P,298:G,334:1176},e(a2,[2,444]),e(H4,[2,441],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,337:X1}),e(H4,[2,442],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,337:X1}),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,99,107,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,189,198,206,208,222,223,224,225,226,227,228,229,232,239,244,245,246,247,249,251,271,272,285,286,287,288,289,290,291,292,294,301,305,311,313,314,315,319,325,326,327,328,329,330,331,335,336,337,338,340,343,344,401,405,406,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,469,475,510,512,513,522,607,768],[2,443],{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1}),e(a2,[2,445]),e(a2,[2,310]),e(a2,[2,311]),e(a2,[2,312]),e(a2,[2,430]),{74:We,78:[1,1178]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1179,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1180,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,Tn),e(j4,[2,290]),e(a2,[2,286]),{78:[1,1182],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1183]},{310:1184,311:an,312:966,313:q3,315:on},{311:[1,1185]},e(G4,[2,380]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1186,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,314:[1,1187],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{76:[1,1188],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{74:[1,1189]},e($e,[2,649]),{2:t,3:702,4:r,5:i,77:T3,111:697,113:695,131:H,132:B,143:x,144:692,145:G2,152:L,156:U,181:D,196:694,200:700,201:699,261:696,262:698,270:P2,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,305:[1,1190],307:_,424:193,425:I,427:1191,428:693,429:C},{78:[1,1192],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:1193,4:r,5:i,149:sn},e(a2,[2,373]),e($e,[2,646]),{2:t,3:711,4:r,5:i,131:Mr,132:Ur,431:[1,1194],433:1195},{2:t,3:702,4:r,5:i,77:T3,111:697,113:695,131:H,132:B,143:x,144:692,145:G2,152:L,156:U,181:D,196:694,200:700,201:699,261:696,262:698,270:P2,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_,424:193,425:I,427:1196,428:693,429:C},{2:t,3:702,4:r,5:i,77:T3,111:697,113:695,131:H,132:B,143:x,144:692,145:G2,152:L,156:U,181:D,196:694,200:700,201:699,261:696,262:698,270:P2,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_,424:193,425:I,427:1197,428:693,429:C},{2:t,3:702,4:r,5:i,77:T3,111:697,113:695,131:H,132:B,143:x,144:692,145:G2,152:L,156:U,181:D,196:694,200:700,201:699,261:696,262:698,270:P2,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_,424:193,425:I,427:1198,428:693,429:C},{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1199,424:193,425:I,429:C},e(W2,[2,467],{74:gt}),{149:A3,346:1201,349:N3},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1205,100:1202,111:1204,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,350:1203,424:193,425:I,429:C},e(W2,[2,475]),e(vn,[2,478]),e(vn,[2,479]),e(i3,[2,483]),e(i3,[2,484]),{2:t,3:248,4:r,5:i,199:1206},{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1207,424:193,425:I,429:C},e(W2,[2,471],{74:gt}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1205,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,350:1203,424:193,425:I,429:C},{313:An,461:1208,463:1209,464:1210},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1212,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{230:[2,706]},{2:t,3:223,4:r,5:i,40:718,77:m3,79:75,89:h,132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,184:99,189:c,199:217,200:219,201:218,202:221,209:1213,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},e(U2,[2,181],{3:1214,2:t,4:r,5:i,76:[1,1215]}),e(U2,[2,182]),e(U2,[2,1104]),e(U2,[2,184]),e(U2,[2,186]),e(U2,[2,188]),e(U2,[2,192]),e(U2,[2,196]),e(U2,[2,199]),e([2,4,5,10,53,72,74,76,77,78,89,93,95,98,118,124,128,143,145,146,148,149,152,154,156,162,168,169,181,183,187,189,206,208,222,223,224,225,226,227,228,229,230,231,232,249,251,271,272,294,302,307,311,315,340,343,344,348,349,361,373,374,378,379,401,405,406,407,408,409,411,413,414,422,423,425,429,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,519,520,521,522,607,768],[2,201]),{2:t,3:1216,4:r,5:i},e(mt,[2,1055],{83:1217,92:1218,93:[1,1219],98:[1,1220]}),{2:t,3:223,4:r,5:i,77:[1,1222],132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,199:217,200:219,201:218,202:221,203:1221,209:1223,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},e(Zt,[2,164]),e(Zt,[2,165]),e(Zt,[2,166]),e(Zt,[2,167]),e(Zt,[2,168]),{2:t,3:674,4:r,5:i},e(k4,[2,83],{74:[1,1224]}),e(a4,[2,85]),e(a4,[2,86]),{113:1225,132:B,301:M},e([10,72,74,78,93,98,118,124,128,162,168,169,183,198,206,208,222,223,224,225,226,227,228,229,232,249,251,311,315,607,768],_4,{116:un}),e(et,[2,73]),e(et,[2,1060]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1226,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(m2,[2,126]),e(m2,[2,144]),e(m2,[2,145]),e(m2,[2,146]),{2:t,3:171,4:r,5:i,56:167,77:i1,78:[2,1075],94:265,111:151,113:155,127:1227,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1228,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{77:[1,1229]},e(m2,[2,94]),e([2,4,5,10,72,74,76,77,78,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,198,244,245,285,286,287,288,289,290,291,292,311,315,425,429,607,768],[2,96],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e([2,4,5,10,72,74,76,77,78,112,118,122,124,128,129,130,131,132,134,135,137,139,140,143,145,146,148,149,150,152,154,156,162,164,166,168,169,170,171,172,173,175,181,183,185,187,198,244,245,285,286,287,288,289,290,291,292,311,315,425,429,607,768],[2,97],{114:632,332:644,99:r2,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1230],112:se,114:743,115:A1,116:N1,117:1231,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},e(q2,[2,1071],{153:1025,179:Xe,180:Ke,181:Qe}),{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,112:se,114:743,115:A1,116:N1,117:1233,118:ae,122:oe,123:le,124:ue,126:1232,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1234,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1235,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1236,4:r,5:i},e(m2,[2,110]),e(m2,[2,111]),e(m2,[2,112]),e(m2,[2,119]),{2:t,3:1237,4:r,5:i},{2:t,3:1021,4:r,5:i,111:1075,143:O3,145:C3,147:1238,341:1074,342:1076},{2:t,3:1239,4:r,5:i},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1240,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(m2,[2,125]),e(q2,[2,1077],{155:1241}),e(q2,[2,1079],{157:1242}),e(q2,[2,1081],{159:1243}),e(q2,[2,1085],{161:1244}),e(wt,s3,{163:1245,178:1246}),{77:[1,1247]},e(q2,[2,1087],{165:1248}),e(q2,[2,1089],{167:1249}),e(wt,s3,{178:1246,163:1250}),e(wt,s3,{178:1246,163:1251}),e(wt,s3,{178:1246,163:1252}),e(wt,s3,{178:1246,163:1253}),{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,112:se,114:743,115:A1,116:N1,117:1254,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,174:1255,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:823,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(Nn,[2,1091],{176:1256}),e(V,[2,615],{183:[1,1257]}),e(V,[2,611],{183:[1,1258]}),e(V,[2,604]),{113:1259,132:B,301:M},e(V,[2,613],{183:[1,1260]}),e(V,[2,608]),e(V,[2,609],{112:[1,1261]}),e(M4,[2,69]),{40:1262,79:75,89:h,184:99,189:c},e(V,[2,459],{74:o4,128:[1,1263]}),e(l4,[2,460]),{124:[1,1265]},{2:t,3:1266,4:r,5:i},e(te,[2,1125]),e(te,[2,1126]),e(V,[2,627]),e(Hr,[2,364],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(Et,Sn,{114:632,332:644,112:W1,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,337:X1}),e(qe,[2,691]),e(qe,[2,693]),e(V,[2,659]),e(V,[2,661],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1267,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1089,4:r,5:i,77:cn,131:hn,437:1268},e(R3,[2,668]),e(R3,[2,669]),e(R3,[2,670]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1269,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1270,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{114:1087,115:A1,116:N1,124:[1,1271]},e(L2,[2,765]),e(qt,[2,148],{74:r3}),e(qt,[2,149],{74:r3}),e(qt,[2,150],{74:r3}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:1272,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1273,4:r,5:i,113:1275,131:[1,1274],132:B,301:M},e(It,[2,279]),e(It,[2,281]),e(It,[2,283]),e(S3,[2,160]),e(S3,[2,1100]),{78:[1,1276]},e(kr,[2,768]),{2:t,3:1277,4:r,5:i},{2:t,3:1278,4:r,5:i},{2:t,3:1280,4:r,5:i,389:1279},{2:t,3:1280,4:r,5:i,389:1281},{2:t,3:1282,4:r,5:i},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1283,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1284,4:r,5:i},{74:We,78:[1,1285]},e(B2,[2,354]),e(B2,[2,355]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1286,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1287,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1288,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1289,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1290,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e($3,[2,513]),e(V,Y4,{412:1291,76:W4,77:[1,1292]}),e(V,Y4,{412:1294,76:W4}),{77:[1,1295]},{2:t,3:248,4:r,5:i,199:1296},e(L2,[2,741]),e(L2,[2,743]),e(L2,[2,1156]),{143:Nt,145:Ot,436:1297},e(On,[2,1157],{424:193,484:1298,144:1299,145:G2,425:I,429:C}),{76:B4,139:[2,1161],486:1300,488:1301},e([10,74,76,78,132,139,145,152,311,315,425,429,607,768],Kr,{495:860,498:861,137:Ct}),e(L2,[2,746]),e(L2,y4),{74:Wr,78:[1,1302]},e(ft,[2,1175],{497:1303,502:1304,152:pt}),e(t4,[2,1174]),e(ft,[2,755]),e(ft,[2,1180]),e(V,[2,499],{77:[1,1305]}),{76:[1,1307],77:[1,1306]},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,148:[1,1308],154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(W2,Cn,{79:75,184:99,473:1309,40:1312,89:h,146:Rn,189:c,475:In}),e(dn,[2,1150]),e(F4,[2,733]),{230:[1,1313]},e(X4,[2,779]),e(X4,[2,780]),e(X4,[2,781]),e($t,zr,{515:1314,95:Zr,519:qr,520:$r,521:en}),e($t,[2,778]),e(V,[2,317]),e(V,[2,318]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1315,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(qe,[2,699],{124:[1,1316]}),e(n3,[2,550]),{131:[1,1318],393:1317,395:[1,1319]},e(n3,[2,5]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1205,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,350:1320,424:193,425:I,429:C},e(V,[2,464],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(V,[2,598]),e(V,[2,599]),{2:t,3:248,4:r,5:i,199:1321},e(V,[2,679]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1322,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1323,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{78:[1,1324],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1325],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:171,4:r,5:i,40:1326,56:167,77:i1,79:75,89:h,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1327,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,184:99,189:c,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{78:[1,1328]},{74:We,78:[1,1329]},e(a2,[2,435]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1330,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,40:1331,56:167,77:i1,78:[1,1333],79:75,89:h,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1332,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,184:99,189:c,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,438]),e(a2,[2,440]),e(a2,K4,{280:1334,281:Q4}),{78:[1,1336],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1337],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:1338,4:r,5:i,180:[1,1339]},e($e,[2,628]),e(a2,[2,372]),{311:[1,1340]},e(a2,[2,379]),{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,311:[2,383],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1341,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{4:e3,7:888,277:1342,392:887,394:t3},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1343,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e($e,[2,650]),e(ln,[2,657]),e(bt,[2,645]),e(j4,Tn),e($e,[2,647]),e(v3,[2,652]),e(v3,[2,654]),e(v3,[2,655]),e(v3,[2,656]),e(W2,[2,466],{74:gt}),{77:[1,1345],143:x,144:1346,145:G2,152:L,181:D,201:1347,307:_,424:193,425:I,429:C},e(W2,[2,472]),{74:St,78:[1,1348]},{74:z4,78:[1,1350]},e([74,78,99,112,115,116,123,124,133,136,138,139,140,141,142,154,170,171,179,180,316,317,318,320,321,322,323,324,325,326,327,328,329,330,331,335,336,337,338],wn),e(K2,[2,488],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{40:1354,77:Rt,79:75,89:h,143:x,144:988,145:G2,149:A3,152:L,181:D,184:99,189:c,201:989,307:_,346:1352,347:1353,349:N3,424:193,425:I,429:C},e(W2,[2,470],{74:gt}),e(V,[2,727],{462:1355,463:1356,464:1357,313:An,469:[1,1358]}),e(Q2,[2,711]),e(Q2,[2,712]),{154:[1,1360],465:[1,1359]},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,313:[2,708],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1361]},e(U2,[2,179]),{2:t,3:1362,4:r,5:i},e(V,[2,583]),e(kn,[2,239],{84:1363,128:[1,1364]}),e(mt,[2,1056]),{77:[1,1365]},{77:[1,1366]},e(n4,[2,169],{204:1367,215:1369,205:1370,216:1371,221:1374,74:xn,206:u4,208:f4,222:c4,223:I3,224:w3,225:k3,226:x3,227:L3,228:D3,229:_3}),{2:t,3:223,4:r,5:i,40:718,77:m3,79:75,89:h,132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,184:99,189:c,199:217,200:219,201:218,202:221,203:1383,209:1223,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},e(h4,[2,177]),{2:t,3:1021,4:r,5:i,110:1384,111:1019,112:fn},e(a4,[2,87]),e(et,[2,147],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{78:[1,1385]},{74:We,78:[2,1076]},{2:t,3:171,4:r,5:i,56:167,77:i1,78:[2,1069],94:1390,111:151,113:155,120:1386,121:1387,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1388,244:j,245:Y,246:[1,1389],258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(m2,[2,98]),e(q2,[2,1072],{153:1025,179:Xe,180:Ke,181:Qe}),{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1391],112:se,114:743,115:A1,116:N1,117:1392,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},e(q2,[2,1073],{153:1025,179:Xe,180:Ke,181:Qe}),{78:[1,1393],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1394],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1395]},e(m2,[2,120]),{74:o4,78:[1,1396]},e(m2,[2,122]),{74:We,78:[1,1397]},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1398],112:se,114:743,115:A1,116:N1,117:1399,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1400],112:se,114:743,115:A1,116:N1,117:1401,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1402],112:se,114:743,115:A1,116:N1,117:1403,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1404],112:se,114:743,115:A1,116:N1,117:1405,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{74:a3,78:[1,1406]},e(K2,[2,143],{424:193,3:740,114:743,144:765,158:775,160:776,117:1408,2:t,4:r,5:i,72:re,76:ne,77:ie,112:se,115:A1,116:N1,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,425:I,429:C}),e(wt,s3,{178:1246,163:1409}),{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1410],112:se,114:743,115:A1,116:N1,117:1411,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:740,4:r,5:i,72:re,76:ne,77:ie,78:[1,1412],112:se,114:743,115:A1,116:N1,117:1413,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{74:a3,78:[1,1414]},{74:a3,78:[1,1415]},{74:a3,78:[1,1416]},{74:a3,78:[1,1417]},{78:[1,1418],153:1025,179:Xe,180:Ke,181:Qe},{74:r3,78:[1,1419]},{2:t,3:740,4:r,5:i,72:re,74:[1,1420],76:ne,77:ie,112:se,114:743,115:A1,116:N1,117:1421,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,144:765,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,158:775,160:776,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,424:193,425:I,429:C},{2:t,3:1422,4:r,5:i},{2:t,3:1423,4:r,5:i},e(V,[2,606]),{2:t,3:1424,4:r,5:i},{113:1425,132:B,301:M},{78:[1,1426]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1427,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1021,4:r,5:i,111:1075,143:O3,145:C3,341:1428,342:1076},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1429,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{124:[1,1430]},e(V,[2,662],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(R3,[2,667]),{78:[1,1431],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(V,[2,663],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1432,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(It,[2,276]),e(It,[2,278]),e(It,[2,280]),e(It,[2,282]),e(S3,[2,161]),e(V,[2,578]),{148:[1,1433]},e(V,[2,579]),e(L2,[2,545],{392:887,7:888,277:1434,4:e3,391:[1,1435],394:t3}),e(V,[2,580]),e(V,[2,582]),{74:We,78:[1,1436]},e(V,[2,586]),e(B2,[2,352]),{74:[1,1437],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{74:[1,1438],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{74:[1,1439],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{74:[1,1440],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{74:[1,1441],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(V,[2,590]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1442,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1443,4:r,5:i},e(V,[2,592]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1390,111:151,113:155,120:1444,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1388,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{77:[1,1445]},{2:t,3:1446,4:r,5:i},{76:B4,139:[2,1159],485:1447,488:1448},e(On,[2,1158]),{139:[1,1449]},{139:[2,1162]},e(L2,[2,747]),e(ft,[2,754]),e(ft,[2,1176]),{2:t,3:1280,4:r,5:i,76:[1,1452],356:1450,363:1451,389:1453},{2:t,3:1021,4:r,5:i,100:1454,111:1455},{40:1456,79:75,89:h,184:99,189:c},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1457,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(W2,[2,732]),{2:t,3:1021,4:r,5:i,111:1075,143:O3,145:C3,147:1458,341:1074,342:1076},{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1459,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(W2,[2,737]),{2:t,3:248,4:r,5:i,199:1460},{340:pn,343:bn,344:En,516:1461},e(qe,[2,700],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1462,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{74:[1,1463],78:[1,1464]},e(K2,[2,552]),e(K2,[2,553]),{74:z4,78:[1,1465]},e(qe,[2,574]),e(i4,[2,389],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,321:C1,322:_1,323:F1,324:P1}),e(i4,[2,391],{114:632,332:644,115:A1,116:N1,123:w1,133:k1,136:R1,138:x1,141:U1,142:y1,179:D1,180:L1,317:I1,321:C1,322:_1,323:F1,324:P1}),e(a2,[2,405]),e(a2,[2,409]),{78:[1,1466]},{74:We,78:[1,1467]},e(a2,[2,431]),e(a2,[2,433]),{78:[1,1468],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1469]},{74:We,78:[1,1470]},e(a2,[2,436]),e(a2,[2,333]),{77:[1,1471]},e(a2,K4,{280:1472,281:Q4}),e(a2,K4,{280:1473,281:Q4}),e(j4,[2,288]),e(a2,[2,285]),e(a2,[2,378]),e(G4,[2,382],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{74:[1,1475],78:[1,1474]},{74:[1,1477],78:[1,1476],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{2:t,3:1338,4:r,5:i},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1205,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,350:1478,424:193,425:I,429:C},e(i3,[2,486]),e(i3,[2,487]),{40:1481,77:Rt,79:75,89:h,143:x,144:988,145:G2,149:A3,152:L,181:D,184:99,189:c,201:989,307:_,346:1479,347:1480,349:N3,424:193,425:I,429:C},{2:t,3:1021,4:r,5:i,111:1482},e(i3,[2,482]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1483,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1484,424:193,425:I,429:C},e(W2,[2,469],{74:gt}),e(W2,[2,476]),e(V,[2,703]),e(Q2,[2,709]),e(Q2,[2,710]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:824,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,174:1485,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,257:823,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{170:[1,1487],314:[1,1486]},{465:[1,1488]},{230:[2,707]},e(U2,[2,180]),e(d4,[2,241],{85:1489,232:[1,1490]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1491,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1492,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1493,4:r,5:i},e(n4,[2,170],{216:1371,221:1374,215:1494,205:1495,206:u4,208:f4,222:c4,223:I3,224:w3,225:k3,226:x3,227:L3,228:D3,229:_3}),{2:t,3:223,4:r,5:i,77:m3,132:Ut,143:x,144:216,145:r1,152:L,156:U,181:D,199:217,200:219,201:218,202:221,209:1496,213:yt,214:222,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},e(nt,[2,205]),e(nt,[2,206]),{2:t,3:223,4:r,5:i,77:[1,1501],143:x,144:1499,145:r1,152:L,156:U,181:D,199:1498,200:1502,201:1500,202:1503,217:1497,270:P2,294:J,295:y,296:F,297:P,298:G,307:_,424:193,425:I,429:C},{207:[1,1504],223:Ln},{207:[1,1506],223:Dn},e(it,[2,222]),{206:[1,1510],208:[1,1509],221:1508,223:I3,224:w3,225:k3,226:x3,227:L3,228:D3,229:_3},e(it,[2,224]),{223:[1,1511]},{208:[1,1513],223:[1,1512]},{208:[1,1515],223:[1,1514]},{208:[1,1516]},{223:[1,1517]},{223:[1,1518]},{74:xn,204:1519,205:1370,206:u4,208:f4,215:1369,216:1371,221:1374,222:c4,223:I3,224:w3,225:k3,226:x3,227:L3,228:D3,229:_3},e(a4,[2,84]),e(m2,[2,100]),{74:V3,78:[1,1520]},{78:[1,1522]},e(kt,[2,262]),{78:[2,1070]},e(kt,[2,266],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,246:[1,1523],247:[1,1524],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(m2,[2,99]),e(q2,[2,1074],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,101]),e(m2,[2,102]),e(m2,[2,103]),e(m2,[2,121]),e(m2,[2,124]),e(m2,[2,127]),e(q2,[2,1078],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,128]),e(q2,[2,1080],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,129]),e(q2,[2,1082],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,130]),e(q2,[2,1086],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,131]),e(wt,[2,1093],{177:1525}),e(wt,[2,1096],{153:1025,179:Xe,180:Ke,181:Qe}),{74:a3,78:[1,1526]},e(m2,[2,133]),e(q2,[2,1088],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,134]),e(q2,[2,1090],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,135]),e(m2,[2,136]),e(m2,[2,137]),e(m2,[2,138]),e(m2,[2,139]),e(m2,[2,140]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:265,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,151:1527,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(Nn,[2,1092],{153:1025,179:Xe,180:Ke,181:Qe}),e(V,[2,616]),e(V,[2,612]),e(V,[2,614]),e(V,[2,610]),e(M4,[2,71]),e(V,[2,458],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(l4,[2,461]),e(l4,[2,462],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1528,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(R3,[2,671]),e(V,[2,664],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:1529,4:r,5:i},e(L2,[2,554],{390:1530,396:1531,397:1532,371:1540,154:_n,187:Vn,230:Mn,302:Un,348:yn,361:Fn,373:Z4,374:Pn,378:Gn,379:Bn}),e(L2,[2,544]),e(V,[2,585],{76:[1,1544]}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1545,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1546,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1547,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1548,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1549,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{74:We,78:[1,1550]},e(V,[2,594]),{74:V3,78:[1,1551]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1390,111:151,113:155,120:1552,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1388,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e([10,74,78,139,311,315,607,768],[2,751]),{139:[1,1553]},{139:[2,1160]},{2:t,3:1133,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,436:592,480:1135,483:1554,487:589,498:586,502:588},{78:[1,1555]},{74:[1,1556],78:[2,515]},{40:1557,79:75,89:h,184:99,189:c},e(K2,[2,541]),{74:St,78:[1,1558]},e(h4,wn),e(V,[2,1143],{417:1559,418:1560,72:Jn}),e(W2,Cn,{79:75,184:99,114:632,332:644,40:1312,473:1562,89:h,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,146:Rn,154:o2,170:h2,171:d2,179:D1,180:L1,189:c,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2,475:In}),e(W2,[2,735],{74:o4}),e(W2,[2,736],{74:We}),e([10,53,72,89,124,146,156,189,271,272,294,311,315,340,343,344,401,405,406,409,411,413,414,422,423,439,441,442,444,445,446,447,448,452,453,456,457,510,512,513,522,607,768],[2,1191],{517:1563,3:1564,2:t,4:r,5:i,76:[1,1565]}),e(q4,[2,1193],{518:1566,76:[1,1567]}),e(qe,[2,701],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{131:[1,1568]},e(n3,[2,547]),e(n3,[2,549]),e(a2,[2,421]),e(a2,[2,422]),e(a2,[2,448]),e(a2,[2,432]),e(a2,[2,434]),{118:Hn,282:1569,283:1570,284:[1,1571]},e(a2,[2,334]),e(a2,[2,335]),e(a2,[2,321]),{131:[1,1573]},e(a2,[2,323]),{131:[1,1574]},{74:z4,78:[1,1575]},{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1576,424:193,425:I,429:C},e(W2,[2,474],{74:gt}),e(W2,[2,477]),e(h4,[2,497]),e(K2,[2,489],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(W2,[2,468],{74:gt}),e(V,[2,728],{74:r3,198:[1,1577]}),{340:$4,343:er,466:1578},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1581,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{119:[1,1583],170:[1,1584],314:[1,1582]},e(jn,[2,260],{86:1585,118:[1,1586]}),{119:[1,1587]},e(kn,[2,240],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{95:[1,1588],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{95:[1,1589]},e(nt,[2,203]),e(nt,[2,204]),e(h4,[2,178]),e(nt,[2,238],{218:1590,230:[1,1591],231:[1,1592]}),e($2,[2,208],{3:1593,2:t,4:r,5:i,76:[1,1594]}),e(Yn,[2,1105],{219:1595,76:[1,1596]}),{2:t,3:1597,4:r,5:i,76:[1,1598]},{40:1599,79:75,89:h,184:99,189:c},e($2,[2,216],{3:1600,2:t,4:r,5:i,76:[1,1601]}),e($2,[2,219],{3:1602,2:t,4:r,5:i,76:[1,1603]}),{77:[1,1604]},e(it,[2,234]),{77:[1,1605]},e(it,[2,230]),e(it,[2,223]),{223:Dn},{223:Ln},e(it,[2,225]),e(it,[2,226]),{223:[1,1606]},e(it,[2,228]),{223:[1,1607]},{223:[1,1608]},e(it,[2,232]),e(it,[2,233]),{78:[1,1609],205:1495,206:u4,208:f4,215:1494,216:1371,221:1374,222:c4,223:I3,224:w3,225:k3,226:x3,227:L3,228:D3,229:_3},e(m2,[2,91]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1390,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1610,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(m2,[2,92]),e(kt,[2,267],{242:1611,243:[1,1612]}),{248:[1,1613]},e(K2,[2,142],{424:193,3:740,114:743,144:765,158:775,160:776,117:1614,2:t,4:r,5:i,72:re,76:ne,77:ie,112:se,115:A1,116:N1,118:ae,122:oe,123:le,124:ue,128:fe,129:ce,130:he,131:de,132:pe,133:be,134:Ee,135:ge,136:me,137:Se,138:Te,139:ve,140:Ae,141:Ne,142:Oe,143:Ce,145:Re,146:Ie,148:we,149:ke,150:xe,152:Le,154:De,156:_e,162:Ve,164:Me,166:Ue,168:ye,169:Fe,170:Pe,171:Ge,172:Be,173:Je,175:He,185:je,187:Ye,244:j,245:Y,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,425:I,429:C}),e(m2,[2,132]),{74:We,78:[1,1615]},e(l4,[2,463],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(V,[2,581]),e(L2,[2,543]),e(L2,[2,555],{371:1540,397:1616,154:_n,187:Vn,230:Mn,302:Un,348:yn,361:Fn,373:Z4,374:Pn,378:Gn,379:Bn}),e(x2,[2,557]),{375:[1,1617]},{375:[1,1618]},{2:t,3:248,4:r,5:i,199:1619},e(x2,[2,563],{77:[1,1620]}),{2:t,3:114,4:r,5:i,77:[1,1622],113:255,131:H,132:B,143:x,152:L,156:U,181:D,196:254,200:1623,201:258,261:256,262:257,269:x4,270:L4,279:1621,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_},e(x2,[2,567]),{302:[1,1624]},e(x2,[2,569]),e(x2,[2,570]),{340:[1,1625]},{77:[1,1626]},{2:t,3:1627,4:r,5:i},{78:[1,1628],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1629],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1630],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1631],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{78:[1,1632],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(V,Y4,{412:1633,76:W4}),e(V,[2,600]),{74:V3,78:[1,1634]},{2:t,3:1133,4:r,5:i,132:Pt,137:Ct,143:Nt,145:Ot,152:pt,436:592,480:1135,483:1635,487:589,498:586,502:588},e(L2,[2,745]),e(V,[2,502],{357:1636,359:1637,360:1638,4:Wn,247:Xn,348:Kn,361:Qn}),e(p4,tr,{3:1280,364:1643,389:1644,365:1645,366:1646,2:t,4:r,5:i,372:rr}),{78:[2,516]},{76:[1,1648]},e(V,[2,618]),e(V,[2,1144]),{373:[1,1650],419:[1,1649]},e(W2,[2,738]),e(V,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:1651,2:t,4:r,5:i,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(V,[2,772]),e(q4,[2,1192]),e(V,n,{17:5,18:7,19:8,20:9,21:10,22:11,23:12,24:13,25:14,26:15,27:16,28:17,29:18,30:19,31:20,32:21,33:22,34:23,35:24,36:25,37:26,38:27,39:28,40:29,41:30,42:31,43:32,44:33,45:34,46:35,47:36,48:37,49:38,50:39,51:40,52:41,54:43,55:44,56:45,57:46,58:47,59:48,60:49,61:50,62:51,63:52,64:53,65:54,66:55,67:56,68:57,69:58,70:59,71:60,79:75,509:95,184:99,3:100,12:1652,2:t,4:r,5:i,53:u,72:f,89:h,124:g,146:m,156:b,189:c,271:d,272:v,294:E,340:O,343:R,344:A,401:N,405:w,406:T1,409:v1,411:O1,413:G1,414:B1,422:Y1,423:k,439:s2,441:K1,442:V1,444:T2,445:p2,446:O2,447:N2,448:A2,452:w2,453:Z2,456:X2,457:g2,510:D2,512:Y2,513:k2,522:F2}),e(q4,[2,1194]),{78:[1,1653]},{78:[1,1654],118:Hn,283:1655},{78:[1,1656]},{119:[1,1657]},{119:[1,1658]},{78:[1,1659]},{78:[1,1660]},e(i3,[2,485]),e(W2,[2,473],{74:gt}),{2:t,3:248,4:r,5:i,143:Nt,145:Ot,199:1662,436:1661},e(Q2,[2,713]),e(Q2,[2,715]),{146:[1,1663]},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,314:[1,1664],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},{344:M3,467:1665},{422:[1,1668],468:[1,1667]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1669,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(Ht,[2,271],{87:1670,249:[1,1671],251:[1,1672]}),{119:[1,1673]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,233:1674,235:1675,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1680,4:r,5:i},{2:t,3:1681,4:r,5:i},e(nt,[2,207]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1682,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1021,4:r,5:i,77:[1,1684],100:1683,111:1455},e($2,[2,209]),{2:t,3:1685,4:r,5:i},e($2,[2,1107],{220:1686,3:1687,2:t,4:r,5:i}),e(Yn,[2,1106]),e($2,[2,212]),{2:t,3:1688,4:r,5:i},{78:[1,1689]},e($2,[2,217]),{2:t,3:1690,4:r,5:i},e($2,[2,220]),{2:t,3:1691,4:r,5:i},{40:1692,79:75,89:h,184:99,189:c},{40:1693,79:75,89:h,184:99,189:c},e(it,[2,227]),e(it,[2,229]),e(it,[2,231]),e(n4,[2,171]),e(kt,[2,263]),e(kt,[2,268]),{244:[1,1694],245:[1,1695]},e(kt,[2,269],{246:[1,1696]}),e(wt,[2,1094],{153:1025,179:Xe,180:Ke,181:Qe}),e(m2,[2,141]),e(x2,[2,556]),e(x2,[2,559]),{379:[1,1697]},e(x2,[2,1137],{400:1698,398:1699,77:zn}),{131:H,196:1701},e(x2,[2,564]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1702,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(x2,[2,566]),e(x2,[2,568]),{2:t,3:114,4:r,5:i,77:[1,1704],113:255,131:H,132:B,143:x,152:L,156:U,181:D,196:254,200:259,201:258,261:256,262:257,269:x4,270:L4,279:1703,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,307:_},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1705,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(V,[2,587]),e(B2,[2,357]),e(B2,[2,358]),e(B2,[2,359]),e(B2,[2,360]),e(B2,[2,361]),e(V,[2,591]),e(V,[2,601]),e(L2,[2,744]),e(V,[2,498]),e(V,[2,503],{360:1706,4:Wn,247:Xn,348:Kn,361:Qn}),e(f3,[2,505]),e(f3,[2,506]),{124:[1,1707]},{124:[1,1708]},{124:[1,1709]},{74:[1,1710],78:[2,514]},e(K2,[2,542]),e(K2,[2,517]),{187:[1,1718],193:[1,1719],367:1711,368:1712,369:1713,370:1714,371:1715,373:Z4,374:[1,1716],375:[1,1720],378:[1,1717]},{2:t,3:1721,4:r,5:i},{40:1722,79:75,89:h,184:99,189:c},{420:[1,1723]},{421:[1,1724]},e(V,[2,771]),e(V,[2,773]),e(n3,[2,546]),e(a2,[2,337]),{78:[1,1725]},e(a2,[2,338]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,233:1726,235:1675,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1390,111:151,113:155,120:1727,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1388,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(a2,[2,322]),e(a2,[2,324]),{2:t,3:1728,4:r,5:i},e(V,[2,730],{77:[1,1729]}),{2:t,3:1021,4:r,5:i,111:1075,143:O3,145:C3,147:1730,341:1074,342:1076},{340:$4,343:er,466:1731},e(Q2,[2,717]),{77:[1,1733],348:[1,1734],349:[1,1732]},{170:[1,1736],314:[1,1735]},{170:[1,1738],314:[1,1737]},{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,314:[1,1739],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(et,[2,251],{88:1740,162:[1,1741],168:[1,1743],169:[1,1742]}),{131:H,196:1744},{131:H,196:1745},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1390,111:151,113:155,120:1746,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,241:1388,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},e(d4,[2,249],{234:1747,74:U3,239:[1,1749]}),e(c3,[2,243]),{146:[1,1750]},{77:[1,1751]},{77:[1,1752]},e(c3,[2,248],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{78:[2,1061],96:1753,99:[1,1755],102:1754},{99:[1,1756]},e(nt,[2,235],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),e(nt,[2,236],{74:St}),{2:t,3:1021,4:r,5:i,100:1757,111:1455},e($2,[2,210]),e($2,[2,211]),e($2,[2,1108]),e($2,[2,213]),{2:t,3:1758,4:r,5:i,76:[1,1759]},e($2,[2,218]),e($2,[2,221]),{78:[1,1760]},{78:[1,1761]},e(kt,[2,264]),e(kt,[2,265]),e(kt,[2,270]),{2:t,3:248,4:r,5:i,199:1762},e(x2,[2,561]),e(x2,[2,1138]),{2:t,3:1763,4:r,5:i},{74:[1,1764]},{78:[1,1765],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(x2,[2,571]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1766,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{78:[1,1767],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(f3,[2,504]),{2:t,3:1768,4:r,5:i},{131:H,196:1769},{2:t,3:1770,4:r,5:i},e(p4,tr,{366:1646,365:1771,372:rr}),e(L2,[2,519]),e(L2,[2,520]),e(L2,[2,521]),e(L2,[2,522]),e(L2,[2,523]),{375:[1,1772]},{375:[1,1773]},e(Zn,[2,1131],{387:1774,375:[1,1775]}),{2:t,3:1776,4:r,5:i},{2:t,3:1777,4:r,5:i},e(p4,[2,525]),e(V,[2,1141],{416:1778,418:1779,72:Jn}),e(V,[2,619]),e(V,[2,620],{372:[1,1780]}),e(a2,[2,339]),e([78,118],[2,340],{74:U3}),{74:V3,78:[2,341]},e(V,[2,729]),{2:t,3:1021,4:r,5:i,100:1781,111:1455},e(Q2,[2,716],{74:o4}),e(Q2,[2,714]),{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1782,424:193,425:I,429:C},{2:t,3:1021,4:r,5:i,100:1783,111:1455},{349:[1,1784]},{344:M3,467:1785},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1786,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{344:M3,467:1787},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1788,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{344:M3,467:1789},e(et,[2,72]),{40:1790,79:75,89:h,164:[1,1791],184:99,189:c,240:[1,1792]},{40:1793,79:75,89:h,184:99,189:c,240:[1,1794]},{40:1795,79:75,89:h,184:99,189:c,240:[1,1796]},e(Ht,[2,274],{250:1797,251:[1,1798]}),{252:1799,253:[2,1109],770:[1,1800]},e(jn,[2,261],{74:V3}),e(d4,[2,242]),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,235:1801,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1802,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{77:[1,1803]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,233:1804,235:1675,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,233:1805,235:1675,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{78:[1,1806]},{78:[2,1062]},{77:[1,1807]},{77:[1,1808]},{74:St,78:[1,1809]},e($2,[2,214]),{2:t,3:1810,4:r,5:i},{2:t,3:1811,4:r,5:i,76:[1,1812]},{2:t,3:1813,4:r,5:i,76:[1,1814]},e(x2,[2,1135],{399:1815,398:1816,77:zn}),{78:[1,1817]},{131:H,196:1818},e(x2,[2,565]),{78:[1,1819],99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(x2,[2,526]),e(f3,[2,507]),e(f3,[2,508]),e(f3,[2,509]),e(K2,[2,518]),{2:t,3:1821,4:r,5:i,77:[2,1127],376:1820},{77:[1,1822]},{2:t,3:1824,4:r,5:i,77:[2,1133],388:1823},e(Zn,[2,1132]),{77:[1,1825]},{77:[1,1826]},e(V,[2,617]),e(V,[2,1142]),e(p4,tr,{366:1646,365:1827,372:rr}),{74:St,78:[1,1828]},e(Q2,[2,723],{74:gt}),{74:St,78:[1,1829]},e(Q2,[2,725]),e(Q2,[2,718]),{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,314:[1,1830],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(Q2,[2,721]),{99:r2,112:W1,114:632,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,314:[1,1831],316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,332:644,335:l2,336:u2,337:X1,338:c2},e(Q2,[2,719]),e(et,[2,252]),{40:1832,79:75,89:h,184:99,189:c,240:[1,1833]},{40:1834,79:75,89:h,184:99,189:c},e(et,[2,254]),{40:1835,79:75,89:h,184:99,189:c},e(et,[2,255]),{40:1836,79:75,89:h,184:99,189:c},e(Ht,[2,272]),{131:H,196:1837},{253:[1,1838]},{253:[2,1110]},e(c3,[2,244]),e(d4,[2,250],{114:632,332:644,99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1679,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,233:1839,235:1675,236:o3,237:l3,238:u3,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{74:U3,78:[1,1840]},{74:U3,78:[1,1841]},e(mt,[2,1063],{97:1842,104:1843,3:1845,2:t,4:r,5:i,76:qn}),{2:t,3:171,4:r,5:i,56:167,77:i1,94:1848,103:1846,105:1847,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1021,4:r,5:i,100:1849,111:1455},e(nt,[2,237]),e($2,[2,215]),e(nt,[2,173]),{2:t,3:1850,4:r,5:i},e(nt,[2,175]),{2:t,3:1851,4:r,5:i},e(x2,[2,560]),e(x2,[2,1136]),e(x2,[2,558]),{78:[1,1852]},e(x2,[2,572]),{77:[1,1853]},{77:[2,1128]},{2:t,3:1855,4:r,5:i,132:nr,377:1854},{77:[1,1857]},{77:[2,1134]},{2:t,3:1021,4:r,5:i,100:1858,111:1455},{2:t,3:1021,4:r,5:i,100:1859,111:1455},e(V,[2,621]),e(V,[2,731]),{348:[1,1861],349:[1,1860]},{344:M3,467:1862},{340:$4,343:er,466:1863},e(et,[2,253]),{40:1864,79:75,89:h,184:99,189:c},e(et,[2,256]),e(et,[2,258]),e(et,[2,259]),e(Ht,[2,275]),{131:[2,1111],254:1865,650:[1,1866]},{74:U3,78:[1,1867]},e(c3,[2,246]),e(c3,[2,247]),e(mt,[2,74]),e(mt,[2,1064]),{2:t,3:1868,4:r,5:i},e(mt,[2,78]),{74:[1,1870],78:[1,1869]},e(K2,[2,80]),e(K2,[2,81],{114:632,332:644,76:[1,1871],99:r2,112:W1,115:A1,116:N1,123:w1,124:f2,133:k1,136:R1,138:x1,139:n2,140:i2,141:U1,142:y1,154:o2,170:h2,171:d2,179:D1,180:L1,316:J1,317:I1,318:H1,320:j1,321:C1,322:_1,323:F1,324:P1,325:Q1,326:z1,327:Z1,328:q1,329:$1,330:e2,331:t2,335:l2,336:u2,337:X1,338:c2}),{74:St,78:[1,1872]},e(nt,[2,174]),e(nt,[2,176]),e(x2,[2,562]),{2:t,3:1855,4:r,5:i,132:nr,377:1873},{74:ir,78:[1,1874]},e(K2,[2,537]),e(K2,[2,538]),{2:t,3:1021,4:r,5:i,100:1876,111:1455},{74:St,78:[1,1877]},{74:St,78:[1,1878]},{77:Rt,143:x,144:988,145:G2,152:L,181:D,201:989,307:_,347:1879,424:193,425:I,429:C},{349:[1,1880]},e(Q2,[2,720]),e(Q2,[2,722]),e(et,[2,257]),{131:H,196:1881},{131:[2,1112]},e(c3,[2,245]),e(mt,[2,77]),{78:[2,76]},{2:t,3:171,4:r,5:i,56:167,77:i1,94:1848,105:1882,111:151,113:155,131:H,132:B,137:s1,143:x,144:163,145:r1,149:a1,152:L,154:o1,156:U,158:170,179:l1,180:u1,181:D,196:153,200:149,201:157,202:158,244:j,245:Y,258:152,259:148,260:150,261:154,262:156,263:159,264:160,265:161,266:164,267:165,269:f1,270:c1,271:d,275:h1,276:d1,278:p1,285:W,286:X,287:K,288:Q,289:z,290:Z,291:q,292:$,294:J,295:y,296:F,297:P,298:G,299:e1,300:t1,301:M,302:n1,303:b1,304:E1,307:_,308:g1,317:m1,322:S1,424:193,425:I,429:C},{2:t,3:1883,4:r,5:i},{78:[1,1884]},{74:ir,78:[1,1885]},{379:[1,1886]},{2:t,3:1887,4:r,5:i,132:[1,1888]},{74:St,78:[1,1889]},e(L2,[2,535]),e(L2,[2,536]),e(Q2,[2,724],{74:gt}),e(Q2,[2,726]),e($n,[2,1113],{255:1890,770:[1,1891]}),e(K2,[2,79]),e(K2,[2,82]),e(mt,[2,1065],{3:1845,101:1892,104:1893,2:t,4:r,5:i,76:qn}),e(L2,[2,527]),{2:t,3:248,4:r,5:i,199:1894},e(K2,[2,539]),e(K2,[2,540]),e(L2,[2,534]),e(Ht,[2,1115],{256:1895,420:[1,1896]}),e($n,[2,1114]),e(mt,[2,75]),e(mt,[2,1066]),e(sr,[2,1129],{380:1897,382:1898,77:[1,1899]}),e(Ht,[2,273]),e(Ht,[2,1116]),e(L2,[2,530],{381:1900,383:1901,230:[1,1902]}),e(sr,[2,1130]),{2:t,3:1855,4:r,5:i,132:nr,377:1903},e(L2,[2,528]),{230:[1,1905],384:1904},{343:[1,1906]},{74:ir,78:[1,1907]},e(L2,[2,531]),{340:[1,1908]},{385:[1,1909]},e(sr,[2,529]),{385:[1,1910]},{386:[1,1911]},{386:[1,1912]},{230:[2,532]},e(L2,[2,533])],defaultActions:{105:[2,6],197:[2,342],198:[2,343],199:[2,344],200:[2,345],201:[2,346],202:[2,347],203:[2,348],204:[2,349],205:[2,350],206:[2,351],213:[2,704],598:[2,1152],660:[2,1117],661:[2,1118],717:[2,705],787:[2,1083],788:[2,1084],935:[2,455],936:[2,456],937:[2,457],996:[2,706],1301:[2,1162],1361:[2,707],1389:[2,1070],1448:[2,1160],1557:[2,516],1754:[2,1062],1800:[2,1110],1821:[2,1128],1824:[2,1134],1866:[2,1112],1869:[2,76],1911:[2,532]},parseError:function(b2,v2){if(v2.recoverable)this.trace(b2);else{var E2=new Error(b2);throw E2.hash=v2,E2}},parse:function(b2){var v2=this,E2=[0],p=[],M2=[null],a=[],_t=this.table,o="",xt=0,y2=0,ze=0,st=2,vt=1,jt=a.slice.call(arguments,1),_2=Object.create(this.lexer),ct={yy:{}};for(var Yt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Yt)&&(ct.yy[Yt]=this.yy[Yt]);_2.setInput(b2,ct.yy),ct.yy.lexer=_2,ct.yy.parser=this,typeof _2.yylloc>"u"&&(_2.yylloc={});var Vt=_2.yylloc;a.push(Vt);var _i=_2.options&&_2.options.ranges;typeof ct.yy.parseError=="function"?this.parseError=ct.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Vi(tt){E2.length=E2.length-2*tt,M2.length=M2.length-tt,a.length=a.length-tt}for(var ei=function(){var tt;return tt=_2.lex()||vt,typeof tt!="number"&&(tt=v2.symbols_[tt]||tt),tt},H2,h3,lt,at,zi,or,d3={},E4,Lt,ti,g4;;){if(lt=E2[E2.length-1],this.defaultActions[lt]?at=this.defaultActions[lt]:((H2===null||typeof H2>"u")&&(H2=ei()),at=_t[lt]&&_t[lt][H2]),typeof at>"u"||!at.length||!at[0]){var y3,F3="",ri=function(tt){for(var lr=E2.length-1,ni=0;;){if(st.toString()in _t[tt])return ni;if(tt===0||lr<2)return!1;lr-=2,tt=E2[lr],++ni}};if(ze)h3!==vt&&(y3=ri(lt));else{y3=ri(lt),g4=[];for(E4 in _t[lt])this.terminals_[E4]&&E4>st&&g4.push("'"+this.terminals_[E4]+"'");_2.showPosition?F3="Parse error on line "+(xt+1)+`:
`+_2.showPosition()+`
Expecting `+g4.join(", ")+", got '"+(this.terminals_[H2]||H2)+"'":F3="Parse error on line "+(xt+1)+": Unexpected "+(H2==vt?"end of input":"'"+(this.terminals_[H2]||H2)+"'"),this.parseError(F3,{text:_2.match,token:this.terminals_[H2]||H2,line:_2.yylineno,loc:Vt,expected:g4,recoverable:y3!==!1})}if(ze==3){if(H2===vt||h3===vt)throw new Error(F3||"Parsing halted while starting to recover from another error.");y2=_2.yyleng,o=_2.yytext,xt=_2.yylineno,Vt=_2.yylloc,H2=ei()}if(y3===!1)throw new Error(F3||"Parsing halted. No suitable error recovery rule available.");Vi(y3),h3=H2==st?null:H2,H2=st,lt=E2[E2.length-1],at=_t[lt]&&_t[lt][st],ze=3}if(at[0]instanceof Array&&at.length>1)throw new Error("Parse Error: multiple actions possible at state: "+lt+", token: "+H2);switch(at[0]){case 1:E2.push(H2),M2.push(_2.yytext),a.push(_2.yylloc),E2.push(at[1]),H2=null,h3?(H2=h3,h3=null):(y2=_2.yyleng,o=_2.yytext,xt=_2.yylineno,Vt=_2.yylloc,ze>0&&ze--);break;case 2:if(Lt=this.productions_[at[1]][1],d3.$=M2[M2.length-Lt],d3._$={first_line:a[a.length-(Lt||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(Lt||1)].first_column,last_column:a[a.length-1].last_column},_i&&(d3._$.range=[a[a.length-(Lt||1)].range[0],a[a.length-1].range[1]]),or=this.performAction.apply(d3,[o,y2,xt,ct.yy,at[1],M2,a].concat(jt)),typeof or<"u")return or;Lt&&(E2=E2.slice(0,-1*Lt*2),M2=M2.slice(0,-1*Lt),a=a.slice(0,-1*Lt)),E2.push(this.productions_[at[1]][0]),M2.push(d3.$),a.push(d3._$),ti=_t[E2[E2.length-2]][E2[E2.length-1]],E2.push(ti);break;case 3:return!0}}return!0}},Li=["A","ABSENT","ABSOLUTE","ACCORDING","ACTION","ADA","ADD","ADMIN","AFTER","ALWAYS","ASC","ASSERTION","ASSIGNMENT","ATTRIBUTE","ATTRIBUTES","BASE64","BEFORE","BERNOULLI","BLOCKED","BOM","BREADTH","C","CASCADE","CATALOG","CATALOG_NAME","CHAIN","CHARACTERISTICS","CHARACTERS","CHARACTER_SET_CATALOG","CHARACTER_SET_NAME","CHARACTER_SET_SCHEMA","CLASS_ORIGIN","COBOL","COLLATION","COLLATION_CATALOG","COLLATION_NAME","COLLATION_SCHEMA","COLUMNS","COLUMN_NAME","COMMAND_FUNCTION","COMMAND_FUNCTION_CODE","COMMITTED","CONDITION_NUMBER","CONNECTION","CONNECTION_NAME","CONSTRAINTS","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONSTRUCTOR","CONTENT","CONTINUE","CONTROL","CURSOR_NAME","DATA","DATETIME_INTERVAL_CODE","DATETIME_INTERVAL_PRECISION","DB","DEFAULTS","DEFERRABLE","DEFERRED","DEFINED","DEFINER","DEGREE","DEPTH","DERIVED","DESC","DESCRIPTOR","DIAGNOSTICS","DISPATCH","DOCUMENT","DOMAIN","DYNAMIC_FUNCTION","DYNAMIC_FUNCTION_CODE","EMPTY","ENCODING","ENFORCED","EXCLUDE","EXCLUDING","EXPRESSION","FILE","FINAL","FIRST","FLAG","FOLLOWING","FORTRAN","FOUND","FS","G","GENERAL","GENERATED","GO","GOTO","GRANTED","HEX","HIERARCHY","ID","IGNORE","IMMEDIATE","IMMEDIATELY","IMPLEMENTATION","INCLUDING","INCREMENT","INDENT","INITIALLY","INPUT","INSTANCE","INSTANTIABLE","INSTEAD","INTEGRITY","INVOKER","ISOLATION","K","KEY","KEY_MEMBER","KEY_TYPE","LAST","LENGTH","LEVEL","LIBRARY","LIMIT","LINK","LOCATION","LOCATOR","M","MAP","MAPPING","MATCHED","MAXVALUE","MESSAGE_LENGTH","MESSAGE_OCTET_LENGTH","MESSAGE_TEXT","MINVALUE","MORE","MUMPS","NAME","NAMES","NAMESPACE","NESTING","NEXT","NFC","NFD","NFKC","NFKD","NIL","NORMALIZED","NULLABLE","NULLS","NUMBER","OBJECT","OCTETS","OFF","OPTION","OPTIONS","ORDERING","ORDINALITY","OTHERS","OUTPUT","OVERRIDING","P","PAD","PARAMETER_MODE","PARAMETER_NAME","PARAMETER_ORDINAL_POSITION","PARAMETER_SPECIFIC_CATALOG","PARAMETER_SPECIFIC_NAME","PARAMETER_SPECIFIC_SCHEMA","PARTIAL","PASCAL","PASSING","PASSTHROUGH","PATH","PERMISSION","PLACING","PLI","PRECEDING","PRESERVE","PRIOR","PRIVILEGES","PUBLIC","READ","RECOVERY","RELATIVE","REPEATABLE","REQUIRING","RESPECT","RESTART","RESTORE","RESTRICT","RETURNED_CARDINALITY","RETURNED_LENGTH","RETURNED_OCTET_LENGTH","RETURNED_SQLSTATE","RETURNING","ROLE","ROUTINE","ROUTINE_CATALOG","ROUTINE_NAME","ROUTINE_SCHEMA","ROW_COUNT","SCALE","SCHEMA","SCHEMA_NAME","SCOPE_CATALOG","SCOPE_NAME","SCOPE_SCHEMA","SECTION","SECURITY","SELECTIVE","SELF","SEQUENCE","SERIALIZABLE","SERVER","SERVER_NAME","SESSION","SETS","SIMPLE","SIZE","SOURCE","SPACE","SPECIFIC_NAME","STANDALONE","STATE","STATEMENT","STRIP","STRUCTURE","STYLE","SUBCLASS_ORIGIN","T","TABLE_NAME","TEMPORARY","TIES","TOKEN","TOP_LEVEL_COUNT","TRANSACTION","TRANSACTIONS_COMMITTED","TRANSACTIONS_ROLLED_BACK","TRANSACTION_ACTIVE","TRANSFORM","TRANSFORMS","TRIGGER_CATALOG","TRIGGER_NAME","TRIGGER_SCHEMA","TYPE","UNBOUNDED","UNCOMMITTED","UNDER","UNLINK","UNNAMED","UNTYPED","URI","USAGE","USER_DEFINED_TYPE_CATALOG","USER_DEFINED_TYPE_CODE","USER_DEFINED_TYPE_NAME","USER_DEFINED_TYPE_SCHEMA","VALID","VERSION","VIEW","WHITESPACE","WORK","WRAPPER","WRITE","XMLDECLARATION","XMLSCHEMA","YES","ZONE"];b4.parseError=function(Tt,b2){if(!(b2.expected&&b2.expected.indexOf("'LITERAL'")>-1&&/[a-zA-Z_][a-zA-Z_0-9]*/.test(b2.token)&&Li.indexOf(b2.token)>-1))throw new SyntaxError(Tt)};var Di=function(){var Tt={EOF:1,parseError:function(v2,E2){if(this.yy.parser)this.yy.parser.parseError(v2,E2);else throw new Error(v2)},setInput:function(b2,v2){return this.yy=v2||this.yy||{},this._input=b2,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var b2=this._input[0];this.yytext+=b2,this.yyleng++,this.offset++,this.match+=b2,this.matched+=b2;var v2=b2.match(/(?:\r\n?|\n).*/g);return v2?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),b2},unput:function(b2){var v2=b2.length,E2=b2.split(/(?:\r\n?|\n)/g);this._input=b2+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-v2),this.offset-=v2;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),E2.length-1&&(this.yylineno-=E2.length-1);var M2=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:E2?(E2.length===p.length?this.yylloc.first_column:0)+p[p.length-E2.length].length-E2[0].length:this.yylloc.first_column-v2},this.options.ranges&&(this.yylloc.range=[M2[0],M2[0]+this.yyleng-v2]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(b2){this.unput(this.match.slice(b2))},pastInput:function(){var b2=this.matched.substr(0,this.matched.length-this.match.length);return(b2.length>20?"...":"")+b2.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var b2=this.match;return b2.length<20&&(b2+=this._input.substr(0,20-b2.length)),(b2.substr(0,20)+(b2.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var b2=this.pastInput(),v2=new Array(b2.length+1).join("-");return b2+this.upcomingInput()+`
`+v2+"^"},test_match:function(b2,v2){var E2,p,M2;if(this.options.backtrack_lexer&&(M2={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(M2.yylloc.range=this.yylloc.range.slice(0))),p=b2[0].match(/(?:\r\n?|\n).*/g),p&&(this.yylineno+=p.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:p?p[p.length-1].length-p[p.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+b2[0].length},this.yytext+=b2[0],this.match+=b2[0],this.matches=b2,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(b2[0].length),this.matched+=b2[0],E2=this.performAction.call(this,this.yy,this,v2,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),E2)return E2;if(this._backtrack){for(var a in M2)this[a]=M2[a];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var b2,v2,E2,p;this._more||(this.yytext="",this.match="");for(var M2=this._currentRules(),a=0;a<M2.length;a++)if(E2=this._input.match(this.rules[M2[a]]),E2&&(!v2||E2[0].length>v2[0].length)){if(v2=E2,p=a,this.options.backtrack_lexer){if(b2=this.test_match(E2,M2[a]),b2!==!1)return b2;if(this._backtrack){v2=!1;continue}else return!1}else if(!this.options.flex)break}return v2?(b2=this.test_match(v2,M2[p]),b2!==!1?b2:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var v2=this.next();return v2||this.lex()},begin:function(v2){this.conditionStack.push(v2)},popState:function(){var v2=this.conditionStack.length-1;return v2>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(v2){return v2=this.conditionStack.length-1-Math.abs(v2||0),v2>=0?this.conditionStack[v2]:"INITIAL"},pushState:function(v2){this.begin(v2)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(v2,E2,p,M2){var a=M2;switch(p){case 0:return 271;case 1:return 307;case 2:return 425;case 3:return 304;case 4:return 5;case 5:return 5;case 6:return 301;case 7:return 301;case 8:return 132;case 9:return 132;case 10:return;case 11:break;case 12:return 321;case 13:return 324;case 14:return E2.yytext="VALUE",89;break;case 15:return E2.yytext="VALUE",189;break;case 16:return E2.yytext="ROW",189;break;case 17:return E2.yytext="COLUMN",189;break;case 18:return E2.yytext="MATRIX",189;break;case 19:return E2.yytext="INDEX",189;break;case 20:return E2.yytext="RECORDSET",189;break;case 21:return E2.yytext="TEXT",189;break;case 22:return E2.yytext="SELECT",189;break;case 23:return 525;case 24:return 386;case 25:return 407;case 26:return 520;case 27:return 291;case 28:return 274;case 29:return 274;case 30:return 164;case 31:return 405;case 32:return 170;case 33:return 229;case 34:return 166;case 35:return 207;case 36:return 292;case 37:return 76;case 38:return 423;case 39:return 246;case 40:return 409;case 41:return 361;case 42:return 290;case 43:return 519;case 44:return 442;case 45:return 335;case 46:return 446;case 47:return 336;case 48:return 320;case 49:return 119;case 50:return 112;case 51:return 320;case 52:return 112;case 53:return 320;case 54:return 112;case 55:return 320;case 56:return 513;case 57:return 308;case 58:return 276;case 59:return 373;case 60:return 130;case 61:return"CLOSE";case 62:return 247;case 63:return 190;case 64:return 190;case 65:return 439;case 66:return 372;case 67:return 475;case 68:return 445;case 69:return 278;case 70:return 240;case 71:return 287;case 72:return 272;case 73:return 206;case 74:return 238;case 75:return 269;case 76:return 270;case 77:return 270;case 78:return"CURSOR";case 79:return 410;case 80:return 295;case 81:return 296;case 82:return 297;case 83:return 453;case 84:return 348;case 85:return 343;case 86:return"DELETED";case 87:return 246;case 88:return 411;case 89:return 185;case 90:return 401;case 91:return 452;case 92:return 135;case 93:return 311;case 94:return 394;case 95:return 315;case 96:return 319;case 97:return 169;case 98:return 513;case 99:return 513;case 100:return 303;case 101:return 14;case 102:return 300;case 103:return 253;case 104:return 244;case 105:return 95;case 106:return 378;case 107:return 183;case 108:return 227;case 109:return 273;case 110:return 318;case 111:return 607;case 112:return 477;case 113:return 232;case 114:return 236;case 115:return 239;case 116:return 156;case 117:return 361;case 118:return 337;case 119:return 99;case 120:return 193;case 121:return 212;case 122:return 224;case 123:return 521;case 124:return 344;case 125:return 213;case 126:return 168;case 127:return 298;case 128:return 198;case 129:return 223;case 130:return 375;case 131:return 245;case 132:return"LET";case 133:return 225;case 134:return 112;case 135:return 249;case 136:return 465;case 137:return 191;case 138:return 289;case 139:return 395;case 140:return 288;case 141:return 457;case 142:return 169;case 143:return 408;case 144:return 222;case 145:return 650;case 146:return 275;case 147:return 248;case 148:return 385;case 149:return 154;case 150:return 302;case 151:return 243;case 152:return 438;case 153:return 230;case 154:return 420;case 155:return 129;case 156:return 251;case 157:return"OPEN";case 158:return 421;case 159:return 171;case 160:return 118;case 161:return 208;case 162:return 281;case 163:return 172;case 164:return 284;case 165:return 769;case 166:return 93;case 167:return 16;case 168:return 374;case 169:return 447;case 170:return 682;case 171:return 15;case 172:return 419;case 173:return 194;case 174:return"REDUCE";case 175:return 379;case 176:return 316;case 177:return 522;case 178:return 686;case 179:return 107;case 180:return 406;case 181:return 175;case 182:return 294;case 183:return 448;case 184:return 691;case 185:return 173;case 186:return 173;case 187:return 226;case 188:return 441;case 189:return 237;case 190:return 150;case 191:return 770;case 192:return 410;case 193:return 89;case 194:return 228;case 195:return 146;case 196:return 146;case 197:return 414;case 198:return 339;case 199:return 422;case 200:return"STRATEGY";case 201:return"STORE";case 202:return 285;case 203:return 286;case 204:return 358;case 205:return 358;case 206:return 468;case 207:return 362;case 208:return 362;case 209:return 192;case 210:return 314;case 211:return"TIMEOUT";case 212:return 148;case 213:return 195;case 214:return 440;case 215:return 440;case 216:return 514;case 217:return 299;case 218:return 456;case 219:return 162;case 220:return 187;case 221:return 98;case 222:return 340;case 223:return 413;case 224:return 231;case 225:return 149;case 226:return 349;case 227:return 134;case 228:return 415;case 229:return 313;case 230:return 128;case 231:return 444;case 232:return 72;case 233:return 440;case 234:return 131;case 235:return 115;case 236:return 137;case 237:return 179;case 238:return 322;case 239:return 180;case 240:return 133;case 241:return 138;case 242:return 331;case 243:return 328;case 244:return 330;case 245:return 327;case 246:return 325;case 247:return 323;case 248:return 324;case 249:return 142;case 250:return 141;case 251:return 139;case 252:return 326;case 253:return 329;case 254:return 140;case 255:return 124;case 256:return 329;case 257:return 77;case 258:return 78;case 259:return 145;case 260:return 429;case 261:return 431;case 262:return 305;case 263:return 510;case 264:return 512;case 265:return 122;case 266:return 116;case 267:return 74;case 268:return 338;case 269:return 152;case 270:return 768;case 271:return 143;case 272:return 181;case 273:return 136;case 274:return 123;case 275:return 317;case 276:return 4;case 277:return 10;case 278:return"INVALID"}},rules:[/^(?:``([^\`])+``)/i,/^(?:\[\?\])/i,/^(?:@\[)/i,/^(?:ARRAY\[)/i,/^(?:\[([^\]'])*?\])/i,/^(?:`([^\`'])*?`)/i,/^(?:N(['](\\.|[^']|\\')*?['])+)/i,/^(?:X(['](\\.|[^']|\\')*?['])+)/i,/^(?:(['](\\.|[^']|\\')*?['])+)/i,/^(?:(["](\\.|[^"]|\\")*?["])+)/i,/^(?:--(.*?)($|\r\n|\r|\n))/i,/^(?:\s+)/i,/^(?:\|\|)/i,/^(?:\|)/i,/^(?:VALUE\s+OF\s+SEARCH\b)/i,/^(?:VALUE\s+OF\s+SELECT\b)/i,/^(?:ROW\s+OF\s+SELECT\b)/i,/^(?:COLUMN\s+OF\s+SELECT\b)/i,/^(?:MATRIX\s+OF\s+SELECT\b)/i,/^(?:INDEX\s+OF\s+SELECT\b)/i,/^(?:RECORDSET\s+OF\s+SELECT\b)/i,/^(?:TEXT\s+OF\s+SELECT\b)/i,/^(?:SELECT\b)/i,/^(?:ABSOLUTE\b)/i,/^(?:ACTION\b)/i,/^(?:ADD\b)/i,/^(?:AFTER\b)/i,/^(?:AGGR\b)/i,/^(?:AGGREGATE\b)/i,/^(?:AGGREGATOR\b)/i,/^(?:ALL\b)/i,/^(?:ALTER\b)/i,/^(?:AND\b)/i,/^(?:ANTI\b)/i,/^(?:ANY\b)/i,/^(?:APPLY\b)/i,/^(?:ARRAY\b)/i,/^(?:AS\b)/i,/^(?:ASSERT\b)/i,/^(?:ASC\b)/i,/^(?:ATTACH\b)/i,/^(?:AUTO(_)?INCREMENT\b)/i,/^(?:AVG\b)/i,/^(?:BEFORE\b)/i,/^(?:BEGIN\b)/i,/^(?:BETWEEN\b)/i,/^(?:BREAK\b)/i,/^(?:NOT\s+BETWEEN\b)/i,/^(?:NOT\s+LIKE\b)/i,/^(?:BY\b)/i,/^(?:~~\*)/i,/^(?:!~~\*)/i,/^(?:~~)/i,/^(?:!~~)/i,/^(?:ILIKE\b)/i,/^(?:NOT\s+ILIKE\b)/i,/^(?:CALL\b)/i,/^(?:CASE\b)/i,/^(?:CAST\b)/i,/^(?:CHECK\b)/i,/^(?:CLASS\b)/i,/^(?:CLOSE\b)/i,/^(?:COLLATE\b)/i,/^(?:COLUMN\b)/i,/^(?:COLUMNS\b)/i,/^(?:COMMIT\b)/i,/^(?:CONSTRAINT\b)/i,/^(?:CONTENT\b)/i,/^(?:CONTINUE\b)/i,/^(?:CONVERT\b)/i,/^(?:CORRESPONDING\b)/i,/^(?:COUNT\b)/i,/^(?:CREATE\b)/i,/^(?:CROSS\b)/i,/^(?:CUBE\b)/i,/^(?:CURRENT_TIMESTAMP\b)/i,/^(?:CURRENT_DATE\b)/i,/^(?:CURDATE\b)/i,/^(?:CURSOR\b)/i,/^(?:DATABASE(S)?)/i,/^(?:DATEADD\b)/i,/^(?:DATEDIFF\b)/i,/^(?:TIMESTAMPDIFF\b)/i,/^(?:DECLARE\b)/i,/^(?:DEFAULT\b)/i,/^(?:DELETE\b)/i,/^(?:DELETED\b)/i,/^(?:DESC\b)/i,/^(?:DETACH\b)/i,/^(?:DISTINCT\b)/i,/^(?:DROP\b)/i,/^(?:ECHO\b)/i,/^(?:EDGE\b)/i,/^(?:END\b)/i,/^(?:ENUM\b)/i,/^(?:ELSE\b)/i,/^(?:ESCAPE\b)/i,/^(?:EXCEPT\b)/i,/^(?:EXEC\b)/i,/^(?:EXECUTE\b)/i,/^(?:EXISTS\b)/i,/^(?:EXPLAIN\b)/i,/^(?:FALSE\b)/i,/^(?:FETCH\b)/i,/^(?:FIRST\b)/i,/^(?:FOR\b)/i,/^(?:FOREIGN\b)/i,/^(?:FROM\b)/i,/^(?:FULL\b)/i,/^(?:FUNCTION\b)/i,/^(?:GLOB\b)/i,/^(?:GO\b)/i,/^(?:GRAPH\b)/i,/^(?:GROUP\b)/i,/^(?:GROUPING\b)/i,/^(?:HAVING\b)/i,/^(?:IF\b)/i,/^(?:IDENTITY\b)/i,/^(?:IS\b)/i,/^(?:IN\b)/i,/^(?:INDEX\b)/i,/^(?:INDEXED\b)/i,/^(?:INNER\b)/i,/^(?:INSTEAD\b)/i,/^(?:INSERT\b)/i,/^(?:INSERTED\b)/i,/^(?:INTERSECT\b)/i,/^(?:INTERVAL\b)/i,/^(?:INTO\b)/i,/^(?:JOIN\b)/i,/^(?:KEY\b)/i,/^(?:LAST\b)/i,/^(?:LET\b)/i,/^(?:LEFT\b)/i,/^(?:LIKE\b)/i,/^(?:LIMIT\b)/i,/^(?:MATCHED\b)/i,/^(?:MATRIX\b)/i,/^(?:MAX\s*(?=\())/i,/^(?:MAX\s*(?=(,|\))))/i,/^(?:MIN\s*(?=\())/i,/^(?:MERGE\b)/i,/^(?:MINUS\b)/i,/^(?:MODIFY\b)/i,/^(?:NATURAL\b)/i,/^(?:NEXT\b)/i,/^(?:NEW\b)/i,/^(?:NOCASE\b)/i,/^(?:NO\b)/i,/^(?:NOT\b)/i,/^(?:NULL\b)/i,/^(?:NULLS\b)/i,/^(?:OFF\b)/i,/^(?:ON\b)/i,/^(?:ONLY\b)/i,/^(?:OF\b)/i,/^(?:OFFSET\b)/i,/^(?:OPEN\b)/i,/^(?:OPTION\b)/i,/^(?:OR\b)/i,/^(?:ORDER\b)/i,/^(?:OUTER\b)/i,/^(?:OVER\b)/i,/^(?:PATH\b)/i,/^(?:PARTITION\b)/i,/^(?:PERCENT\b)/i,/^(?:PIVOT\b)/i,/^(?:PLAN\b)/i,/^(?:PRIMARY\b)/i,/^(?:PRINT\b)/i,/^(?:PRIOR\b)/i,/^(?:QUERY\b)/i,/^(?:READ\b)/i,/^(?:RECORDSET\b)/i,/^(?:REDUCE\b)/i,/^(?:REFERENCES\b)/i,/^(?:REGEXP\b)/i,/^(?:REINDEX\b)/i,/^(?:RELATIVE\b)/i,/^(?:REMOVE\b)/i,/^(?:RENAME\b)/i,/^(?:REPEAT\b)/i,/^(?:REPLACE\b)/i,/^(?:REQUIRE\b)/i,/^(?:RESTORE\b)/i,/^(?:RETURN\b)/i,/^(?:RETURNS\b)/i,/^(?:RIGHT\b)/i,/^(?:ROLLBACK\b)/i,/^(?:ROLLUP\b)/i,/^(?:ROW\b)/i,/^(?:ROWS\b)/i,/^(?:SCHEMA(S)?)/i,/^(?:SEARCH\b)/i,/^(?:SEMI\b)/i,/^(?:SET\b)/i,/^(?:SETS\b)/i,/^(?:SHOW\b)/i,/^(?:SOME\b)/i,/^(?:SOURCE\b)/i,/^(?:STRATEGY\b)/i,/^(?:STORE\b)/i,/^(?:SUM\b)/i,/^(?:TOTAL\b)/i,/^(?:TABLE\b)/i,/^(?:TABLES\b)/i,/^(?:TARGET\b)/i,/^(?:TEMP\b)/i,/^(?:TEMPORARY\b)/i,/^(?:TEXTSTRING\b)/i,/^(?:THEN\b)/i,/^(?:TIMEOUT\b)/i,/^(?:TO\b)/i,/^(?:TOP\b)/i,/^(?:TRAN\b)/i,/^(?:TRANSACTION\b)/i,/^(?:TRIGGER\b)/i,/^(?:TRUE\b)/i,/^(?:TRUNCATE\b)/i,/^(?:UNION\b)/i,/^(?:UNIQUE\b)/i,/^(?:UNPIVOT\b)/i,/^(?:UPDATE\b)/i,/^(?:USE\b)/i,/^(?:USING\b)/i,/^(?:VALUE\b)/i,/^(?:VALUES\b)/i,/^(?:VERTEX\b)/i,/^(?:VIEW\b)/i,/^(?:WHEN\b)/i,/^(?:WHERE\b)/i,/^(?:WHILE\b)/i,/^(?:WITH\b)/i,/^(?:WORK\b)/i,/^(?:(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?)/i,/^(?:->)/i,/^(?:#)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:\*)/i,/^(?:\/)/i,/^(?:%)/i,/^(?:!===)/i,/^(?:===)/i,/^(?:!==)/i,/^(?:==)/i,/^(?:>=)/i,/^(?:&)/i,/^(?:\|)/i,/^(?:<<)/i,/^(?:>>)/i,/^(?:>)/i,/^(?:<=)/i,/^(?:<>)/i,/^(?:<)/i,/^(?:=)/i,/^(?:!=)/i,/^(?:\()/i,/^(?:\))/i,/^(?:@)/i,/^(?:\{)/i,/^(?:\})/i,/^(?:\])/i,/^(?::-)/i,/^(?:\?-)/i,/^(?:\.\.)/i,/^(?:\.)/i,/^(?:,)/i,/^(?:::)/i,/^(?::)/i,/^(?:;)/i,/^(?:\$)/i,/^(?:\?)/i,/^(?:!)/i,/^(?:\^)/i,/^(?:~)/i,/^(?:[0-9]*[a-zA-Z_]+[a-zA-Z_0-9]*)/i,/^(?:$)/i,/^(?:.)/i],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278],inclusive:!0}}};return Tt}();b4.lexer=Di;function ar(){this.yy={}}return ar.prototype=b4,b4.Parser=ar,new ar}();typeof ht<"u"&&typeof exports<"u"&&(exports.parser=Mt,exports.Parser=Mt.Parser,exports.parse=function(){return Mt.parse.apply(Mt,arguments)},exports.main=function(n){n[1]||(console.log("Usage: "+n[0]+" FILE"),process.exit(1));var t=ht("fs").readFileSync(ht("path").normalize(n[1]),"utf8");return exports.parser.parse(t)},typeof module<"u"&&ht.main===module&&exports.main(process.argv.slice(1))),l.prettyflag=!1,l.pretty=function(e,n){var t=l.prettyflag;l.prettyflag=!n;var r=l.parse(e).toString();return l.prettyflag=t,r};var M1=l.utils={};function m4(e){return"(y="+e+",y===y?y:undefined)"}function Dt(e,n){return"(y="+e+',typeof y=="undefined"?undefined:'+n+")"}function ee(){return!0}function Mi(){}var C2=M1.escapeq=function(e){return(""+e).replace(/["'\\\n\r\u2028\u2029]/g,function(n){switch(n){case'"':case"'":case"\\":return"\\"+n;case`
`:return"\\n";case"\r":return"\\r";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029"}})},ur=M1.undoubleq=function(e){return e.replace(/(\')/g,"''")},si=M1.doubleq=function(e){return e.replace(/(\'\')/g,"\\'")},Ui=M1.doubleqq=function(e){return e.replace(/'/g,"\\'")},P3=function(e){return e[0]==="\uFEFF"&&(e=e.substr(1)),e};M1.global=function(){return typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:Function("return this")()}();var yi=M1.isNativeFunction=function(e){return typeof e=="function"&&!!~e.toString().indexOf("[native code]")};M1.isWebWorker=function(){try{var e=M1.global.importScripts;return M1.isNativeFunction(e)}catch{return!1}}(),M1.isNode=function(){try{return!(typeof process>"u"||!process.versions||!process.versions.node)}catch{return!1}}(),M1.isBrowser=function(){try{return M1.isNativeFunction(M1.global.location.reload)}catch{return!1}}(),M1.isBrowserify=function(){return M1.isBrowser&&typeof process<"u"&&process.browser}(),M1.isRequireJS=function(){return M1.isBrowser&&typeof ht=="function"&&typeof ht.specified=="function"}(),M1.isMeteor=function(){return typeof Meteor<"u"&&Meteor.release}(),M1.isMeteorClient=M1.isMeteorClient=function(){return M1.isMeteor&&Meteor.isClient}(),M1.isMeteorServer=function(){return M1.isMeteor&&Meteor.isServer}(),M1.isCordova=function(){return typeof cordova=="object"}(),M1.isReactNative=function(){var e=!1;return e}(),M1.hasIndexedDB=function(){return!!M1.global.indexedDB}(),M1.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"};const Fi=/^[a-z]+:\/\//i;let G3=M1.loadFile=function(e,n,t,r){var i,s;if(!(M1.isNode||M1.isMeteorServer)){if(M1.isCordova){M1.global.requestFileSystem(LocalFileSystem.PERSISTENT,0,function(g){g.root.getFile(e,{create:!1},function(m){m.file(function(b){var c=new FileReader;c.onloadend=function(d){t(P3(this.result))},c.readAsText(b)})})});return}if(typeof e=="string"){if(e.substr(0,1)==="#"&&typeof document<"u"){i=document.querySelector(e).textContent,t(i);return}fr(e,g=>t(P3(g)),r,n);return}if(e instanceof Event){var u=e.target.files,f=new FileReader,h=u[0].name;f.onload=function(g){var m=g.target.result;t(P3(m))},f.readAsText(u[0])}fr(e,g=>t(P3(g)),r,n)}},ai=typeof fetch<"u"?fetch:null;async function fr(e,n,t,r){return r?cr(e,n,t):await cr(e,n,t)}function cr(e,n,t){return ai(e).then(r=>r.arrayBuffer()).then(r=>{var i=new Uint8Array(r),s=[...i].map(u=>String.fromCharCode(u)).join("");n(s)}).catch(r=>{if(t)return t(r);throw console.error(r),r})}var Pi=M1.loadBinaryFile=function(e,n,t,r=i=>{throw i}){var i;if(!(M1.isNode||M1.isMeteorServer))if(typeof e=="string"){var s=new XMLHttpRequest;s.open("GET",e,n),s.responseType="arraybuffer",s.onload=function(){for(var g=new Uint8Array(s.response),m=[],b=0;b<g.length;++b)m[b]=String.fromCharCode(g[b]);t(m.join(""))},s.onerror=r,s.send()}else if(e instanceof Event){var u=e.target.files,f=new FileReader,h=u[0].name;f.onload=function(g){var m=g.target.result;t(m)},f.onerror=r,f.readAsArrayBuffer(u[0])}else e instanceof Blob&&t(e)},Gi=M1.removeFile=function(e,n){if(!M1.isNode)throw new Error("You can remove files only in Node.js and Apache Cordova")},Bi=M1.deleteFile=function(e,n){};M1.autoExtFilename=function(e,n,t){return t=t||{},typeof e!="string"||e.match(/^[A-Z]+:\/\/|\n|\..{2,6}$/i)||t.autoExt===0||t.autoExt===!1?e:e+"."+n};var Ji=M1.fileExists=function(e,n){if(!M1.isNode)throw new Error("You can use exists() only in Node.js or Apach Cordova")},Hi=M1.saveFile=function(e,n,t,r){var i=1;if(e===void 0)i=n,t&&(i=t(i));else if(!M1.isNode){var s={disableAutoBom:!1};l.utils.extend(s,r);var u=new Blob([n],{type:"text/plain;charset=utf-8"});Qt(u,e,s.disableAutoBom),t&&(i=t(i))}return i},ot=M1.hash=function(e){for(var n=2166136261,t=e.length;t;)n^=e.charCodeAt(--t),n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24);return n},oi=M1.arrayUnion=function(e,n){var t=n.slice(0);return e.forEach(function(r){t.indexOf(r)<0&&t.push(r)}),t},li=M1.arrayDiff=function(e,n){return e.filter(function(t){return n.indexOf(t)<0})},ui=M1.arrayIntersect=function(e,n){var t=[];return e.forEach(function(r){var i=!1;n.forEach(function(s){i=i||r===s}),i&&t.push(r)}),t},fi=M1.arrayUnionDeep=function(e,n){var t=n.slice(0);return e.forEach(function(r){var i=!1;t.forEach(function(s){i=i||Xt(r,s)}),i||t.push(r)}),t},ci=M1.arrayExceptDeep=function(e,n){var t=[];return e.forEach(function(r){var i=!1;n.forEach(function(s){i=i||Xt(r,s)}),i||t.push(r)}),t},hi=M1.arrayIntersectDeep=function(e,n){var t=[];return e.forEach(function(r){var i=!1;n.forEach(function(s){i=i||Xt(r,s,!0)}),i&&t.push(r)}),t},Wt=M1.cloneDeep=function e(n){if(n===null||typeof n!="object")return n;if(n instanceof Date)return new Date(n);if(n instanceof String)return n.toString();if(n instanceof Number)return+n;var t=new n.constructor;for(var r in n)n.hasOwnProperty(r)&&(t[r]=e(n[r]));return t},Xt=M1.deepEqual=function(e,n){if(e===n)return!0;if(typeof e=="object"&&e!==null&&typeof n=="object"&&n!==null){if(Object.keys(e).length!==Object.keys(n).length)return!1;for(var t in e)if(!Xt(e[t],n[t]))return!1;return!0}return!1},S4=M1.distinctArray=function(e){for(var n={},t=0,r=e.length;t<r;t++){var i;typeof e[t]=="object"?i=Object.keys(e[t]).sort().map(function(f){return f+"`"+e[t][f]}).join("`"):i=e[t],n[i]=e[t]}var s=[];for(var u in n)s.push(n[u]);return s},V2=M1.extend=function(n,t){n=n||{};for(var r in t)t.hasOwnProperty(r)&&(n[r]=t[r]);return n},di=M1.getValueOf=function(e){return typeof e=="object"&&(e instanceof String||e instanceof Number)?e.valueOf():e},pi=M1.flatArray=function(e){if(!e||e.length===0)return[];if(typeof e=="object"&&e instanceof l.Recordset)return e.data.map(function(t){return di(t[e.columns[0].columnid])});var n=Object.keys(e[0])[0];return n===void 0?[]:e.map(function(t){return t[n]})},ji=M1.arrayOfArrays=function(e){return e.map(function(n){var t=[];for(var r in n)t.push(n[r]);return t})};Array.isArray||(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});var Yi=M1.xlsnc=function(e){var n="";if(e>701){let r=((e-26)/676|0)-1;n=String.fromCharCode(65+r%26),e=e%(26*26)}var t=String.fromCharCode(65+e%26);return e>=26&&(e=(e/26|0)-1,t=String.fromCharCode(65+e%26)+t,e>26&&(e=(e/26|0)-1,t=String.fromCharCode(65+e%26)+t)),n+t},Wi=M1.xlscn=function(e){var n=e.charCodeAt(0)-65;return e.length>1&&(n=(n+1)*26+e.charCodeAt(1)-65,e.length>2&&(n=(n+1)*26+e.charCodeAt(2)-65)),n},Xi=M1.domEmptyChildren=function(e){for(var n=e.childNodes.length;n--;)e.removeChild(e.lastChild)},T4={},Ki=M1.like=function(e,n,t){if(!T4[e]){t||(t="");for(var r=0,i="^";r<e.length;){var s=e[r],u="";r<e.length-1&&(u=e[r+1]),s===t?(i+="\\"+u,r++):s==="["&&u==="^"?(i+="[^",r++):s==="["||s==="]"?i+=s:s==="%"?i+="[\\s\\S]*":s==="_"?i+=".":"/.*+?|(){}".indexOf(s)>-1?i+="\\"+s:i+=s,r++}i+="$",T4[e]=RegExp(i,"i")}return(""+(n??"")).search(T4[e])>-1};M1.glob=function(e,n){for(var t=0,r="^";t<n.length;){var i=n[t],s="";t<n.length-1&&(s=n[t+1]),i==="["&&s==="^"?(r+="[^",t++):i==="["||i==="]"?r+=i:i==="*"?r+=".*":i==="?"?r+=".":"/.*+?|(){}".indexOf(i)>-1?r+="\\"+i:r+=i,t++}return r+="$",(""+(e||"")).toUpperCase().search(RegExp(r.toUpperCase()))>-1},M1.findAlaSQLPath=function(){if(M1.isWebWorker)return"";if(M1.isMeteorClient)return"/packages/dist/";if(M1.isMeteorServer)return"assets/packages/dist/";if(M1.isNode)return ii;if(M1.isBrowser)for(var e=document.getElementsByTagName("script"),n=0;n<e.length;n++){if(e[n].src.substr(-16).toLowerCase()==="alasql-worker.js")return e[n].src.substr(0,e[n].src.length-16);if(e[n].src.substr(-20).toLowerCase()==="alasql-worker.min.js")return e[n].src.substr(0,e[n].src.length-20);if(e[n].src.substr(-9).toLowerCase()==="alasql.js")return e[n].src.substr(0,e[n].src.length-9);if(e[n].src.substr(-13).toLowerCase()==="alasql.min.js")return e[n].src.substr(0,e[n].src.length-13)}return""};var p3=function(){var e=l.private.externalXlsxLib||M1.global.XLSX||null;if(e)return e;if(M1.isNode||M1.isBrowserify||M1.isMeteorServer,!e)throw new Error("Please include the xlsx.js library");return e};l.path=l.utils.findAlaSQLPath(),l.utils.uncomment=function(e){e=("__"+e+"__").split("");for(var n=!1,t,r=!1,i=!1,s=0,u=e.length;s<u;s++){var f=e[s-1]!=="\\"||e[s-2]==="\\";n?e[s]===t&&f&&(n=!1):r?e[s]==="*"&&e[s+1]==="/"?(e[s]=e[s+1]="",r=!1,s++):e[s]="":i?((e[s+1]===`
`||e[s+1]==="\r"||e.length-2===s)&&(i=!1),e[s]=""):e[s]==='"'||e[s]==="'"?(n=!0,t=e[s]):e[s]==="["&&e[s-1]!=="@"?(n=!0,t="]"):e[s]==="-"&&e[s+1]==="-"?(e[s]="",i=!0):e[s]==="/"&&e[s+1]==="*"&&(e[s]="",r=!0)}return e=e.join("").slice(2,-2),e},l.parser=Mt,l.parser.parseError=function(e,n){throw new Error("Have you used a reserved keyword without `escaping` it?\n"+e)},l.parse=function(e){return Mt.parse(l.utils.uncomment(e))},l.engines={},l.databases={},l.databasenum=0,l.options={errorlog:!1,valueof:!0,dropifnotexists:!1,datetimeformat:"sql",casesensitive:!0,logtarget:"output",logprompt:!0,progress:!1,modifier:void 0,columnlookup:10,autovertex:!0,usedbo:!0,autocommit:!0,cache:!0,tsql:!0,mysql:!0,postgres:!0,oracle:!0,sqlite:!0,orientdb:!0,nocount:!1,nan:!1,excel:{cellDates:!0},joinstar:"overwrite",loopbreak:1e5,dateAsString:!0},l.vars={},l.declares={},l.prompthistory=[],l.plugins={},l.from={},l.into={},l.fn={},l.aggr={},l.busy=0,l.MAXSQLCACHESIZE=1e4,l.DEFAULTDATABASEID="alasql",l.lastid=0,l.buffer={},l.private={externalXlsxLib:null},l.setXLSX=function(e){l.private.externalXlsxLib=e},l.use=function(e){if(e||(e=l.DEFAULTDATABASEID),l.useid!==e)if(l.databases[e]!==void 0){l.useid=e;let n=l.databases[l.useid];l.tables=n.tables,n.resetSqlCache(),l.options.usedbo&&(l.databases.dbo=n)}else throw Error("Database does not exist: "+e)},l.autoval=function(e,n,t,r){var i=r?l.databases[r]:l.databases[l.useid];if(!i.tables[e])throw new Error("Tablename not found: "+e);if(!i.tables[e].identities[n])throw new Error("Colname not found: "+n);return t?i.tables[e].identities[n].value||null:i.tables[e].identities[n].value-i.tables[e].identities[n].step||null},l.exec=function(e,n,t,r){if(typeof n=="function"&&(r=t,t=n,n={}),delete l.error,n=n||{},l.options.errorlog)try{return l.dexec(l.useid,e,n,t,r)}catch(i){l.error=i,t&&t(null,l.error)}else return l.dexec(l.useid,e,n,t,r)};function hr(e){e&&l.options.cache&&e&&e.query&&e.query.data&&(e.query.data=[])}l.dexec=function(e,n,t,r,i){var s=l.databases[e],u=ot(n);if(l.options.cache){let m=s.sqlCache[u];if(m&&s.dbversion===m.dbversion){var f=m(t,r);return hr(m),f}}let h=s.astCache[u];if(l.options.cache&&!h?(h=l.parse(n),h&&(s.astCache[u]=h)):h=l.parse(n),!!h.statements){if(h.statements.length===0)return 0;if(h.statements.length===1){if(h.statements[0].compile){var g=h.statements[0].compile(e,t);if(!g)return;g.sql=n,g.dbversion=s.dbversion,l.options.cache&&(s.sqlCacheSize>l.MAXSQLCACHESIZE&&s.resetSqlCache(),s.sqlCacheSize++,s.sqlCache[u]=g);var f=l.res=g(t,r,i);return hr(g),f}l.precompile(h.statements[0],l.useid,t);var f=l.res=h.statements[0].execute(e,t,r,i);return f}if(r){l.adrun(e,h,t,r,i);return}return l.drun(e,h,t,r,i)}},l.drun=function(e,n,t,r,i){var s=l.useid;s!==e&&l.use(e);for(var u=[],f=0,h=n.statements.length;f<h;f++)if(n.statements[f])if(n.statements[f].compile){var g=n.statements[f].compile(l.useid);u.push(l.res=g(t,null,i))}else l.precompile(n.statements[f],l.useid,t),u.push(l.res=n.statements[f].execute(l.useid,t));return s!==e&&l.use(s),r&&r(u),l.res=u,u},l.adrun=function(e,n,t,r,i){var s=0,u=n.statements.length;l.options.progress!==!1&&l.options.progress(u,s++);var f=l.useid;f!==e&&l.use(e);var h=[];function g(m){m!==void 0&&h.push(m);var b=n.statements.shift();if(!b){f!==e&&l.use(f),r(h);return}if(b.compile){var c=b.compile(l.useid);c(t,g,i),l.options.progress!==!1&&l.options.progress(u,s++);return}l.precompile(n.statements[0],l.useid,t),b.execute(l.useid,t,g),l.options.progress!==!1&&l.options.progress(u,s++)}g()},l.compile=function(e,n){n=n||l.useid;let t=l.parse(e);if(t.statements.length!==1)throw new Error("Cannot compile, because number of statements in SQL is not equal to 1");var r=t.statements[0].compile(n);return r.promise=function(i){return new Promise(function(s,u){r(i,function(f,h){h?u(h):s(f)})})},r},M1.global.Promise||(M1.global.Promise=Promise);var dr=function(e,n,t,r){return new M1.global.Promise(function(i,s){l(e,n,function(u,f){f?s(f):(t&&r&&l.options.progress!==!1&&l.options.progress(t,r),i(u))})})};const bi=(e,n)=>{var t=n.resolve([]);return e.forEach(r=>{t=t.then(i=>dr(r.sql,r.params,r.i,r.length).then(s=>[...i,s]))}),t};var Ei=function(e){if(!(e.length<1)){for(var n,t,r,i=[],s=0;s<e.length;s++){if(n=e[s],typeof n=="string"&&(n=[n]),!M1.isArray(n)||n.length<1||2<n.length)throw new Error("Error in .promise parameter");t=n[0],r=n[1]||void 0,i.push({sql:t,params:r,i:s,length:e.length})}return bi(i,M1.global.Promise)}};l.promise=function(e,n){if(typeof Promise>"u")throw new Error("Please include a Promise/A+ library");if(typeof e=="string")return dr(e,n);if(!M1.isArray(e)||e.length<1||typeof n<"u")throw new Error("Error in .promise parameters");return Ei(e)};var b3=l.Database=function(e){var n=this;if(n===l)if(e){if(n=l.databases[e],l.databases[e]=n,!n)throw new Error(`Database ${e} not found`)}else n=l.databases.alasql,l.options.tsql&&(l.databases.tempdb=l.databases.alasql);return e||(e="db"+l.databasenum++),n.databaseid=e,l.databases[e]=n,n.dbversion=0,n.tables={},n.views={},n.triggers={},n.indices={},n.objects={},n.counter=0,n.resetSqlCache(),n};b3.prototype.resetSqlCache=function(){this.sqlCache={},this.sqlCacheSize=0,this.astCache={}},b3.prototype.exec=function(e,n,t){return l.dexec(this.databaseid,e,n,t)},b3.prototype.autoval=function(e,n,t){return l.autoval(e,n,t,this.databaseid)},b3.prototype.transaction=function(e){var n=new l.Transaction(this.databaseid),t=e(n);return t};class v4{transactionid=Date.now();committed=!1;bank;constructor(n){this.databaseid=n,this.dbversion=l.databases[n].dbversion,this.bank=JSON.stringify(l.databases[n])}commit(){this.committed=!0,l.databases[this.databaseid].dbversion=Date.now(),delete this.bank}rollback(){if(!this.committed)l.databases[this.databaseid]=JSON.parse(this.bank),delete this.bank;else throw new Error("Transaction already commited")}exec(n,t,r){return l.dexec(this.databaseid,n,t,r)}}v4.prototype.executeSQL=v4.prototype.exec,l.Transaction=v4;var pr=l.Table=function(e){this.data=[],this.columns=[],this.xcolumns={},this.inddefs={},this.indices={},this.uniqs={},this.uniqdefs={},this.identities={},this.checks=[],this.checkfns=[],this.beforeinsert={},this.afterinsert={},this.insteadofinsert={},this.beforedelete={},this.afterdelete={},this.insteadofdelete={},this.beforeupdate={},this.afterupdate={},this.insteadofupdate={},Object.assign(this,e)};pr.prototype.indexColumns=function(){var e=this;e.xcolumns={},e.columns.forEach(function(n){e.xcolumns[n.columnid]=n})};class gi{constructor(n){this.columns=[],this.xcolumns={},this.query=[],Object.assign(this,n)}}l.View=gi;class br{constructor(n){this.alasql=l,this.columns=[],this.xcolumns={},this.selectGroup=[],this.groupColumns={},Object.assign(this,n)}}class mi{constructor(n){Object.assign(this,n)}}l.Recordset=mi,l.Query=br;class Si{constructor(n){Object.assign(this,n)}toString(){}toType(){}toJS(){}exec(){}compile(){}}var T={extend:Object.assign,casesensitive:l.options.casesensitive,Base:Si};Mt.yy=l.yy=T,T.Statements=class{constructor(n){Object.assign(this,n)}toString(){return this.statements.map(n=>n.toString()).join("; ")}compile(n){const t=this.statements.map(r=>r.compile(n));return t.length===1?t[0]:(r,i)=>{const s=t.map(u=>u(r));return i&&i(s),s}}},T.Search=class{constructor(n){Object.assign(this,n)}toString(){let n="SEARCH ";return this.selectors&&(n+=this.selectors.toString()),this.from&&(n+="FROM "+this.from.toString()),n}toJS(n){return`this.queriesfn[${this.queriesidx-1}](this.params,null,${n})`}compile(n){var t=n,r=(i,s)=>{var u;return this.#e(t,i,function(f){u=mr(r.query,f),s&&(u=s(u))}),u};return r.query={},r}#e(n,t,r){var i,s={},u,f=Wt(this.selectors);function h(v,E,O){var R,A,N,w=v[E],T1=l.options.loopbreak||1e5;if(w.selid){if(w.selid==="PATH"){for(var v1=[{node:O,stack:[]}],O1={},G1=l.databases[l.useid].objects;v1.length>0;){var B1=v1.shift(),Y1=B1.node,k=B1.stack,N=h(w.args,0,Y1);if(N.length>0){if(E+1+1>v.length)return k;var s2=[];return k&&k.length>0&&k.forEach(function(k2){s2=s2.concat(h(v,E+1,k2))}),s2}else{if(typeof O1[Y1.$id]<"u")continue;O1[Y1.$id]=!0,Y1.$out&&Y1.$out.length>0&&Y1.$out.forEach(function(k2){var F2=G1[k2],rt=k.concat(F2);rt.push(G1[F2.$out[0]]),v1.push({node:G1[F2.$out[0]],stack:rt})})}}return[]}if(w.selid==="NOT"){var A=h(w.args,0,O);return A.length>0?[]:E+1+1>v.length?[O]:h(v,E+1,O)}else if(w.selid==="DISTINCT"){var A;if(typeof w.args>"u"||w.args.length===0?A=S4(O):A=h(w.args,0,O),A.length===0)return[];var g2=S4(A);return E+1+1>v.length?g2:h(v,E+1,g2)}else if(w.selid==="AND"){var g2=!0;return w.args.forEach(function(k2){g2=g2&&h(k2,0,O).length>0}),g2?E+1+1>v.length?[O]:h(v,E+1,O):[]}else if(w.selid==="OR"){var g2=!1;return w.args.forEach(function(k2){g2=g2||h(k2,0,O).length>0}),g2?E+1+1>v.length?[O]:h(v,E+1,O):[]}else if(w.selid==="ALL"){var A=h(w.args[0],0,O);return A.length===0?[]:E+1+1>v.length?A:h(v,E+1,A)}else if(w.selid==="ANY"){var A=h(w.args[0],0,O);return A.length===0?[]:E+1+1>v.length?[A[0]]:h(v,E+1,[A[0]])}else if(w.selid==="UNIONALL"){var A=[];return w.args.forEach(function(k2){A=A.concat(h(k2,0,O))}),A.length===0?[]:E+1+1>v.length?A:h(v,E+1,A)}else if(w.selid==="UNION"){var A=[];w.args.forEach(function(k2){A=A.concat(h(k2,0,O))});var A=S4(A);return A.length===0?[]:E+1+1>v.length?A:h(v,E+1,A)}else if(w.selid==="IF"){var A=h(w.args,0,O);return A.length===0?[]:E+1+1>v.length?[O]:h(v,E+1,O)}else if(w.selid==="REPEAT"){var K1,V1,T2=w.args[0].value;w.args[1]?V1=w.args[1].value:V1=T2,w.args[2]&&(K1=w.args[2].variable);var p2=[];if(T2===0&&(E+1+1>v.length?p2=[O]:(K1&&(l.vars[K1]=0),p2=p2.concat(h(v,E+1,O)))),V1>0)for(var O2=[{value:O,lvl:1}],N2=0;O2.length>0;){var A=O2[0];if(O2.shift(),A.lvl<=V1){K1&&(l.vars[K1]=A.lvl);var A2=h(w.sels,0,A.value);A2.forEach(function(k2){O2.push({value:k2,lvl:A.lvl+1})}),A.lvl>=T2&&(E+1+1>v.length?p2=p2.concat(A2):A2.forEach(function(k2){p2=p2.concat(h(v,E+1,k2))}))}if(N2++,N2>T1)throw new Error("Infinite loop brake. Number of iterations = "+N2)}return p2}else if(w.selid==="OF"){if(E+1+1>v.length)return[O];var w2=[];return Object.keys(O).forEach(function(Y2){l.vars[w.args[0].variable]=Y2,w2=w2.concat(h(v,E+1,O[Y2]))}),w2}else if(w.selid==="TO"){var Z2=l.vars[w.args[0]],X2=[];if(Z2!==void 0?X2=Z2.slice(0):X2=[],X2.push(O),E+1+1>v.length)return[O];l.vars[w.args[0]]=X2;var w2=h(v,E+1,O);return l.vars[w.args[0]]=Z2,w2}else if(w.selid==="ARRAY"){var A=h(w.args,0,O);if(A.length>0)R=A;else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="SUM"){var A=h(w.args,0,O);if(A.length>0)var R=A.reduce(function(F2,rt){return F2+rt},0);else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="AVG"){if(A=h(w.args,0,O),A.length>0)R=A.reduce(function(Y2,k2){return Y2+k2},0)/A.length;else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="COUNT"){if(A=h(w.args,0,O),A.length>0)R=A.length;else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="FIRST"){if(A=h(w.args,0,O),A.length>0)R=A[0];else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="LAST"){if(A=h(w.args,0,O),A.length>0)R=A[A.length-1];else return[];return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="MIN"){if(A=h(w.args,0,O),A.length===0)return[];var R=A.reduce(function(k2,F2){return Math.min(k2,F2)},1/0);return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="MAX"){var A=h(w.args,0,O);if(A.length===0)return[];var R=A.reduce(function(F2,rt){return Math.max(F2,rt)},-1/0);return E+1+1>v.length?[R]:h(v,E+1,R)}else if(w.selid==="PLUS"){var p2=[],O2=h(w.args,0,O).slice();E+1+1>v.length?p2=p2.concat(O2):O2.forEach(function(rt){p2=p2.concat(h(v,E+1,rt))});for(var N2=0;O2.length>0;){var A=O2.shift();if(A=h(w.args,0,A),O2=O2.concat(A),E+1+1>v.length?p2=p2.concat(A):A.forEach(function(zt){var ut=h(v,E+1,zt);p2=p2.concat(ut)}),N2++,N2>T1)throw new Error("Infinite loop brake. Number of iterations = "+N2)}return p2}else if(w.selid==="STAR"){var p2=[];p2=h(v,E+1,O);var O2=h(w.args,0,O).slice();E+1+1>v.length?p2=p2.concat(O2):O2.forEach(function(rt){p2=p2.concat(h(v,E+1,rt))});for(var N2=0;O2.length>0;){var A=O2[0];if(O2.shift(),A=h(w.args,0,A),O2=O2.concat(A),E+1+1<=v.length&&A.forEach(function(zt){p2=p2.concat(h(v,E+1,zt))}),N2++,N2>T1)throw new Error("Infinite loop brake. Number of iterations = "+N2)}return p2}else if(w.selid==="QUESTION"){var p2=[];p2=p2.concat(h(v,E+1,O));var A=h(w.args,0,O);return E+1+1<=v.length&&A.forEach(function(F2){p2=p2.concat(h(v,E+1,F2))}),p2}else if(w.selid==="WITH"){var A=h(w.args,0,O);if(A.length===0)return[];var N={status:1,values:A}}else{if(w.selid==="ROOT")return E+1+1>v.length?[O]:h(v,E+1,u);throw new Error("Wrong selector "+w.selid)}}else if(w.srchid)var N=l.srch[w.srchid.toUpperCase()](O,w.args,s,t);else throw new Error("Selector not found");typeof N>"u"&&(N={status:1,values:[O]});var g2=[];if(N.status===1){var D2=N.values;if(E+1+1>v.length)g2=D2;else for(var N2=0;N2<N.values.length;N2++)g2=g2.concat(h(v,E+1,D2[N2]))}return g2}if(f!==void 0&&f.length>0&&(f&&f[0]&&f[0].srchid==="PROP"&&f[0].args&&f[0].args[0]&&(f[0].args[0].toUpperCase()==="XML"?(s.mode="XML",f.shift()):f[0].args[0].toUpperCase()==="HTML"?(s.mode="HTML",f.shift()):f[0].args[0].toUpperCase()==="JSON"&&(s.mode="JSON",f.shift())),f.length>0&&f[0].srchid==="VALUE"&&(s.value=!0,f.shift())),this.from instanceof T.Column){var g=this.from.databaseid||n;u=l.databases[g].tables[this.from.columnid].data}else if(this.from instanceof T.FuncValue&&l.from[this.from.funcid.toUpperCase()]){var m=this.from.args.map(function(v){var E=v.toJS(),O=new Function("params,alasql","var y;return "+E).bind(this);return O(t,l)});u=l.from[this.from.funcid.toUpperCase()].apply(this,m)}else if(typeof this.from>"u")u=l.databases[n].objects;else{var b=new Function("params,alasql","var y;return "+this.from.toJS());u=b(t,l),typeof Mongo=="object"&&typeof Mongo.Collection!="object"&&u instanceof Mongo.Collection&&(u=u.find().fetch())}if(f!==void 0&&f.length>0?i=h(f,0,u):i=u,this.into){var c,d;typeof this.into.args[0]<"u"&&(c=new Function("params,alasql","var y;return "+this.into.args[0].toJS())(t,l)),typeof this.into.args[1]<"u"&&(d=new Function("params,alasql","var y;return "+this.into.args[1].toJS())(t,l)),i=l.into[this.into.funcid.toUpperCase()](c,d,i,[],r)}else s.value&&i.length>0&&(i=i[0]),r&&(i=r(i));return i}},l.srch={PROP(e,n,t){if(t.mode==="XML"){const r=e.children.filter(i=>i.name.toUpperCase()===n[0].toUpperCase());return{status:r.length?1:-1,values:r}}else return typeof e!="object"||e===null||typeof n!="object"||typeof e[n[0]]>"u"?{status:-1,values:[]}:{status:1,values:[e[n[0]]]}},APROP(e,n){return typeof e!="object"||e===null||typeof n!="object"||typeof e[n[0]]>"u"?{status:1,values:[void 0]}:{status:1,values:[e[n[0]]]}},EQ(e,n,t,r){var i=n[0].toJS("x",""),s=new Function("x,alasql,params","return "+i);return e===s(e,l,r)?{status:1,values:[e]}:{status:-1,values:[]}},LIKE(e,n,t,r){var i=n[0].toJS("x",""),s=new Function("x,alasql,params","return "+i);return e.toUpperCase().match(new RegExp("^"+s(e,l,r).toUpperCase().replace(/%/g,".*").replace(/\?|_/g,".")+"$"),"g")?{status:1,values:[e]}:{status:-1,values:[]}},ATTR(e,n,t){if(t.mode==="XML")return typeof n>"u"?{status:1,values:[e.attributes]}:typeof e=="object"&&typeof e.attributes=="object"&&typeof e.attributes[n[0]]<"u"?{status:1,values:[e.attributes[n[0]]]}:{status:-1,values:[]};throw new Error("ATTR is not using in usual mode")},CONTENT(e,n,t){if(t.mode!=="XML")throw new Error("ATTR is not using in usual mode");return{status:1,values:[e.content]}},SHARP(e,n){const t=l.databases[l.useid].objects[n[0]];return e!==void 0&&e===t?{status:1,values:[e]}:{status:-1,values:[]}},PARENT(){return console.error("PARENT not implemented",arguments),{status:-1,values:[]}},CHILD(e,n,t){return typeof e=="object"?Array.isArray(e)?{status:1,values:e}:t.mode==="XML"?{status:1,values:Object.keys(e.children).map(function(r){return e.children[r]})}:{status:1,values:Object.keys(e).map(function(r){return e[r]})}:{status:1,values:[]}},KEYS(e){return typeof e=="object"&&e!==null?{status:1,values:Object.keys(e)}:{status:1,values:[]}},WHERE(e,n,t,r){var i=n[0].toJS("x",""),s=new Function("x,alasql,params","return "+i);return s(e,l,r)?{status:1,values:[e]}:{status:-1,values:[]}},NAME(e,n){return e.name===n[0]?{status:1,values:[e]}:{status:-1,values:[]}},CLASS(e,n){return e.$class==n?{status:1,values:[e]}:{status:-1,values:[]}},VERTEX(e){return e.$node==="VERTEX"?{status:1,values:[e]}:{status:-1,values:[]}},INSTANCEOF(e,n){return e instanceof l.fn[n[0]]?{status:1,values:[e]}:{status:-1,values:[]}},EDGE(e){return e.$node==="EDGE"?{status:1,values:[e]}:{status:-1,values:[]}},EX(e,n,t,r){var i=n[0].toJS("x",""),s=new Function("x,alasql,params","return "+i);return{status:1,values:[s(e,l,r)]}},RETURN(e,n,t,r){var i={};return n&&n.length>0&&n.forEach(function(s){var u=s.toJS("x",""),f=new Function("x,alasql,params","return "+u);typeof s.as>"u"&&(s.as=s.toString()),i[s.as]=f(e,l,r)}),{status:1,values:[i]}},REF(e){return{status:1,values:[l.databases[l.useid].objects[e]]}},OUT(e){if(e.$out&&e.$out.length>0){var n=e.$out.map(function(t){return l.databases[l.useid].objects[t]});return{status:1,values:n}}else return{status:-1,values:[]}},OUTOUT(e){if(e.$out&&e.$out.length>0){var n=[];return e.$out.forEach(function(t){var r=l.databases[l.useid].objects[t];r&&r.$out&&r.$out.length>0&&r.$out.forEach(function(i){n=n.concat(l.databases[l.useid].objects[i])})}),{status:1,values:n}}else return{status:-1,values:[]}},IN(e){if(e.$in&&e.$in.length>0){var n=e.$in.map(function(t){return l.databases[l.useid].objects[t]});return{status:1,values:n}}else return{status:-1,values:[]}},ININ(e){if(e.$in&&e.$in.length>0){var n=[];return e.$in.forEach(function(t){var r=l.databases[l.useid].objects[t];r&&r.$in&&r.$in.length>0&&r.$in.forEach(function(i){n=n.concat(l.databases[l.useid].objects[i])})}),{status:1,values:n}}else return{status:-1,values:[]}},AS(e,n){return l.vars[n[0]]=e,{status:1,values:[e]}},AT(e,n){var t=l.vars[n[0]];return{status:1,values:[t]}},CLONEDEEP(e){var n=Wt(e);return{status:1,values:[n]}},SET(e,n,t,r){var i=n.map(function(u){return u.method==="@"?`alasql.vars[${JSON.stringify(u.variable)}]=`+u.expression.toJS("x",""):u.method==="$"?`params[${JSON.stringify(u.variable)}]=`+u.expression.toJS("x",""):`x[${JSON.stringify(u.column.columnid)}]=`+u.expression.toJS("x","")}).join(";"),s=new Function("x,params,alasql",i);return s(e,r,l),{status:1,values:[e]}},ROW(e,n,t,r){var i="var y;return [";i+=n.map(f=>f.toJS("x","")).join(","),i+="]";var s=new Function("x,params,alasql",i),u=s(e,r,l);return{status:1,values:[u]}},D3(e){return e.$node!=="VERTEX"&&e.$node==="EDGE"&&(e.source=e.$in[0],e.target=e.$out[0]),{status:1,values:[e]}},ORDERBY(e,n){var t=e.sort(Ti(n));return{status:1,values:t}}};var Ti=function(e){if(e){if(typeof e?.[0]?.expression=="function"){var n=e[0].expression;return function(i,s){var u=n(i),f=n(s);return u>f?1:u===f?0:-1}}var t="",r="";return e.forEach(function(i){var s="";if(i.expression instanceof T.NumValue&&(i.expression=self.columns[i.expression.value-1]),i.expression instanceof T.Column){var u=i.expression.columnid;l.options.valueof&&(s=".valueOf()"),i.nocase&&(s+=".toUpperCase()"),u==="_"?(t+="if(a"+s+(i.direction==="ASC"?">":"<")+"b"+s+")return 1;",t+="if(a"+s+"==b"+s+"){"):t+=`if (
							(a[${JSON.stringify(u)}]||'')${s}
							${i.direction==="ASC"?">":"<"}
							(b[${JSON.stringify(u)}]||'')${s}
						) return 1;

						if(
							(a[${JSON.stringify(u)}]||'')${s}
							==
							(b[${JSON.stringify(u)}]||'')${s}
						){
						`}else s=".valueOf()",i.nocase&&(s+=".toUpperCase()"),t+=`
					if (
						(${i.toJS("a","")} || '')${s}
						${i.direction==="ASC"?">":"<"}
						(${i.toJS("b","")} || '')${s}
					) return 1;

					if (
						(${i.toJS("a","")} || '')${s} ==
						(${i.toJS("b","")} || '')${s}
					) {`;r+="}"}),t+="return 0;",t+=r+"return -1",new Function("a,b",t)}};function vi(e,n,t,r,i){e.sourceslen=e.sources.length;let s=e.sourceslen;e.query=e,e.A=r,e.B=i,e.cb=t,e.oldscope=n,e.queriesfn&&(e.sourceslen+=e.queriesfn.length,s+=e.queriesfn.length,e.queriesdata=[],e.queriesfn.forEach(function(f,h){f.query.params=e.params,Er([],-h-1,e)})),e.scope=n?Wt(n):{};let u;if(e.sources.forEach(function(f,h){f.query=e;var g=f.datafn(e,e.params,Er,h,l);typeof g<"u"&&((e.intofn||e.intoallfn)&&Array.isArray(g)&&(g=g.length),u=g),f.queriesdata=e.queriesdata}),e.sources.length==0||s===0)try{u=gr(e)}catch(f){if(t)return t(null,f);throw f}return u}function Er(e,n,t){if(n>=0){let r=t.sources[n];r.data=e,typeof r.data=="function"&&(r.getfn=r.data,r.dontcache=r.getfn.dontcache,["OUTER","RIGHT","ANTI"].includes(r.joinmode)&&(r.dontcache=!1),r.data={})}else t.queriesdata[-n-1]=pi(e);if(t.sourceslen--,!(t.sourceslen>0))return gr(t)}function gr(e){var n=e.scope,t;Oi(e),e.data=[],e.xgroups={},e.groups=[];var r=0;if(Kt(e,n,r),e.groupfn){if(e.data=[],e.groups.length===0&&e.allgroups.length===0){var i={};e.selectGroup.length>0&&e.selectGroup.forEach(function(N){N.aggregatorid=="COUNT"||N.aggregatorid=="SUM"||N.aggregatorid=="TOTAL"?i[N.nick]=0:i[N.nick]=void 0}),e.groups=[i]}if(e.aggrKeys.length>0){var s="";e.aggrKeys.forEach(function(N){s+=`
				g[${JSON.stringify(N.nick)}] = alasql.aggr[${JSON.stringify(N.funcid)}](undefined,g[${JSON.stringify(N.nick)}],3); `});var u=new Function("g,params,alasql","var y;"+s)}for(var f=0,h=e.groups.length;f<h;f++){var i=e.groups[f];if(u&&u(i,e.params,l),!e.havingfn||e.havingfn(i,e.params,l)){var g=e.selectgfn(i,e.params,l);for(const w in e.groupColumns)e.groupColumns[w]!==w&&g[e.groupColumns[w]]&&!e.groupColumns[e.groupColumns[w]]&&delete g[e.groupColumns[w]];e.data.push(g)}}}if(Ni(e),e.unionallfn){var m,b;if(e.corresponding)e.unionallfn.query.modifier||(e.unionallfn.query.modifier=void 0),m=e.unionallfn(e.params);else{e.unionallfn.query.modifier||(e.unionallfn.query.modifier="RECORDSET"),b=e.unionallfn(e.params),m=[],h=b.data.length;for(var f=0;f<h;f++){var c={};if(e.columns.length){t=Math.min(e.columns.length,b.columns.length);for(var d=0;d<t;d++)c[e.columns[d].columnid]=b.data[f][b.columns[d].columnid]}else{t=b.columns.length;for(var d=0;d<t;d++)c[b.columns[d].columnid]=b.data[f][b.columns[d].columnid]}m.push(c)}}e.data=e.data.concat(m)}else if(e.unionfn){if(e.corresponding)e.unionfn.query.modifier||(e.unionfn.query.modifier="ARRAY"),m=e.unionfn(e.params);else{e.unionfn.query.modifier||(e.unionfn.query.modifier="RECORDSET"),b=e.unionfn(e.params),m=[],h=b.data.length;for(var f=0;f<h;f++){if(c={},e.columns.length){t=Math.min(e.columns.length,b.columns.length);for(var d=0;d<t;d++)c[e.columns[d].columnid]=b.data[f][b.columns[d].columnid]}else{t=b.columns.length;for(var d=0;d<t;d++)c[b.columns[d].columnid]=b.data[f][b.columns[d].columnid]}m.push(c)}}e.data=fi(e.data,m)}else if(e.exceptfn){if(e.corresponding){e.exceptfn.query.modifier||(e.exceptfn.query.modifier="ARRAY");var m=e.exceptfn(e.params)}else{e.exceptfn.query.modifier||(e.exceptfn.query.modifier="RECORDSET");for(var b=e.exceptfn(e.params),m=[],f=0,h=b.data.length;f<h;f++){for(var c={},d=Math.min(e.columns.length,b.columns.length)-1;0<=d;d--)c[e.columns[d].columnid]=b.data[f][b.columns[d].columnid];m.push(c)}}e.data=ci(e.data,m)}else if(e.intersectfn){if(e.corresponding)e.intersectfn.query.modifier||(e.intersectfn.query.modifier=void 0),m=e.intersectfn(e.params);else for(e.intersectfn.query.modifier||(e.intersectfn.query.modifier="RECORDSET"),b=e.intersectfn(e.params),m=[],h=b.data.length,f=0;f<h;f++){for(c={},t=Math.min(e.columns.length,b.columns.length),d=0;d<t;d++)c[e.columns[d].columnid]=b.data[f][b.columns[d].columnid];m.push(c)}e.data=hi(e.data,m)}if(e.orderfn){if(e.explain)var v=Date.now();e.data=e.data.sort(e.orderfn),e.explain&&e.explaination.push({explid:e.explid++,description:"QUERY BY",ms:Date.now()-v})}if(Ai(e),typeof angular<"u"&&e.removeKeys.push("$$hashKey"),e.removeKeys.length>0){var E=e.removeKeys;if(t=E.length,t>0)for(h=e.data.length,f=0;f<h;f++)for(d=0;d<t;d++)delete e.data[f][E[d]];e.columns.length>0&&(e.columns=e.columns.filter(function(N){var w=!1;return E.forEach(function(T1){N.columnid==T1&&(w=!0)}),!w}))}if(typeof e.removeLikeKeys<"u"&&e.removeLikeKeys.length>0){for(var O=e.removeLikeKeys,f=0,h=e.data.length;f<h;f++){c=e.data[f];for(var R in c)for(d=0;d<e.removeLikeKeys.length;d++)l.utils.like(e.removeLikeKeys[d],R)&&delete c[R]}e.columns.length>0&&(e.columns=e.columns.filter(function(N){var w=!1;return O.forEach(function(T1){l.utils.like(T1,N.columnid)&&(w=!0)}),!w}))}if(e.pivotfn&&e.pivotfn(),e.unpivotfn&&e.unpivotfn(),e.intoallfn){var A=e.intoallfn(e.columns,e.cb,e.params,e.alasql);return A}if(e.intofn){for(h=e.data.length,f=0;f<h;f++)e.intofn(e.data[f],f,e.params,e.alasql);return e.cb&&e.cb(e.data.length,e.A,e.B),e.data.length}return A=e.data,e.cb&&(A=e.cb(e.data,e.A,e.B)),A}function Ai(e){if(e.limit){var n=0;e.offset&&(n=e.offset|0||0,n=n<0?0:n);var t;e.percent?t=(e.data.length*e.limit/100|0)+n:t=(e.limit|0)+n,e.data=e.data.slice(n,t)}}function Ni(e){if(e.distinct){for(var n={},t=Object.keys(e.data[0]||[]),r=0,i=e.data.length;r<i;r++){var s=t.map(function(f){return e.data[r][f]}).join("`");n[s]=e.data[r]}e.data=[];for(var u in n)e.data.push(n[u])}}var Oi=function(e){for(var n=0,t=e.sources.length;n<t;n++){var r=e.sources[n];if(delete r.ix,n>0&&r.optimization=="ix"&&r.onleftfn&&r.onrightfn){if(r.databaseid&&l.databases[r.databaseid].tables[r.tableid]){l.databases[r.databaseid].tables[r.tableid].indices||(e.database.tables[r.tableid].indices={});let f=l.databases[r.databaseid].tables[r.tableid].indices[ot(r.onrightfns+"`"+r.srcwherefns)];!l.databases[r.databaseid].tables[r.tableid].dirty&&f&&(r.ix=f)}if(!r.ix){r.ix={};let f={},h=0,g=r.data.length,m;for(;(m=r.data[h])||r.getfn&&(m=r.getfn(h))||h<g;){if(r.getfn&&!r.dontcache&&(r.data[h]=m),f[r.alias||r.tableid]=m,r.srcwherefn(f,e.params,l)){var i=r.onrightfn(f,e.params,l),s=r.ix[i];s||(s=r.ix[i]=[]),s.push(m)}h++}r.databaseid&&l.databases[r.databaseid].tables[r.tableid]&&(l.databases[r.databaseid].tables[r.tableid].indices[ot(r.onrightfns+"`"+r.srcwherefns)]=r.ix)}}else if(r.wxleftfn){if(l.databases[r.databaseid].engineid||(ixx=l.databases[r.databaseid].tables[r.tableid].indices[ot(r.wxleftfns+"`")]),!l.databases[r.databaseid].tables[r.tableid].dirty&&ixx)r.ix=ixx,r.data=r.ix[r.wxrightfn(null,e.params,l)];else{for(r.ix={},u={},j2=0,ilen=r.data.length,dataw;(dataw=r.data[j2])||r.getfn&&(dataw=r.getfn(j2))||j2<ilen;)r.getfn&&!r.dontcache&&(r.data[j2]=dataw),u[r.alias||r.tableid]=r.data[j2],i=r.wxleftfn(u,e.params,l),s=r.ix[i],s||(s=r.ix[i]=[]),s.push(r.data[j2]),j2++;l.databases[r.databaseid].engineid||(l.databases[r.databaseid].tables[r.tableid].indices[ot(r.wxleftfns+"`")]=r.ix)}r.srcwherefns&&(r.data?(u={},r.data=r.data.filter(function(f){return u[r.alias]=f,r.srcwherefn(u,e.params,l)})):r.data=[])}else if(r.srcwherefns&&!r.dontcache)if(r.data){var u={};r.data=r.data.filter(function(h){return u[r.alias]=h,r.srcwherefn(u,e.params,l)}),u={},j2=0,ilen=r.data.length;let f=[];for(;(dataw=r.data[j2])||r.getfn&&(dataw=r.getfn(j2))||j2<ilen;)r.getfn&&!r.dontcache&&(r.data[j2]=dataw),u[r.alias]=dataw,r.srcwherefn(u,e.params,l)&&f.push(dataw),j2++;r.data=f}else r.data=[];r.databaseid&&l.databases[r.databaseid].tables[r.tableid]}};function Kt(e,n,t){if(t>=e.sources.length)e.wherefn(n,e.params,l)&&(e.groupfn?e.groupfn(n,e.params,l):e.data.push(e.selectfn(n,e.params,l)));else if(e.sources[t].applyselect){var r=e.sources[t];r.applyselect(e.params,function(h){if(h.length>0)for(var g=0;g<h.length;g++)n[r.alias]=h[g],Kt(e,n,t+1);else r.applymode=="OUTER"&&(n[r.alias]={},Kt(e,n,t+1))},n)}else{let h=e.sources[t],g=e.sources[t+1],m=h.alias||h.tableid,b=!1,c=h.data,d=!1;(!h.getfn||h.getfn&&!h.dontcache)&&h.joinmode!="RIGHT"&&h.joinmode!="OUTER"&&h.joinmode!="ANTI"&&h.optimization=="ix"&&(c=h.ix[h.onleftfn(n,e.params,l)]||[],d=!0);let v=0;if(typeof c>"u")throw new Error("Data source number "+t+" in undefined");let E=c.length,O;for(;(O=c[v])||!d&&h.getfn&&(O=h.getfn(v))||v<E;){!d&&h.getfn&&!h.dontcache&&(c[v]=O),n[m]=O;var i=!h.onleftfn;if(!i){var s=h.onleftfn(n,e.params,l),u=h.onrightfn(n,e.params,l);(s instanceof String||s instanceof Number)&&(s=s.valueOf()),(u instanceof String||u instanceof Number)&&(u=s.valueOf()),i=s==u}i&&h.onmiddlefn(n,e.params,l)&&(h.joinmode!="SEMI"&&h.joinmode!="ANTI"&&Kt(e,n,t+1),h.joinmode!="LEFT"&&h.joinmode!="INNER"&&(O._rightjoin=!0),b=!0),v++}if((h.joinmode=="LEFT"||h.joinmode=="OUTER"||h.joinmode=="SEMI")&&!b&&(n[m]={},Kt(e,n,t+1)),t==0)for(var f=t+1;f<e.sources.length;f++){if(g.joinmode=="OUTER"||g.joinmode=="RIGHT"||g.joinmode=="ANTI"){n[h.alias]={};let R=0,A=g.data.length,N;for(;(N=g.data[R])||g.getfn&&(N=g.getfn(R))||R<A;)g.getfn&&!g.dontcache&&(g.data[R]=N),N._rightjoin?delete N._rightjoin:(n[g.alias]=N,Kt(e,n,f+1)),R++}h=e.sources[f],g=e.sources[f+1]}n[m]=void 0}}function Qi(e,n){var t=e.sources[n],r=e.sources[n+1];let i=t.onleftfn,s=t.onleftfns,u=t.onrightfn,f=t.onrightfns,h=t.optimization;t.onleftfn=r.onrightfn,t.onleftfns=r.onrightfns,t.onrightfn=r.onleftfn,t.onrightfns=r.onleftfns,t.optimization=r.optimization,r.onleftfn=i,r.onleftfns=s,r.onrightfn=u,r.onrightfns=f,r.optimization=h,e.sources[n]=r,e.sources[n+1]=t}T.Select=class{constructor(n){Object.assign(this,n)}toString(){var n;return n="",this.explain&&(n+="EXPLAIN "),n+="SELECT ",this.modifier&&(n+=this.modifier+" "),this.distinct&&(n+="DISTINCT "),this.top&&(n+="TOP "+this.top.value+" ",this.percent&&(n+="PERCENT ")),n+=this.columns.map(function(t){var r;return r=t.toString(),typeof t.as<"u"&&(r+=" AS "+t.as),r}).join(", "),this.from&&(n+=" FROM "+this.from.map(function(t){var r;return r=t.toString(),t.as&&(r+=" AS "+t.as),r}).join(",")),this.joins&&(n+=this.joins.map(function(t){var r;if(r=" ",t.joinmode&&(r+=t.joinmode+" "),t.table)r+="JOIN "+t.table.toString();else if(t.select)r+="JOIN ("+t.select.toString()+")";else if(t instanceof l.yy.Apply)r+=t.toString();else throw new Error("Wrong type in JOIN mode");return t.as&&(r+=" AS "+t.as),t.using&&(r+=" USING "+t.using.toString()),t.on&&(r+=" ON "+t.on.toString()),r}).join("")),this.where&&(n+=" WHERE "+this.where.toString()),this.group&&this.group.length>0&&(n+=" GROUP BY "+this.group.map(function(t){return t.toString()}).join(", ")),this.having&&(n+=" HAVING "+this.having.toString()),this.order&&this.order.length>0&&(n+=" ORDER BY "+this.order.map(function(t){return t.toString()}).join(", ")),this.limit&&(n+=" LIMIT "+this.limit.value),this.offset&&(n+=" OFFSET "+this.offset.value),this.union&&(n+=" UNION "+(this.corresponding?"CORRESPONDING ":"")+this.union.toString()),this.unionall&&(n+=" UNION ALL "+(this.corresponding?"CORRESPONDING ":"")+this.unionall.toString()),this.except&&(n+=" EXCEPT "+(this.corresponding?"CORRESPONDING ":"")+this.except.toString()),this.intersect&&(n+=" INTERSECT "+(this.corresponding?"CORRESPONDING ":"")+this.intersect.toString()),n}toJS(n){var t="alasql.utils.flatArray(this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+n+"))[0]";return t}compile(n,t){var r=l.databases[n],i=new br;if(i.removeKeys=[],i.aggrKeys=[],i.explain=this.explain,i.explaination=[],i.explid=1,i.modifier=this.modifier,i.database=r,this.compileWhereExists(i),this.compileQueries(i),i.defcols=this.compileDefCols(i,n),i.fromfn=this.compileFrom(i),this.joins&&this.compileJoins(i),i.rownums=[],this.compileSelectGroup0(i),this.group||i.selectGroup.length>0?i.selectgfns=this.compileSelectGroup1(i):i.selectfns=this.compileSelect1(i,t),this.compileRemoveColumns(i),this.where&&this.compileWhereJoins(i),i.wherefn=this.compileWhere(i),(this.group||i.selectGroup.length>0)&&(i.groupfn=this.compileGroup(i)),this.having&&(i.havingfn=this.compileHaving(i)),this.order&&(i.orderfn=this.compileOrder(i,t)),this.group||i.selectGroup.length>0?i.selectgfn=this.compileSelectGroup2(i):i.selectfn=this.compileSelect2(i,t),i.distinct=this.distinct,this.pivot&&(i.pivotfn=this.compilePivot(i)),this.unpivot&&(i.pivotfn=this.compileUnpivot(i)),this.top?i.limit=this.top.value:this.limit&&(i.limit=this.limit.value,this.offset&&(i.offset=this.offset.value)),i.percent=this.percent,i.corresponding=this.corresponding,this.union?(i.unionfn=this.union.compile(n),i.orderfn=this.union.order?this.union.compileOrder(i,t):null):this.unionall?(i.unionallfn=this.unionall.compile(n),i.orderfn=this.unionall.order?this.unionall.compileOrder(i,t):null):this.except?(i.exceptfn=this.except.compile(n),i.orderfn=this.except.order?this.except.compileOrder(i,t):null):this.intersect&&(i.intersectfn=this.intersect.compile(n),i.orderfn=this.intersect.order?this.intersect.compileOrder(i,t):null),this.into){if(this.into instanceof T.Table)l.options.autocommit&&l.databases[this.into.databaseid||n].engineid?i.intoallfns=`return alasql
								.engines[${JSON.stringify(l.databases[this.into.databaseid||n].engineid)}]
								.intoTable(
									${JSON.stringify(this.into.databaseid||n)},
									${JSON.stringify(this.into.tableid)},
									this.data,
									columns,
									cb
								);`:i.intofns=`alasql
							.databases[${JSON.stringify(this.into.databaseid||n)}]
							.tables[${JSON.stringify(this.into.tableid)}]
							.data.push(r);
						`;else if(this.into instanceof T.VarValue)i.intoallfns=`
					alasql.vars[${JSON.stringify(this.into.variable)}]=this.data;
					res=this.data.length;
					if(cb) res = cb(res);
					return res;
				`;else if(this.into instanceof T.FuncValue){var s="return alasql.into["+JSON.stringify(this.into.funcid.toUpperCase())+"](";this.into.args&&this.into.args.length>0?(s+=this.into.args[0].toJS()+",",this.into.args.length>1?s+=this.into.args[1].toJS()+",":s+="undefined,"):s+="undefined, undefined,",i.intoallfns=s+"this.data,columns,cb)"}else this.into instanceof T.ParamValue&&(i.intofns=`params[${JSON.stringify(this.into.param)}].push(r)`);i.intofns?i.intofn=new Function("r,i,params,alasql","var y;"+i.intofns):i.intoallfns&&(i.intoallfn=new Function("columns,cb,params,alasql","var y;"+i.intoallfns))}var u=function(f,h,g){i.params=f;var m=vi(i,g,function(b,c){if(c){if(h)return h(null,c);throw c}if(i.rownums.length>0)for(var d=0,v=b.length;d<v;d++)for(var E=0,O=i.rownums.length;E<O;E++)b[d][i.rownums[E]]=d+1;var R=mr(i,b);return h&&h(R),R});return m};return u.query=i,u}execute(n,t,r){return this.compile(n)(t,r)}compileWhereExists(n){this.exists&&(n.existsfn=this.exists.map(function(t){var r=t.compile(n.database.databaseid);return r.query.modifier="RECORDSET",r}))}compileQueries(n){this.queries&&(n.queriesfn=this.queries.map(function(t){var r=t.compile(n.database.databaseid);return r.query.modifier="RECORDSET",r}))}};function mr(e,n){if(typeof n>"u"||typeof n=="number"||typeof n=="string"||typeof n=="boolean")return n;var t=e.modifier||l.options.modifier,r=e.columns;if(typeof r>"u"||r.length==0)if(n.length>0){for(var i={},s=Math.min(n.length,l.options.columnlookup||10)-1;0<=s;s--)for(var u in n[s])i[u]=!0;r=Object.keys(i).map(function(h){return{columnid:h}})}else r=[];switch(t){case"VALUE":if(n.length===0)return;const h=r&&r.length>0?r[0].columnid:Object.keys(n[0])[0];return n[0][h];case"ROW":return n.length===0?void 0:Object.values(n[0]);case"COLUMN":if(n.length===0)return[];let g;r&&r.length>0?g=r[0].columnid:g=Object.keys(n[0])[0];let m=[];for(var s=0,f=n.length;s<f;s++)m.push(n[s][g]);return m;case"MATRIX":return n.length===0?void 0:n.map(v=>r.map(E=>v[E.columnid]));case"INDEX":if(n.length===0)return;const b=r&&r.length>0?r[0].columnid:Object.keys(n[0])[0],c=r&&r.length>1?r[1].columnid:Object.keys(n[0])[1];return n.reduce((v,E)=>({...v,[E[b]]:E[c]}),{});case"RECORDSET":return new l.Recordset({columns:r,data:n});case"TEXTSTRING":if(n.length===0)return;const d=r&&r.length>0?r[0].columnid:Object.keys(n[0])[0];return n.map(v=>v[d]).join(`
`)}return n}T.ExistsValue=class{constructor(n){Object.assign(this,n)}toString(){return"EXISTS("+this.value.toString()+")"}toType(){return"boolean"}toJS(n,t,r){return`!!this.existsfn[${this.existsidx}](params, null, ${n}).data.length`}},l.precompile=function(e,n,t){e&&(e.params=t,e.queries&&(e.queriesfn=e.queries.map(function(r){var i=r.compile(n||e.database.databaseid);return i.query.modifier="RECORDSET",i})),e.exists&&(e.existsfn=e.exists.map(function(r){var i=r.compile(n||e.database.databaseid);return i.query.modifier="RECORDSET",i})))},T.Select.prototype.compileFrom=function(e){const n=this;e.sources=[],e.aliases={},n.from&&(n.from.forEach(t=>{const r=t.as||t.tableid;if(t instanceof T.Table)e.aliases[r]={tableid:t.tableid,databaseid:t.databaseid||e.database.databaseid,type:"table"};else if(t instanceof T.Select)e.aliases[r]={type:"subquery"};else if(t instanceof T.Search)e.aliases[r]={type:"subsearch"};else if(t instanceof T.ParamValue)e.aliases[r]={type:"paramvalue"};else if(t instanceof T.FuncValue)e.aliases[r]={type:"funcvalue"};else if(t instanceof T.VarValue)e.aliases[r]={type:"varvalue"};else if(t instanceof T.FromData)e.aliases[r]={type:"fromdata"};else if(t instanceof T.Json)e.aliases[r]={type:"json"};else if(t.inserted)e.aliases[r]={type:"inserted"};else throw new Error("Wrong table at FROM");const i={alias:r,databaseid:t.databaseid||e.database.databaseid,tableid:t.tableid,joinmode:"INNER",onmiddlefn:ee,srcwherefns:"",srcwherefn:ee};if(t instanceof T.Table)i.columns=l.databases[i.databaseid].tables[i.tableid].columns,l.options.autocommit&&l.databases[i.databaseid].engineid&&!l.databases[i.databaseid].tables[i.tableid].view?i.datafn=(s,u,f,h,g)=>g.engines[g.databases[i.databaseid].engineid].fromTable(i.databaseid,i.tableid,f,h,s):l.databases[i.databaseid].tables[i.tableid].view?i.datafn=(s,u,f,h,g)=>{let m=g.databases[i.databaseid].tables[i.tableid].select(u);return f&&(m=f(m,h,s)),m}:i.datafn=(s,u,f,h,g)=>{let m=g.databases[i.databaseid].tables[i.tableid].data;return f&&(m=f(m,h,s)),m};else if(t instanceof T.Select)i.subquery=t.compile(e.database.databaseid),typeof i.subquery.query.modifier>"u"&&(i.subquery.query.modifier="RECORDSET"),i.columns=i.subquery.query.columns,i.datafn=(s,u,f,h,g)=>{let m;return i.subquery(s.params,b=>{m=b.data,f&&(m=f(m,h,s))}),m};else if(t instanceof T.Search)i.subsearch=t,i.columns=[],i.datafn=(s,u,f,h,g)=>{let m;return i.subsearch.execute(s.database.databaseid,s.params,b=>{m=b,f&&(m=f(m,h,s))}),m};else if(t instanceof T.ParamValue){let s=`var res = alasql.prepareFromData(params['${t.param}']`;t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",i.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t.inserted){let s="var res = alasql.prepareFromData(alasql.inserted";t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",i.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof T.Json){let s="var res = alasql.prepareFromData("+t.toJS();t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",i.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof T.VarValue){let s=`var res = alasql.prepareFromData(alasql.vars['${t.variable}']`;t.array&&(s+=",true"),s+=");if(cb)res=cb(res,idx,query);return res",i.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof T.FuncValue){let s="var res=alasql.from["+JSON.stringify(t.funcid.toUpperCase())+"](";t.args&&t.args.length>0?(t.args[0]?s+=t.args[0].toJS("query.oldscope")+",":s+="null,",t.args[1]?s+=t.args[1].toJS("query.oldscope")+",":s+="null,"):s+="null,null,",s+="cb,idx,query); return res",i.datafn=new Function("query,params,cb,idx,alasql",s)}else if(t instanceof T.FromData)i.datafn=(s,u,f,h,g)=>{let m=t.data;return f&&(m=f(m,h,s)),m};else throw new Error("Wrong table at FROM");e.sources.push(i)}),e.defaultTableid=e.sources[0].alias)},l.prepareFromData=function(e,n){let t=e;if(typeof e=="string")t=e.split(/\r?\n/),n&&(t=t.map(r=>[r]));else if(n)t=e.map(r=>[r]);else if(typeof e=="object"&&!Array.isArray(e))if(typeof Mongo<"u"&&typeof Mongo.Collection<"u"&&e instanceof Mongo.Collection)t=e.find().fetch();else{t=[];for(const r in e)e.hasOwnProperty(r)&&t.push([r,e[r]])}return t},T.Select.prototype.compileJoins=function(e){const n=this;this.joins.forEach(t=>{let r,i,s;if(t.joinmode==="CROSS"){if(t.using||t.on)throw new Error("CROSS JOIN cannot have USING or ON clauses");t.joinmode="INNER"}if(t instanceof T.Apply){s={alias:t.as,applymode:t.applymode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee,columns:[]},s.applyselect=t.select.compile(e.database.databaseid),s.columns=s.applyselect.query.columns,s.datafn=function(f,h,g,m,b){let c;return g&&(c=g(c,m,f)),c},e.sources.push(s);return}if(t.table){if(r=t.table,s={alias:t.as||r.tableid,databaseid:r.databaseid||e.database.databaseid,tableid:r.tableid,joinmode:t.joinmode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee,columns:[]},!l.databases[s.databaseid].tables[s.tableid])throw new Error("Table '"+s.tableid+"' is not exists in database '"+s.databaseid+"'");s.columns=l.databases[s.databaseid].tables[s.tableid].columns,l.options.autocommit&&l.databases[s.databaseid].engineid?s.datafn=function(f,h,g,m,b){return b.engines[b.databases[s.databaseid].engineid].fromTable(s.databaseid,s.tableid,g,m,f)}:l.databases[s.databaseid].tables[s.tableid].view?s.datafn=function(f,h,g,m,b){let c=b.databases[s.databaseid].tables[s.tableid].select(h);return g&&(c=g(c,m,f)),c}:s.datafn=function(f,h,g,m,b){let c=b.databases[s.databaseid].tables[s.tableid].data;return g&&(c=g(c,m,f)),c},e.aliases[s.alias]={tableid:r.tableid,databaseid:r.databaseid||e.database.databaseid}}else if(t.select)r=t.select,s={alias:t.as,joinmode:t.joinmode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee,columns:[]},s.subquery=r.compile(e.database.databaseid),typeof s.subquery.query.modifier>"u"&&(s.subquery.query.modifier="RECORDSET"),s.columns=s.subquery.query.columns,s.datafn=function(f,h,g,m,b){s.data=s.subquery(f.params,null,g,m).data;let c=s.data;return g&&(c=g(c,m,f)),c},e.aliases[s.alias]={type:"subquery"};else if(t.param)s={alias:t.as,joinmode:t.joinmode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee},i="let res=alasql.prepareFromData(params['"+t.param.param+"']",t.array&&(i+=",true"),i+="); if(cb) res=cb(res, idx, query); return res",s.datafn=new Function("query,params,cb,idx, alasql",i),e.aliases[s.alias]={type:"paramvalue"};else if(t.variable)s={alias:t.as,joinmode:t.joinmode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee},i="let res=alasql.prepareFromData(alasql.vars['"+t.variable+"']",t.array&&(i+=", true"),i+="); if(cb)res=cb(res, idx, query);return res",s.datafn=new Function("query,params,cb,idx, alasql",i),e.aliases[s.alias]={type:"varvalue"};else if(t.func){s={alias:t.as,joinmode:t.joinmode,onmiddlefn:ee,srcwherefns:"",srcwherefn:ee};let f="let res=alasql.from["+JSON.stringify(t.func.funcid.toUpperCase())+"](";const h=t.func.args;h&&h.length>0?(h[0]?f+=h[0].toJS("query.oldscope")+", ":f+="null, ",h[1]?f+=h[1].toJS("query.oldscope")+", ":f+="null, "):f+="null, null, ",f+="cb, idx, query); return res",s.datafn=new Function("query, params, cb, idx, alasql",f),e.aliases[s.alias]={type:"funcvalue"}}const u=s.alias;if(t.natural){if(t.using||t.on)throw new Error("NATURAL JOIN cannot have USING or ON clauses");if(e.sources.length>0){const f=e.sources[e.sources.length-1],h=l.databases[f.databaseid].tables[f.tableid],g=l.databases[s.databaseid].tables[s.tableid];if(h&&g){const m=h.columns.map(c=>c.columnid),b=g.columns.map(c=>c.columnid);t.using=ui(m,b).map(c=>({columnid:c}))}else throw new Error("In this version of Alasql NATURAL JOIN works for tables with predefined columns only")}}if(t.using){const f=e.sources[e.sources.length-1];s.onleftfns=t.using.map(h=>"p['"+(f.alias||f.tableid)+"']['"+h.columnid+"']").join('+"`"+'),s.onleftfn=new Function("p,params,alasql","let y;return "+s.onleftfns),s.onrightfns=t.using.map(h=>"p['"+(s.alias||s.tableid)+"']['"+h.columnid+"']").join('+"`"+'),s.onrightfn=new Function("p,params,alasql","let y;return "+s.onrightfns),s.optimization="ix"}else if(t.on)if(t.on instanceof T.Op&&t.on.op==="="&&!t.on.allsome){s.optimization="ix";let f="",h="",g="",m=!1;const b=t.on.left.toJS("p",e.defaultTableid,e.defcols),c=t.on.right.toJS("p",e.defaultTableid,e.defcols);b.indexOf("p['"+u+"']")>-1&&!(c.indexOf("p['"+u+"']")>-1)?(b.match(/p\['.*?'\]/g)||[]).every(d=>d==="p['"+u+"']")?h=b:m=!0:!(b.indexOf("p['"+u+"']")>-1)&&c.indexOf("p['"+u+"']")>-1&&(c.match(/p\['.*?'\]/g)||[]).every(d=>d==="p['"+u+"']")?f=b:m=!0,c.indexOf("p['"+u+"']")>-1&&!(b.indexOf("p['"+u+"']")>-1)?(c.match(/p\['.*?'\]/g)||[]).every(d=>d==="p['"+u+"']")?h=c:m=!0:!(c.indexOf("p['"+u+"']")>-1)&&b.indexOf("p['"+u+"']")>-1&&(b.match(/p\['.*?'\]/g)||[]).every(d=>d==="p['"+u+"']")?f=c:m=!0,m&&(h="",f="",g=t.on.toJS("p",e.defaultTableid,e.defcols),s.optimization="no"),s.onleftfns=f,s.onrightfns=h,s.onmiddlefns=g||"true",s.onleftfn=new Function("p,params,alasql","let y;return "+s.onleftfns),s.onrightfn=new Function("p,params,alasql","let y;return "+s.onrightfns),s.onmiddlefn=new Function("p,params,alasql","let y;return "+s.onmiddlefns)}else s.optimization="no",s.onmiddlefns=t.on.toJS("p",e.defaultTableid,e.defcols),s.onmiddlefn=new Function("p,params,alasql","let y;return "+t.on.toJS("p",e.defaultTableid,e.defcols));e.sources.push(s)})},T.Select.prototype.compileWhere=function(e){if(this.where){if(typeof this.where=="function")return this.where;var n=this.where.toJS("p",e.defaultTableid,e.defcols);return e.wherefns=n,new Function("p,params,alasql","var y;return "+n)}else return function(){return!0}},T.Select.prototype.compileWhereJoins=function(e){};function A4(e,n){if(!n)return!1;if(n instanceof T.Op&&!(n.op!="="&&n.op!="AND")&&!n.allsome){var t=n.toJS("p",e.defaultTableid,e.defcols),r=[];if(e.sources.forEach(function(f,h){f.tableid&&t.indexOf("p['"+f.alias+"']")>-1&&r.push(f)}),r.length!=0)if(r.length==1){if(!(t.match(/p\[\'.*?\'\]/g)||[]).every(function(f){return f=="p['"+r[0].alias+"']"}))return;var i=r[0];if(i.srcwherefns=i.srcwherefns?i.srcwherefns+"&&"+t:t,n instanceof T.Op&&n.op=="="&&!n.allsome){if(n.left instanceof T.Column){var s=n.left.toJS("p",e.defaultTableid,e.defcols),u=n.right.toJS("p",e.defaultTableid,e.defcols);u.indexOf("p['"+r[0].alias+"']")==-1&&(r[0].wxleftfns=s,r[0].wxrightfns=u)}if(n.right instanceof T.Column){var s=n.left.toJS("p",e.defaultTableid,e.defcols),u=n.right.toJS("p",e.defaultTableid,e.defcols);s.indexOf("p['"+r[0].alias+"']")==-1&&(r[0].wxleftfns=u,r[0].wxrightfns=s)}}n.reduced=!0;return}else(n.op="AND")&&(A4(e,n.left),A4(e,n.right))}}T.Select.prototype.compileGroup=function(e){if(e.sources.length>0)var n=e.sources[0].alias;else var n="";var t=e.defcols,r=[[]];this.group&&(r=O4(this.group,e));var i=[];r.forEach(function(u){i=oi(i,u)}),e.allgroups=i,e.ingroup=[];var s="";return r.forEach(function(u){s+="var g=this.xgroups[";var f=u.map(function(b){var c=b.split("	")[0],d=b.split("	")[1];return c===""?"1":(e.ingroup.push(c),d)});f.length===0&&(f=["''"]),s+=f.join('+"`"+'),s+="];if(!g) {this.groups.push((g=this.xgroups[",s+=f.join('+"`"+'),s+="] = {",s+=u.map(function(b){var c=b.split("	")[0],d=b.split("	")[1];return c===""?"":"'"+c+"':"+d+","}).join("");var h=li(i,u);s+=h.map(function(b){var c=b.split("	")[0];return"'"+c+"':null,"}).join("");var g="",m="";typeof e.groupStar<"u"&&(m+="for(var f in p['"+e.groupStar+"']) {g[f]=p['"+e.groupStar+"'][f];};"),s+=e.selectGroup.map(function(b){var c=b.expression.toJS("p",n,t),d=b.nick;let v=E=>E.args[0].toJS("p",n,t);if(b instanceof T.AggrValue){if(b.distinct&&(g+=",g['$$_VALUES_"+d+"']={},g['$$_VALUES_"+d+"']["+c+"]=true"),b.aggregatorid==="SUM"){if("funcid"in b.expression){let E=v(b.expression);return`'${d}':(${E})|| typeof ${E} == 'number' ? ${c} : null,`}return`'${d}':(${c})|| typeof ${c} == 'number' ? ${c} : null,`}else if(b.aggregatorid==="TOTAL"){if("funcid"in b.expression){let E=v(b.expression);return`'${d}':(${E}) || typeof ${E} == 'number' ?
							${E} : ${E} == 'string' && typeof Number(${E}) == 'number' ? Number(${E}) :
							typeof ${E} == 'boolean' ?  Number(${E}) : 0,`}return`'${d}':(${c})|| typeof ${c} == 'number' ?
							${c} : ${c} == 'string' && typeof Number(${c}) == 'number' ? Number(${c}) :
							typeof ${c} === 'boolean' ?  Number(${c}) : 0,`}else{if(b.aggregatorid==="FIRST"||b.aggregatorid==="LAST")return"'"+d+"':"+c+",";if(b.aggregatorid==="MIN"){if("funcid"in b.expression){let E=v(b.expression);return`'${d}': (typeof ${E} == 'number' || typeof ${E} == 'bigint' ? ${c} : typeof ${E} == 'object' ?
							typeof Number(${E}) == 'number' && ${E}!== null? ${c} : null : null),`}return`'${d}': (typeof ${c} == 'number' || typeof ${c} == 'bigint' ? ${c} : typeof ${c} == 'object' ?
							typeof Number(${c}) == 'number' && ${c}!== null? ${c} : null : null),`}else if(b.aggregatorid==="MAX"){if("funcid"in b.expression){let E=v(b.expression);return`'${d}' : (typeof ${E} == 'number' || typeof ${E} == 'bigint' ? ${c} : typeof ${E} == 'object' ?
							typeof Number(${E}) == 'number' ? ${c} : null : null),`}return`'${d}' : (typeof ${c} == 'number' || typeof ${c} == 'bigint' ? ${c} : typeof ${c} == 'object' ?
							typeof Number(${c}) == 'number' ? ${c} : null : null),`}else{if(b.aggregatorid==="ARRAY")return`'${d}':[${c}],`;if(b.aggregatorid==="COUNT")return b.expression.columnid==="*"?`'${d}':1,`:`'${d}':(typeof ${c} == "undefined" || ${c} === null) ? 0 : 1,`;if(b.aggregatorid==="AVG")return e.removeKeys.push(`_SUM_${d}`),e.removeKeys.push(`_COUNT_${d}`),`'${d}':${c},'_SUM_${d}':(${c})||0,'_COUNT_${d}':(typeof ${c} == "undefined" || ${c} === null) ? 0 : 1,`;if(b.aggregatorid==="AGGR")return g+=`,g['${d}']=${b.expression.toJS("g",-1)}`,"";if(b.aggregatorid==="REDUCE")return e.aggrKeys.push(b),`'${d}':alasql.aggr['${b.funcid}'](${c},undefined,1),`}}return""}return""}).join(""),s+="}"+g+",g));"+m+"} else {",s+=e.selectGroup.map(function(b){var c=b.nick,d=b.expression.toJS("p",n,t);let v=R=>R.args[0].toJS("p",n,t);if(b instanceof T.AggrValue){var E="",O="";if(b.distinct&&(E=`if(typeof ${d}!="undefined" && (!g['$$_VALUES_${c}'][${d}])) {`,O=`g['$$_VALUES_${c}'][${d}]=true;}`),b.aggregatorid==="SUM"){if("funcid"in b.expression){let R=v(b.expression);return E+`
								{
									const __g_colas = g['${c}'];
									const __typeof_colexp1 = typeof ${R};
									const __colexp1 = ${R};

									if (__g_colas == null && ${R} == null) {
										g['${c}'] = null;
									} else if (typeof __g_colas === 'bigint' || typeof __colexp1 === 'bigint') {
            					    	g['${c}'] = BigInt(__g_colas) + BigInt(__colexp);
            						} else if ((typeof __g_colas !== 'object' && typeof __g_colas !== 'number' && __typeof_colexp1 !== 'object' && __typeof_colexp1 !== 'number') ||
											   (__g_colas == null || (typeof __g_colas !== 'number' && typeof __g_colas !== 'object')) && (${R} == null || (__typeof_colexp1 !== 'number' && __typeof_colexp1 !== 'object'))) {
										g['${c}'] = null;
									} else if ((typeof __g_colas !== 'object' && typeof __g_colas !== 'number' && __typeof_colexp1 == 'number') ||
											   (__g_colas == null && __typeof_colexp1 == 'number')) {
										g['${c}'] = ${d};
									} else if (typeof __g_colas == 'number' && ${R} == null) {
										g['${c}'] = __g_colas;
									} else {
										g['${c}'] += ${d} || 0;
									}
								}
								`+O}return E+`
							{
								const __g_colas = g['${c}'];
								const __typeof_colexp = typeof ${d};
								const __colexp = ${d};

								if (__g_colas == null && ${d} == null) {
									g['${c}'] = null;
								} else if (typeof __g_colas === 'bigint' || typeof __colexp === 'bigint') {
            					    g['${c}'] = BigInt(__g_colas) + BigInt(__colexp);
            					} else if ((typeof __g_colas !== 'object' && typeof __g_colas !== 'number' && __typeof_colexp !== 'object' && __typeof_colexp !== 'number') ||
										   (__g_colas == null || (typeof __g_colas !== 'number' && typeof __g_colas !== 'object')) && (${d} == null || (__typeof_colexp !== 'number' && __typeof_colexp !== 'object'))) {
									g['${c}'] = null;
								} else if (typeof __g_colas !== 'object' && typeof __g_colas !== 'number' && __typeof_colexp == 'number') {
									g['${c}'] = ${d};
								} else if (typeof __g_colas == 'number' && ${d} == null) {
									g['${c}'] = __g_colas;
								} else if (__g_colas == null && __typeof_colexp == 'number') {
									g['${c}'] = ${d};
								} else {
									g['${c}'] += ${d} || 0;
								}
							}
							`+O}else if(b.aggregatorid==="TOTAL"){if("funcid"in b.expression){let R=v(b.expression);return E+`{
									const __g_colas = g['${c}'];
									const __colexp1 = ${R};
									const __typeof_g_colas = typeof __g_colas;
									const __typeof_colexp1 = typeof __colexp1;

									if (__typeof_g_colas == 'string' && !isNaN(__g_colas) && typeof Number(__g_colas) == 'number' &&
										__typeof_colexp1 == 'string' && !isNaN(__colexp1) && typeof Number(__colexp1) == 'number') {
										g['${c}'] = Number(__g_colas) + Number(__colexp1);
									} else if (__typeof_g_colas === 'bigint' || __typeof_colexp1 === 'bigint') {
       							    	g['${c}'] = BigInt(__g_colas || 0) + BigInt(__colexp1 || 0);
       								} else if (__typeof_g_colas == 'string' && __typeof_colexp1 == 'string') {
										g['${c}'] = 0;
									} else if (__typeof_g_colas == 'string' && __typeof_colexp1 == 'number') {
										g['${c}'] = __colexp1;
									} else if (__typeof_colexp1 == 'string' && __typeof_g_colas == 'number') {
										g['${c}'] = __g_colas;
									} else {
										g['${c}'] += __colexp1 || 0;
									}
								}`+O}return E+`{
								const __g_colas = g['${c}'];
								const __colexp = ${d};
								const __typeof_g_colas = typeof __g_colas;
								const __typeof_colexp = typeof __colexp;

								if (__typeof_g_colas === 'string' && !isNaN(__g_colas) && typeof Number(__g_colas) === 'number' &&
									__typeof_colexp === 'string' && !isNaN(__colexp) && typeof Number(__colexp) === 'number') {
									g['${c}'] = Number(__g_colas) + Number(__colexp);
								} else if (__typeof_g_colas === 'bigint' || __typeof_colexp === 'bigint') {
       							    g['${c}'] = BigInt(__g_colas || 0) + BigInt(__colexp || 0);
       							} else if (__typeof_g_colas === 'string' && __typeof_colexp === 'string') {
									g['${c}'] = 0;
								} else if (__typeof_g_colas === 'string' && __typeof_colexp === 'number') {
									g['${c}'] = __colexp;
								} else if (__typeof_colexp === 'string' && __typeof_g_colas === 'number') {
									g['${c}'] = __g_colas;
								} else {
									g['${c}'] += __colexp || 0;
								}
							}

							`+O}else{if(b.aggregatorid==="COUNT")return b.expression.columnid==="*"?`${E}
								g['${c}']++;
								${O}`:`${E}
							if(typeof ${d}!="undefined" && ${d} !== null) g['${c}']++;
							${O}`;if(b.aggregatorid==="ARRAY")return E+"g['"+c+"'].push("+d+");"+O;if(b.aggregatorid==="MIN"){if("funcid"in b.expression){let R=v(b.expression);return E+`if ((g['${c}'] == null && ${R} !== null) ? y = ${d} : 
									(g['${c}'] !== null && ${R} == null) ? y = g['${c}'] : 
									((y = ${d}) < g['${c}'])) {
									if (typeof y == 'number' || typeof y == 'bigint') {
									  g['${c}'] = y;
									} else if (typeof y == 'object' && y instanceof Date) {
									  g['${c}'] = y;
									} else if (typeof y == 'object' && typeof Number(y) == 'number') {
									  g['${c}'] = Number(y);
									}
								} else if (g['${c}'] !== null && typeof g['${c}'] == 'object' && y instanceof Date) {
									g['${c}'] = g['${c}'];
								} else if (g['${c}'] !== null && typeof g['${c}'] == 'object') {
									g['${c}'] = Number(g['${c}']);
								}`+O}return E+`if((g['${c}'] == null && ${d}!== null) ? y = ${d} : 
								(g['${c}']!== null && ${d} == null) ? y = g['${c}'] : 
								((y=${d}) < g['${c}'])) { 
								if(typeof y == 'number' || typeof y == 'bigint') {
									g['${c}'] = y;
								} else if(typeof y == 'object' && y instanceof Date) {
									g['${c}'] = y;
								} else if(typeof y == 'object' && typeof Number(y) == 'number') {
									g['${c}'] = Number(y);
								}
							} else if(g['${c}']!== null && typeof g['${c}'] == 'object' && y instanceof Date) {
								g['${c}'] = g['${c}'];
							} else if(g['${c}']!== null && typeof g['${c}'] == 'object') {
								g['${c}'] = Number(g['${c}']);
							}`+O}else if(b.aggregatorid==="MAX"){if("funcid"in b.expression){let R=v(b.expression);return E+`if ((g['${c}'] == null && ${R} !== null) ? y = ${d} : 
									(g['${c}'] !== null && ${R} == null) ? y = g['${c}'] : 
									((y = ${d}) > g['${c}'])) {
									if (typeof y == 'number' || typeof y == 'bigint') {
									  g['${c}'] = y;
									} else if (typeof y == 'object' && y instanceof Date) {
									  g['${c}'] = y;
									} else if (typeof y == 'object' && typeof Number(y) == 'number') {
									  g['${c}'] = Number(y);
									}
								} else if (g['${c}'] !== null && typeof g['${c}'] == 'object' && y instanceof Date) {
									g['${c}'] = g['${c}'];
								} else if (g['${c}'] !== null && typeof g['${c}'] == 'object') {
									g['${c}'] = Number(g['${c}']);
								}`+O}return E+`if((g['${c}'] == null && ${d}!== null) ? y = ${d} : 
								(g['${c}']!== null && ${d} == null) ? y = g['${c}'] : 
								((y=${d}) > g['${c}'])) { 
								if(typeof y == 'number' || typeof y == 'bigint') {
									g['${c}'] = y;
								} else if(typeof y == 'object' && y instanceof Date) {
									g['${c}'] = y;
								} else if(typeof y == 'object' && typeof Number(y) == 'number') {
									g['${c}'] = Number(y);
								}
							} else if(g['${c}']!== null && typeof g['${c}'] == 'object' && y instanceof Date) {
								g['${c}'] = g['${c}'];
							} else if(g['${c}']!== null && typeof g['${c}'] == 'object') {
								g['${c}'] = Number(g['${c}']);
							}`+O}else{if(b.aggregatorid==="FIRST")return"";if(b.aggregatorid==="LAST")return`${E}g['${c}']=${d};${O}`;if(b.aggregatorid==="AVG")return`${E}
							y= (${d});
							g['_COUNT_${c}'] += (typeof y == "undefined" || y === null) ? 0 : 1;
							if (typeof g['_SUM_${c}'] === 'bigint' || typeof y === 'bigint') {
								g['_SUM_${c}'] = BigInt(g['_SUM_${c}']);
								g['_SUM_${c}'] += BigInt(y || 0);
    							g['${c}'] = BigInt(g['_SUM_${c}']) / BigInt(g['_COUNT_${c}']); 
    						} else {
								g['_SUM_${c}'] += (y || 0);
    							g['${c}'] = g['_SUM_${c}'] / g['_COUNT_${c}']; 
    						}
							${O}`;if(b.aggregatorid==="AGGR")return`${E}
							g['${c}']=${b.expression.toJS("g",-1)};
							${O}`;if(b.aggregatorid==="REDUCE")return`${E}
							g['${c}'] = alasql.aggr.${b.funcid}(${d},g['${c}'],2);
							${O}`}}return""}return""}).join(""),s+="}"}),new Function("p,params,alasql","var y;"+s)};function Sr(e,n,t){var r="",i=[],s={};return n.forEach(function(u){e.ixsources={},e.sources.forEach(function(h){e.ixsources[h.alias]=h});var f;if(e.ixsources[u])var f=e.ixsources[u].columns;t&&l.options.joinstar=="json"&&(r+="r['"+u+"']={};"),f&&f.length>0?f.forEach(function(h){const g=C2(h.columnid);if(t&&l.options.joinstar=="underscore")i.push("'"+u+"_"+g+"':p['"+u+"']['"+g+"']");else if(t&&l.options.joinstar=="json")r+="r['"+u+"']['"+g+"']=p['"+u+"']['"+g+"'];";else{var m="p['"+u+"']['"+g+"']";if(s[h.columnid]){var c=m+" !== undefined ? "+m+" : "+s[h.columnid].value;i[s[h.columnid].id]=s[h.columnid].key+c,s[h.columnid].value=c}else{var b="'"+g+"':";i.push(b+m),s[h.columnid]={id:i.length-1,value:m,key:b}}}e.selectColumns[g]=!0;var d={columnid:h.columnid,dbtypeid:h.dbtypeid,dbsize:h.dbsize,dbprecision:h.dbprecision,dbenum:h.dbenum};e.columns.push(d),e.xcolumns[d.columnid]=d}):(r+='var w=p["'+u+'"];for(var k in w){r[k]=w[k]};',e.dirtyColumns=!0)}),{s:i.join(","),sp:r}}T.Select.prototype.compileSelect1=function(e,n){var t=this;e.columns=[],e.xcolumns={},e.selectColumns={},e.dirtyColumns=!1;var r="var r={",i="",s=[];return this.columns.forEach(function(u){if(u instanceof T.Column)if(u.columnid==="*")if(u.func)i+="r=params['"+u.param+"'](p['"+e.sources[0].alias+"'],p,params,alasql);";else if(u.tableid){var f=Sr(e,[u.tableid],!1);f.s&&(s=s.concat(f.s)),i+=f.sp}else{var f=Sr(e,Object.keys(e.aliases),!0);f.s&&(s=s.concat(f.s)),i+=f.sp}else{var h=u.tableid,g=u.databaseid||e.sources[0].databaseid||e.database.databaseid;if(h||(h=e.defcols[u.columnid]),h||(h=e.defaultTableid),u.columnid!=="_"){var m=n&&n.length>1&&Array.isArray(n[0])&&n[0].length>=1&&n[0][0].hasOwnProperty("sheetid");m?i='var r={};var w=p["'+h+'"];var cols=['+t.columns.map(function(E){return"'"+E.columnid+"'"}).join(",")+"];var colas=["+t.columns.map(function(E){return"'"+(E.as||E.columnid)+"'"}).join(",")+"];for (var i=0;i<Object.keys(p['"+h+"']).length;i++) for(var k=0;k<cols.length;k++){if (!r.hasOwnProperty(i)) r[i]={}; r[i][colas[k]]=w[i][cols[k]];}":s.push("'"+C2(u.as||u.columnid)+"':p['"+h+"']['"+u.columnid+"']")}else s.push("'"+C2(u.as||u.columnid)+"':p['"+h+"']");if(e.selectColumns[C2(u.as||u.columnid)]=!0,e.aliases[h]&&e.aliases[h].type==="table"){if(!l.databases[g].tables[e.aliases[h].tableid])throw new Error("Table '"+h+"' does not exist in database");var b=l.databases[g].tables[e.aliases[h].tableid].columns,c=l.databases[g].tables[e.aliases[h].tableid].xcolumns;if(c&&b.length>0){var d=c[u.columnid];if(d===void 0)throw new Error("Column does not exist: "+u.columnid);var v={columnid:u.as||u.columnid,dbtypeid:d.dbtypeid,dbsize:d.dbsize,dbpecision:d.dbprecision,dbenum:d.dbenum};e.columns.push(v),e.xcolumns[v.columnid]=v}else{var v={columnid:u.as||u.columnid};e.columns.push(v),e.xcolumns[v.columnid]=v,e.dirtyColumns=!0}}else{var v={columnid:u.as||u.columnid};e.columns.push(v),e.xcolumns[v.columnid]=v}}else if(u instanceof T.AggrValue){t.group||(t.group=[""]),u.as||(u.as=C2(u.toString())),u.aggregatorid==="SUM"||u.aggregatorid==="MAX"||u.aggregatorid==="MIN"||u.aggregatorid==="FIRST"||u.aggregatorid==="LAST"||u.aggregatorid==="AVG"||u.aggregatorid==="ARRAY"||u.aggregatorid==="REDUCE"||u.aggregatorid==="TOTAL"?s.push("'"+C2(u.as)+"':"+m4(u.expression.toJS("p",e.defaultTableid,e.defcols))):u.aggregatorid==="COUNT"&&s.push("'"+C2(u.as)+"':1");var v={columnid:u.as||u.columnid||u.toString()};e.columns.push(v),e.xcolumns[v.columnid]=v}else{s.push("'"+C2(u.as||u.columnid||u.toString())+"':"+m4(u.toJS("p",e.defaultTableid,e.defcols))),e.selectColumns[C2(u.as||u.columnid||u.toString())]=!0;var v={columnid:u.as||u.columnid||u.toString()};e.columns.push(v),e.xcolumns[v.columnid]=v}}),r+=s.join(",")+"};"+i,r},T.Select.prototype.compileSelect2=function(e,n){var t=e.selectfns;return this.orderColumns&&this.orderColumns.length>0&&this.orderColumns.forEach(function(r,i){var s="$$$"+i;r instanceof T.Column&&e.xcolumns[r.columnid]?t+="r['"+s+"']=r['"+r.columnid+"'];":r instanceof T.ParamValue&&e.xcolumns[n[r.param]]?t+="r['"+s+"']=r['"+n[r.param]+"'];":t+="r['"+s+"']="+r.toJS("p",e.defaultTableid,e.defcols)+";",e.removeKeys.push(s)}),new Function("p,params,alasql","var y;"+t+"return r")},T.Select.prototype.compileSelectGroup0=function(e){var n=this;n.columns.forEach(function(t,r){if(t instanceof T.Column&&t.columnid==="*")e.groupStar=t.tableid||"default";else{var i;t instanceof T.Column?i=C2(t.columnid):i=C2(t.toString(!0));for(var s=0;s<r;s++)if(i===n.columns[s].nick){i=n.columns[s].nick+":"+r;break}if(t.nick=i,n.group){var u=n.group.findIndex(function(f){return f.columnid===t.columnid&&f.tableid===t.tableid});u>-1&&(n.group[u].nick=i)}t.funcid&&(t.funcid.toUpperCase()==="ROWNUM"||t.funcid.toUpperCase()==="ROW_NUMBER")&&e.rownums.push(t.as)}}),this.columns.forEach(function(t){t.findAggregator&&t.findAggregator(e)}),this.having&&this.having.findAggregator&&this.having.findAggregator(e)},T.Select.prototype.compileSelectGroup1=function(e){var n=this,t="var r = {};";return n.columns.forEach(function(r){if(r instanceof T.Column&&r.columnid==="*")return t+="for(var k in g) {r[k]=g[k]};","";var i=r.as;i===void 0&&(r instanceof T.Column?i=C2(r.columnid):i=r.nick),e.groupColumns[i]=r.nick,t+="r['"+i+"']=",t+=m4(r.toJS("g",""))+";";for(var s=0;s<e.removeKeys.length;s++)if(e.removeKeys[s]===i){e.removeKeys.splice(s,1);break}}),t},T.Select.prototype.compileSelectGroup2=function(e){var n=this,t=e.selectgfns;return n.columns.forEach(function(r){e.ingroup.indexOf(r.nick)>-1&&(t+="r['"+(r.as||r.nick)+"']=g['"+r.nick+"'];")}),this.orderColumns&&this.orderColumns.length>0&&this.orderColumns.forEach(function(r,i){var s="$$$"+i;r instanceof T.Column&&e.groupColumns[r.columnid]?t+="r['"+s+"']=r['"+r.columnid+"'];":t+="r['"+s+"']="+r.toJS("g","")+";",e.removeKeys.push(s)}),new Function("g,params,alasql","var y;"+t+"return r")},T.Select.prototype.compileRemoveColumns=function(e){var n=this;typeof this.removecolumns<"u"&&(e.removeKeys=e.removeKeys.concat(this.removecolumns.filter(function(t){return typeof t.like>"u"}).map(function(t){return t.columnid})),e.removeLikeKeys=this.removecolumns.filter(function(t){return typeof t.like<"u"}).map(function(t){return t.like.value}))},T.Select.prototype.compileHaving=function(e){if(this.having){var n=this.having.toJS("g",-1);return e.havingfns=n,new Function("g,params,alasql","var y;return "+n)}return function(){return!0}},T.Select.prototype.compileOrder=function(e,n){var t=this;if(t.orderColumns=[],this.order){if(this.order&&this.order.length==1&&this.order[0].expression&&typeof this.order[0].expression=="function"){var r=this.order[0].expression,i=this.order[0].nullsOrder=="FIRST"?-1:this.order[0].nullsOrder=="LAST"?1:0;return function(f,h){var g=r(f),m=r(h);if(i){if(g==null)return m==null?0:i;if(m==null)return-i}return g>m?1:g==m?0:-1}}var s="",u="";return this.order.forEach(function(f,h){if(f.expression instanceof T.NumValue){if(f.expression.value>t.columns.length)throw new Error(`You are trying to order by column number ${f.expression.value} but you have only selected ${t.columns.length} columns.`);var g=t.columns[f.expression.value-1]}else var g=f.expression;t.orderColumns.push(g);var m="$$$"+h,b="";if(f.expression instanceof T.Column){var c=f.expression.columnid;if(l.options.valueof)b=".valueOf()";else if(e.xcolumns[c]){var d=e.xcolumns[c].dbtypeid;(d=="DATE"||d=="DATETIME"||d=="DATETIME2"||d=="STRING"||d=="NUMBER")&&(b=".valueOf()")}}if(f.expression instanceof T.ParamValue){var c=n[f.expression.param];if(l.options.valueof)b=".valueOf()";else if(e.xcolumns[c]){var d=e.xcolumns[c].dbtypeid;(d=="DATE"||d=="DATETIME"||d=="DATETIME2"||d=="STRING"||d=="NUMBER")&&(b=".valueOf()")}}f.nocase&&(b+=".toUpperCase()"),f.nullsOrder&&(f.nullsOrder=="FIRST"?s+="if((a['"+m+"'] != null) && (b['"+m+"'] == null)) return 1;":f.nullsOrder=="LAST"&&(s+="if((a['"+m+"'] == null) && (b['"+m+"'] != null)) return 1;"),s+="if((a['"+m+"'] == null) == (b['"+m+"'] == null)) {",u+="}"),s+="if((a['"+m+"']||'')"+b+(f.direction=="ASC"?">":"<")+"(b['"+m+"']||'')"+b+")return 1;",s+="if((a['"+m+"']||'')"+b+"==(b['"+m+"']||'')"+b+"){",u+="}"}),s+="return 0;",s+=u+"return -1",e.orderfns=s,new Function("a,b","var y;"+s)}},T.Select.prototype.compilePivot=function(e){var n=this,t=n.pivot.columnid,r=n.pivot.expr.aggregatorid,i=n.pivot.inlist,s=null;if(n.pivot.expr.expression.hasOwnProperty("columnid")?s=n.pivot.expr.expression.columnid:s=n.pivot.expr.expression.expression.columnid,s==null)throw"columnid not found";return i&&(i=i.map(function(u){return u.expr.columnid})),function(){var u=this;if(!u.data||u.data.length===0){u.columns=[];return}var f=Object.keys(u.data[0]),h=f.filter(function(N){return N!==t&&N!==s}),g=[],m={},b={},c={},d=[];if(u.data.forEach(function(N){if(!(i&&i.indexOf(N[t])===-1)){var w=h.map(function(G1){return N[G1]===void 0||N[G1]===null?"":N[G1]}).join("`"),T1=b[w];T1||(T1={},b[w]=T1,d.push(T1),h.forEach(function(G1){T1[G1]=N[G1]})),c[w]||(c[w]={});var v1=N[t],O1=N[s];if(c[w][v1]?O1!==null&&typeof O1<"u"&&c[w][v1]++:c[w][v1]=O1!==null&&typeof O1<"u"?1:0,m[v1]||(m[v1]=!0,g.push(v1)),r=="SUM"||r=="AVG"||r=="TOTAL")O1!==null&&typeof O1<"u"?T1[v1]=typeof T1[v1]>"u"||T1[v1]===null?Number(O1):T1[v1]+Number(O1):typeof T1[v1]>"u"&&(T1[v1]=null);else if(r=="COUNT")s==="*"||O1!==null&&typeof O1<"u"?T1[v1]=(T1[v1]||0)+1:typeof T1[v1]>"u"&&(T1[v1]=0);else if(r=="MIN")O1!==null&&typeof O1<"u"?(typeof T1[v1]>"u"||T1[v1]===null||O1<T1[v1])&&(T1[v1]=O1):typeof T1[v1]>"u"&&(T1[v1]=null);else if(r=="MAX")O1!==null&&typeof O1<"u"?(typeof T1[v1]>"u"||T1[v1]===null||O1>T1[v1])&&(T1[v1]=O1):typeof T1[v1]>"u"&&(T1[v1]=null);else if(r=="FIRST")typeof T1[v1]>"u"&&(T1[v1]=O1);else if(r=="LAST")T1[v1]=O1;else if(l.aggr[r])typeof T1[v1]>"u"?T1[v1]=l.aggr[r](O1,void 0,1):T1[v1]=l.aggr[r](O1,T1[v1],2);else throw new Error("Unknown aggregator in PIVOT clause: "+r)}}),r=="AVG")for(var v in b){var E=b[v];for(var O in c[v])if(E.hasOwnProperty(O)&&E[O]!==null){var R=c[v][O];R>0?E[O]=E[O]/R:E[O]=null}}u.data=d,i?g=i:g.sort();let A=u.columns.find(N=>N.columnid===s);if(!A&&u.sources&&u.sources.length>0){let N=u.sources[0].tableid,w=u.sources[0].databaseid;N&&w&&l.databases[w]?.tables?.[N]?.xcolumns&&(A=l.databases[w].tables[N].xcolumns[s])}A=A||{columnid:s,dbtypeid:"OBJECT"},u.columns=u.columns.filter(function(N){return h.includes(N.columnid)}),g.forEach(function(N){var w=Wt(A);w.columnid=N;const T1=(A.dbtypeid||"OBJECT").toUpperCase(),v1=["INT","INTEGER","SMALLINT","BIGINT","SERIAL","SMALLSERIAL","BIGSERIAL"],O1=[...v1,"NUMBER","FLOAT","DECIMAL","NUMERIC","MONEY"];r==="COUNT"?w.dbtypeid="INT":r==="AVG"?v1.includes(T1)?w.dbtypeid=A.dbtypeid:w.dbtypeid="FLOAT":r==="SUM"||r==="TOTAL"?O1.includes(T1)?w.dbtypeid=A.dbtypeid:w.dbtypeid="FLOAT":r==="MIN"||r==="MAX"||r==="FIRST"||r==="LAST"?w.dbtypeid=A.dbtypeid:w.dbtypeid||(w.dbtypeid="OBJECT"),u.columns.push(w)})}},T.Select.prototype.compileUnpivot=function(e){var n=this,t=n.unpivot.tocolumnid,r=n.unpivot.forcolumnid,i=n.unpivot.inlist.map(function(s){return s.columnid});return function(){var s=[],u=e.columns.map(function(f){return f.columnid}).filter(function(f){return i.indexOf(f)==-1&&f!=r&&f!=t});e.data.forEach(function(f){i.forEach(function(h){var g={};u.forEach(function(m){g[m]=f[m]}),g[r]=h,g[t]=f[h],s.push(g)})}),e.data=s}};const Ci=(e,n)=>{const t=[];let r=0;const i=e.length;for(let s=0;s<i+1;s++){const u=[];for(let f=0;f<i;f++){let h;e[f]instanceof T.Column?(e[f].nick=C2(e[f].columnid),n.groupColumns[C2(e[f].columnid)]=e[f].nick,h=`${e[f].nick}	${e[f].toJS("p",n.sources[0].alias,n.defcols)}`):(n.groupColumns[C2(e[f].toString())]=C2(e[f].toString()),h=`${C2(e[f].toString())}	${e[f].toJS("p",n.sources[0].alias,n.defcols)}`),r&1<<f&&u.push(h)}t.push(u),r=(r<<1)+1}return t},Ri=(e,n)=>{const t=[],r=e.length,i=1<<r;for(let s=0;s<i;s++){let u=[];for(let f=0;f<r;f++)s&1<<f&&(u=u.concat(O4(e[f],n)));t.push(u)}return t},Ii=(e,n)=>e.reduce((t,r)=>(t=t.concat(O4(r,n)),t),[]),N4=(e,n)=>{const t=[];for(let r=0;r<e.length;r++)for(let i=0;i<n.length;i++)t.push(e[r].concat(n[i]));return t};function O4(e,n){if(Array.isArray(e)){let t=[[]];for(let r=0;r<e.length;r++)if(e[r]instanceof T.Column)e[r].nick=e[r].nick?C2(e[r].nick):C2(e[r].columnid),n.groupColumns[e[r].nick]=e[r].nick,t=t.map(i=>i.concat(`${e[r].nick}	${e[r].toJS("p",n.sources[0].alias,n.defcols)}`));else if(e[r]instanceof T.FuncValue)n.groupColumns[C2(e[r].toString())]=C2(e[r].toString()),t=t.map(i=>i.concat(`${C2(e[r].toString())}	${e[r].toJS("p",n.sources[0].alias,n.defcols)}`));else if(e[r]instanceof T.GroupExpression)if(e[r].type=="ROLLUP")t=N4(t,Ci(e[r].group,n));else if(e[r].type=="CUBE")t=N4(t,Ri(e[r].group,n));else if(e[r].type=="GROUPING SETS")t=N4(t,Ii(e[r].group,n));else throw new Error("Unknown grouping function");else e[r]===""?t=[["1	1"]]:t=t.map(i=>i.concat(`${C2(e[r].toString())}	${e[r].toJS("p",n.sources[0].alias,n.defcols)}`));return t}return e instanceof T.FuncValue?(n.groupColumns[C2(e.toString())]=C2(e.toString()),[`${e.toString()}	${e.toJS("p",n.sources[0].alias,n.defcols)}`]):e instanceof T.Column?(e.nick=C2(e.columnid),n.groupColumns[e.nick]=e.nick,[`${e.nick}	${e.toJS("p",n.sources[0].alias,n.defcols)}`]):(n.groupColumns[C2(e.toString())]=C2(e.toString()),[`${C2(e.toString())}	${e.toJS("p",n.sources[0].alias,n.defcols)}`])}T.Select.prototype.compileDefCols=function(e,n){var t={".":{}};return this.from&&this.from.forEach(function(r){if(t["."][r.as||r.tableid]=!0,r instanceof T.Table){var i=r.as||r.tableid,s=l.databases[r.databaseid||n].tables[r.tableid];if(s===void 0)throw new Error("Table does not exist: "+r.tableid);s.columns&&s.columns.forEach(function(u){t[u.columnid]?t[u.columnid]="-":t[u.columnid]=i})}else if(!(r instanceof T.Select)){if(!(r instanceof T.Search)){if(!(r instanceof T.ParamValue)){if(!(r instanceof T.VarValue)){if(!(r instanceof T.FuncValue)){if(!(r instanceof T.FromData)){if(!(r instanceof T.Json)){if(!r.inserted)throw new Error("Unknown type of FROM clause")}}}}}}}}),this.joins&&this.joins.forEach(function(r){if(t["."][r.as||r.table.tableid]=!0,r.table){var i=r.as||r.table.tableid,s=r.table.databaseid||n,u=l.databases[s];if(u===void 0)throw new Error("Database does not exist: "+s);var f=u.tables[r.table.tableid];if(f===void 0)throw new Error("Table does not exist: "+r.table.tableid);f.columns&&f.columns.forEach(function(h){t[h.columnid]?t[h.columnid]="-":t[h.columnid]=i})}else if(!r.select){if(!r.param){if(!r.func)throw new Error("Unknown type of FROM clause")}}}),t},T.Union=class{constructor(n){Object.assign(this,n)}toString(){return"UNION"}compile(n){return null}},T.Apply=class{constructor(n){Object.assign(this,n)}toString(){let n=`${this.applymode} APPLY (${this.select.toString()})`;return this.as&&(n+=` AS ${this.as}`),n}},T.Over=class{constructor(n){Object.assign(this,n)}toString(){let n="OVER (";return this.partition&&(n+=`PARTITION BY ${this.partition.toString()}`,this.order&&(n+=" ")),this.order&&(n+=`ORDER BY ${this.order.toString()}`),n+=")",n}};{const e=Object.assign;class n{constructor(k){e(this,k)}toString(){return this.expression.toString()}execute(k,s2,K1){if(this.expression){l.precompile(this,k,s2);var V1=new Function("params,alasql,p","var y;return "+this.expression.toJS("({})","",null)).bind(this),T2=V1(s2,l);return K1&&(T2=K1(T2)),T2}}}class t{constructor(k){e(this,k)}toString(){var k=this.expression.toString();return this.order&&(k+=" "+this.order.toString()),this.nocase&&(k+=" COLLATE NOCASE"),this.direction&&(k+=" "+this.direction),k}findAggregator(k){this.expression.findAggregator&&this.expression.findAggregator(k)}toJS(k,s2,K1){return this.expression.reduced?"true":this.expression.toJS(k,s2,K1)}compile(k,s2,K1){return this.reduced?ee():new Function("p","var y;return "+this.toJS(k,s2,K1))}}class r{constructor(k){e(this,k)}toString(){var k="``"+this.value+"``";return k}toJS(){return"("+this.value+")"}execute(k,s2,K1){var V1=1,T2=new Function("params,alasql,p",this.value);return T2(s2,l),K1&&(V1=K1(V1)),V1}}class i{constructor(k){e(this,k)}toString(){var k=this.value;return this.value1&&(k=this.value1+"."+k),k}}class s{constructor(k){e(this,k)}toString(){var k=" ";return this.joinmode&&(k+=this.joinmode+" "),k+="JOIN "+this.table.toString(),k}}class u{constructor(k){e(this,k)}toString(){var k=this.tableid;return this.databaseid&&(k=this.databaseid+"."+k),k}}class f{constructor(k){e(this,k)}toString(){var k=this.viewid;return this.databaseid&&(k=this.databaseid+"."+k),k}}const h=new Set(["-","*","/","%","^"]),g=new Set(["||"]),m=new Set(["AND","OR","NOT","=","==","===","!=","!==","!===",">",">=","<","<=","IN","NOT IN","LIKE","NOT LIKE","REGEXP","GLOB","BETWEEN","NOT BETWEEN","IS NULL","IS NOT NULL"]);class b{constructor(k){e(this,k)}toString(){const k=this.left.toString();let s2;return this.op==="IN"||this.op==="NOT IN"?`${k} ${this.op} (${this.right.toString()})`:this.allsome?`${k} ${this.op} ${this.allsome} (${this.right.toString()})`:this.op==="->"||this.op==="!"?(s2=`${k}${this.op}`,typeof this.right!="string"&&typeof this.right!="number"?s2+`(${this.right.toString()})`:s2+this.right.toString()):this.op==="BETWEEN"||this.op==="NOT BETWEEN"?`${k} ${this.op} ${this.right1.toString()} AND ${this.right2.toString()}`:`${k} ${this.op} ${this.allsome?this.allsome+" ":""}${this.right.toString()}`}findAggregator(k){this.left&&this.left.findAggregator&&this.left.findAggregator(k),this.right&&this.right.findAggregator&&!this.allsome&&this.right.findAggregator(k)}toType(k){if(h.has(this.op))return"number";if(g.has(this.op))return"string";if(this.op==="+"){const s2=this.left.toType(k),K1=this.right.toType(k);if(s2==="string"||K1==="string")return"string";if(s2==="number"||K1==="number")return"number"}return m.has(this.op)||this.allsome?"boolean":this.op?"unknown":this.left.toType(k)}toJS(k,s2,K1){var V1;let T2=[],p2=this.op,O2=this,N2=function(g2){return g2.toJS&&(g2=g2.toJS(k,s2,K1)),"y["+(T2.push(g2)-1)+"]"};var A2=function(){return N2(O2.left)},w2=function(){return N2(O2.right)};if(this.op==="=")p2="===";else if(this.op==="<>")p2="!=";else if(this.op==="OR")p2="||";else if(this.op==="->"){const g2=`(${A2()} || {})`;if(typeof this.right=="string")V1=`${g2}["${C2(this.right)}"]`;else if(typeof this.right=="number")V1=`${g2}[${this.right}]`;else if(this.right instanceof T.FuncValue){let D2=[];this.right.args&&this.right.args.length>0&&(D2=this.right.args.map(N2)),V1=`${g2}[${JSON.stringify(this.right.funcid)}](${D2.join(",")})`}else V1=`${g2}[${w2()}]`}else if(this.op==="!")typeof this.right=="string"&&(V1=`alasql.databases[alasql.useid].objects[${A2()}]["${this.right}"]`);else if(this.op==="IS"){const g2=A2(),D2=w2();this.right instanceof T.NullValue||this.right.op==="NOT"&&this.right.right instanceof T.NullValue?V1=`((${g2} == null) === (${D2} == null))`:V1=`((${g2} == ${D2}) || (${g2} < 0 && true == ${D2}))`}else if(this.op==="==")V1=`alasql.utils.deepEqual(${A2()}, ${w2()})`;else if(this.op==="==="||this.op==="!===")V1=`(${this.op==="!==="?"!":""}((${A2()}).valueOf() === (${w2()}).valueOf()))`;else if(this.op==="!==")V1=`(!alasql.utils.deepEqual(${A2()}, ${w2()}))`;else if(this.op==="||")V1=`(''+(${A2()} || '') + (${w2()} || ''))`;else if(this.op==="LIKE"||this.op==="NOT LIKE")V1=`(${this.op==="NOT LIKE"?"!":""}alasql.utils.like(${w2()}, ${A2()}${this.escape?`, ${N2(this.escape)}`:""}))`;else if(this.op==="REGEXP")V1=`alasql.stdfn.REGEXP_LIKE(${A2()}, ${w2()})`;else if(this.op==="GLOB")V1=`alasql.utils.glob(${A2()}, ${w2()})`;else if(this.op==="BETWEEN"||this.op==="NOT BETWEEN"){const g2=A2();V1=`(${this.op==="NOT BETWEEN"?"!":""}((${N2(this.right1)} <= ${g2}) && (${g2} <= ${N2(this.right2)})))`}else if(this.op==="IN")if(this.right instanceof T.Select)V1=`alasql.utils.flatArray(this.queriesfn[${this.queriesidx}](params, null, ${k})).indexOf(alasql.utils.getValueOf(${A2()})) > -1`;else if(Array.isArray(this.right))if(!l.options.cache||this.right.some(g2=>g2 instanceof T.ParamValue))V1=`(new Set([${this.right.map(N2).join(",")}]).has(alasql.utils.getValueOf(${A2()})))`;else{l.sets=l.sets||{};const g2=this.right.map(Y2=>Y2.value),D2=g2.join(",");l.sets[D2]=l.sets[D2]||new Set(g2),V1=`alasql.sets["${D2}"].has(alasql.utils.getValueOf(${A2()}))`}else V1=`(${w2()}.indexOf(${A2()}) > -1)`;else if(this.op==="NOT IN")if(this.right instanceof T.Select)V1=`alasql.utils.flatArray(this.queriesfn[${this.queriesidx}](params, null, p)).indexOf(alasql.utils.getValueOf(${A2()})) < 0`;else if(Array.isArray(this.right))if(!l.options.cache||this.right.some(g2=>g2 instanceof T.ParamValue))V1=`(!(new Set([${this.right.map(N2).join(",")}]).has(alasql.utils.getValueOf(${A2()}))))`;else{l.sets=l.sets||{};const g2=this.right.map(Y2=>Y2.value),D2=g2.join(",");l.sets[D2]=l.sets[D2]||new Set(g2),V1=`!alasql.sets["${D2}"].has(alasql.utils.getValueOf(${A2()}))`}else V1=`(${w2()}.indexOf(${A2()}) === -1)`;if(this.allsome==="ALL"){var V1;if(this.right instanceof T.Select)V1="alasql.utils.flatArray(this.query.queriesfn["+this.queriesidx+"](params,null,p))",V1+=".every(function(b){return (",V1+=A2()+")"+p2+"b})";else if(Array.isArray(this.right))V1=""+(this.right.length==1?N2(this.right[0]):"["+this.right.map(N2).join(",")+"]"),V1+=".every(function(b){return (",V1+=A2()+")"+p2+"b})";else throw new Error("NOT IN operator without SELECT")}if(this.allsome==="SOME"||this.allsome==="ANY"){var V1;if(this.right instanceof T.Select)V1="alasql.utils.flatArray(this.query.queriesfn["+this.queriesidx+"](params,null,p))",V1+=".some(function(b){return (",V1+=A2()+")"+p2+"b})";else if(Array.isArray(this.right))V1=""+(this.right.length==1?N2(this.right[0]):"["+this.right.map(N2).join(",")+"]"),V1+=".some(function(b){return (",V1+=A2()+")"+p2+"b})";else throw new Error("SOME/ANY operator without SELECT")}if(this.op==="AND"){if(this.left.reduced){if(this.right.reduced)return"true";V1=w2()}else this.right.reduced&&(V1=A2());p2="&&"}var Z2=V1||"("+A2()+p2+w2()+")",X2="y=[("+T2.join("), (")+")]";return p2==="&&"||p2==="||"||p2==="IS"||p2==="IS NULL"||p2==="IS NOT NULL"?"("+X2+", "+Z2+")":`(${X2}, y.some(e => e == null) ? void 0 : ${Z2})`}}class c{constructor(k){e(this,k)}toString(){return"@"+this.variable}toType(){return"unknown"}toJS(){return"alasql.vars['"+C2(this.variable)+"']"}}class d{constructor(k){e(this,k)}toString(){return this.value.toString()}toType(){return"number"}toJS(){return""+this.value}}class v{constructor(k){e(this,k)}toString(){return"'"+this.value.toString()+"'"}toType(){return"string"}toJS(){return"'"+C2(this.value)+"'"}}class E{constructor(k){e(this,k)}toString(){return"VALUE"}toType(){return"object"}toJS(k,s2,K1){return k}}class O{constructor(k){e(this,k)}toString(){return"ARRAY[]"}toType(){return"object"}toJS(k,s2,K1){return"[("+this.value.map(function(V1){return V1.toJS(k,s2,K1)}).join("), (")+")]"}}class R{constructor(k){e(this,k)}toString(){return this.value?"TRUE":"FALSE"}toType(){return"boolean"}toJS(){return this.value?"true":"false"}}class A{constructor(k){e(this,k)}toString(){return"NULL"}toJS(){return"undefined"}}class N{constructor(k){e(this,k)}toString(){return"$"+this.param}toJS(){return typeof this.param=="string"?"params['"+this.param+"']":"params["+this.param+"]"}}const w={"~":"~","-":"-","+":"+",NOT:"!"};class T1{constructor(k){e(this,k)}toString(){const{op:k,right:s2}=this,K1=s2.toString();switch(k){case"~":case"-":case"+":case"#":return k+K1;case"NOT":return k+"("+K1+")";default:return"("+K1+")"}}findAggregator(k){this.right.findAggregator&&this.right.findAggregator(k)}toType(){switch(this.op){case"-":case"+":return"number";case"NOT":return"boolean";default:return"string"}}toJS(k,s2,K1){if(this.right instanceof v1&&this.op==="#")return`(alasql.databases[alasql.useid].objects['${this.right.columnid}'])`;const V1=this.right.toJS(k,s2,K1);if(w.hasOwnProperty(this.op))return`(${w[this.op]}(${V1}))`;if(this.op==null)return`(${V1})`;throw new Error(`Unsupported operator: ${this.op}`)}}class v1{constructor(k){e(this,k)}toString(){let k=this.columnid;return this.columnid==+this.columnid&&(k="["+this.columnid+"]"),this.tableid&&(k=this.tableid+(this.columnid===+this.columnid?"":".")+k,this.databaseid&&(k=this.databaseid+"."+k)),k}toJS(k,s2,K1){if(!this.tableid&&s2===""&&!K1)return this.columnid!=="_"?`${k}['${this.columnid}']`:k==="g"?"g['_']":k;if(k==="g")return`g['${this.nick}']`;if(this.tableid)return this.columnid!=="_"?`${k}['${this.tableid}']['${this.columnid}']`:k==="g"?"g['_']":`${k}['${this.tableid}']`;if(K1){const V1=K1[this.columnid];if(V1==="-")throw new Error(`Cannot resolve column "${this.columnid}" because it exists in two source tables`);return V1?this.columnid!=="_"?`${k}['${V1}']['${this.columnid}']`:`${k}['${V1}']`:this.columnid!=="_"?`${k}['${this.tableid||s2}']['${this.columnid}']`:`${k}['${this.tableid||s2}']`}return s2===-1?`${k}['${this.columnid}']`:this.columnid!=="_"?`${k}['${this.tableid||s2}']['${this.columnid}']`:`${k}['${this.tableid||s2}']`}}class O1{constructor(k){e(this,k)}toString(){const k=this.aggregatorid==="REDUCE"?this.funcid.replace(Tr,""):this.aggregatorid,s2=this.distinct?"DISTINCT ":"",K1=this.expression?this.expression.toString():"",V1=this.over?` ${this.over.toString()}`:"";return`${k}(${s2}${K1})${V1}`}findAggregator(k){const s2=C2(this.toString())+":"+k.selectGroup.length;this.nick||(this.nick=s2,k.removeKeys.includes(s2)||k.removeKeys.push(s2)),k.selectGroup.push(this)}toType(){return["SUM","COUNT","AVG","MIN","MAX","AGGR","VAR","STDDEV","TOTAL"].includes(this.aggregatorid)?"number":this.aggregatorid==="ARRAY"?"array":this.expression.toType()}toJS(){var k=this.nick;return k===void 0&&(k=C2(this.toString())),"g['"+k+"']"}}class G1{constructor(k){e(this,k)}}G1.prototype.toString=t.prototype.toString;class B1{constructor(k){e(this,k)}toString(){return this.type+"("+this.group.toString()+")"}}e(T,{AggrValue:O1,ArrayValue:O,Column:v1,DomainValueValue:E,Expression:t,ExpressionStatement:n,GroupExpression:B1,JavaScript:r,Join:s,Literal:i,LogicValue:R,NullValue:A,NumValue:d,Op:b,OrderExpression:G1,ParamValue:N,StringValue:v,Table:u,UniOp:T1,VarValue:c,View:f})}T.FromData=function(e){return T.extend(this,e)},T.FromData.prototype.toString=function(){return this.data?"DATA("+(Math.random()*1e16|0)+")":"?"},T.FromData.prototype.toJS=function(){},T.Select.prototype.exec=function(e,n){this.preparams&&(e=this.preparams.concat(e));var t=l.useid,r=l.databases[t],i=this.toString(),s=ot(i),u=this.compile(t);if(u){u.sql=i,u.dbversion=r.dbversion,r.sqlCacheSize>l.MAXSQLCACHESIZE&&r.resetSqlCache(),r.sqlCacheSize++,r.sqlCache[s]=u;var f=l.res=u(e,n);return f}},T.Select.prototype.Select=function(){var e=this,n=[];if(arguments.length>1)n=Array.prototype.slice.call(arguments);else if(arguments.length==1)Array.isArray(arguments[0])?n=arguments[0]:n=[arguments[0]];else throw new Error("Wrong number of arguments of Select() function");return e.columns=[],n.forEach(function(t){if(typeof t=="string")e.columns.push(new T.Column({columnid:t}));else if(typeof t=="function"){var r=0;e.preparams?r=e.preparams.length:e.preparams=[],e.preparams.push(t),e.columns.push(new T.Column({columnid:"*",func:t,param:r}))}}),e},T.Select.prototype.From=function(e){var n=this;if(n.from||(n.from=[]),Array.isArray(e)){var t=0;n.preparams?t=n.preparams.length:n.preparams=[],n.preparams.push(e),n.from.push(new T.ParamValue({param:t}))}else if(typeof e=="string")n.from.push(new T.Table({tableid:e}));else throw new Error("Unknown arguments in From() function");return n},T.Select.prototype.OrderBy=function(){var e=this,n=[];if(e.order=[],arguments.length==0)n=["_"];else if(arguments.length>1)n=Array.prototype.slice.call(arguments);else if(arguments.length==1)Array.isArray(arguments[0])?n=arguments[0]:n=[arguments[0]];else throw new Error("Wrong number of arguments of Select() function");return n.length>0&&n.forEach(function(t){var r=new T.Column({columnid:t});typeof t=="function"&&(r=t),e.order.push(new T.OrderExpression({expression:r,direction:"ASC"}))}),e},T.Select.prototype.Top=function(e){var n=this;return n.top=new T.NumValue({value:e}),n},T.Select.prototype.GroupBy=function(){var e=this,n=[];if(arguments.length>1)n=Array.prototype.slice.call(arguments);else if(arguments.length==1)Array.isArray(arguments[0])?n=arguments[0]:n=[arguments[0]];else throw new Error("Wrong number of arguments of Select() function");return e.group=[],n.forEach(function(t){var r=new T.Column({columnid:t});e.group.push(r)}),e},T.Select.prototype.Where=function(e){var n=this;return typeof e=="function"&&(n.where=e),n},T.FuncValue=function(e){return Object.assign(this,e)};let Tr=/[^0-9A-Z_$]+/i;T.FuncValue.prototype.toString=function(){let e="";return l.fn[this.funcid]?e+=this.funcid:l.aggr[this.funcid]?e+=this.funcid:(l.stdlib[this.funcid.toUpperCase()]||l.stdfn[this.funcid.toUpperCase()])&&(e+=this.funcid.toUpperCase().replace(Tr,"")),this.funcid!=="CURRENT_TIMESTAMP"&&(e+="(",this.args&&this.args.length>0&&(e+=this.args.map(function(n){return n.toString()}).join(",")),e+=")"),e},T.FuncValue.prototype.execute=function(e,n,t){let r=1;return l.precompile(this,e,n),new Function("params,alasql","var y;return "+this.toJS("","",null))(n,l),t&&(r=t(r)),r},T.FuncValue.prototype.findAggregator=function(e){this.args&&this.args.length>0&&this.args.forEach(function(n){n.findAggregator&&n.findAggregator(e)})},T.FuncValue.prototype.toJS=function(e,n,t){var r="",i=this.funcid;return!l.fn[i]&&l.stdlib[i.toUpperCase()]?this.args&&this.args.length>0?r+=l.stdlib[i.toUpperCase()].apply(this,this.args.map(function(s){return s.toJS(e,n)})):r+=l.stdlib[i.toUpperCase()]():!l.fn[i]&&l.stdfn[i.toUpperCase()]?(this.newid&&(r+="new "),r+="alasql.stdfn["+JSON.stringify(this.funcid.toUpperCase())+"](",this.args&&this.args.length>0&&(r+=this.args.map(function(s){return s.toJS(e,n,t)}).join(",")),r+=")"):(this.newid&&(r+="new "),r+="alasql.fn["+JSON.stringify(this.funcid)+"](",this.args&&this.args.length>0&&(r+=this.args.map(function(s){return s.toJS(e,n,t)}).join(",")),r+=")"),r};var R2=l.stdlib={},I2=l.stdfn={};R2.ABS=function(e){return"Math.abs("+e+")"},R2.CLONEDEEP=function(e){return"alasql.utils.cloneDeep("+e+")"},I2.CONCAT=function(){return Array.prototype.slice.call(arguments).join("")},R2.EXP=function(e){return"Math.pow(Math.E,"+e+")"},R2.IIF=function(e,n,t){if(arguments.length===3)return`((${e}) ? (${n}) : (${t}))`;throw new Error("Number of arguments of IFF is not equals to 3")},R2.IFNULL=function(e,n){return`((typeof ${e} === "undefined" || ${e} === null) ? ${n} : ${e})`},R2.INSTR=function(e,n){return`((${e}).indexOf(${n}) + 1)`},R2.LEN=R2.LENGTH=function(e){return Dt(e,"y.length")},R2.LOWER=R2.LCASE=function(e){return Dt(e,"String(y).toLowerCase()")},R2.LTRIM=function(e){return Dt(e,'y.replace(/^[ ]+/,"")')},R2.RTRIM=function(e){return Dt(e,'y.replace(/[ ]+$/,"")')},R2.MAX=R2.GREATEST=function(){return"["+Array.prototype.join.call(arguments,",")+"].reduce(function (a, b) { return a > b ? a : b; })"},R2.MIN=R2.LEAST=function(){return"["+Array.prototype.join.call(arguments,",")+"].reduce(function (a, b) { return a < b ? a : b; })"},R2.SUBSTRING=R2.SUBSTR=R2.MID=function(e,n,t){if(arguments.length==2)return Dt(e,"y.substr("+n+"-1)");if(arguments.length==3)return Dt(e,"y.substr("+n+"-1,"+t+")")},I2.REGEXP_LIKE=function(e,n,t){return(e||"").search(RegExp(n,t))>-1},R2.ISNULL=R2.NULLIF=function(e,n){return"("+e+"=="+n+"?undefined:"+e+")"},R2.POWER=function(e,n){return"Math.pow("+e+","+n+")"},R2.RANDOM=function(e){return arguments.length==0?"Math.random()":"(Math.random()*("+e+")|0)"},R2.ROUND=function(e,n){return arguments.length==2?"Math.round(("+e+")*Math.pow(10,("+n+")))/Math.pow(10,("+n+"))":"Math.round("+e+")"},R2.CEIL=R2.CEILING=function(e){return"Math.ceil("+e+")"},R2.FLOOR=function(e){return"Math.floor("+e+")"},R2.ROWNUM=function(){return"1"},R2.ROW_NUMBER=function(){return"1"},R2.SQRT=function(e){return"Math.sqrt("+e+")"},R2.TRIM=function(e){return Dt(e,"y.trim()")},R2.UPPER=R2.UCASE=function(e){return Dt(e,"String(y).toUpperCase()")},I2.CONCAT_WS=function(){var e=Array.prototype.slice.call(arguments);return e=e.filter(n=>!(n===null||typeof n>"u")),e.slice(1,e.length).join(e[0]||"")},l.aggr.group_concat=l.aggr.GROUP_CONCAT=function(e,n,t){return t===1?""+e:(t===2&&(n+=","+e),n)},l.aggr.median=l.aggr.MEDIAN=function(e,n,t){if(t===2)return e!==null&&n.push(e),n;if(t===1)return e===null?[]:[e];if(!n.length)return null;let r=n.sort((f,h)=>f>h?1:f<h?-1:0),i=(r.length+1)/2,s=i|0,u=r[s-1];return i===s||typeof u!="number"&&!(u instanceof Number)?u:(u+r[s])/2},l.aggr.QUART=function(e,n,t,r){if(t===2)return e!==null&&n.push(e),n;if(t===1)return e===null?[]:[e];if(!n.length)return n;r=r||1;var i=n.sort(function(u,f){return u===f?0:u>f?1:-1});let s=r*(i.length+1)/4;return Number.isInteger(s)?i[s-1]:i[Math.floor(s)]},l.aggr.QUART2=function(e,n,t){return l.aggr.QUART(e,n,t,2)},l.aggr.QUART3=function(e,n,t){return l.aggr.QUART(e,n,t,3)},l.aggr.VAR=function(e,n,t){return t===1?e===null?{sum:0,sumSq:0,count:0}:{sum:e,sumSq:e*e,count:1}:t===2?(e!==null&&(n.sum+=e,n.sumSq+=e*e,n.count++),n):n.count>1?(n.sumSq-n.sum*n.sum/n.count)/(n.count-1):0},l.aggr.STDEV=function(e,n,t){return t===1||t===2?l.aggr.VAR(e,n,t):Math.sqrt(l.aggr.VAR(e,n,t))},l.aggr.STDEV=function(e,n,t){return t===1||t===2?l.aggr.VAR(e,n,t):Math.sqrt(l.aggr.VAR(e,n,t))},l.aggr.VARP=function(e,n,t){if(t===1)return{count:1,sum:e,sumSq:e*e};if(t===2)return n.count++,n.sum+=e,n.sumSq+=e*e,n;if(n.count>0){const r=n.sum/n.count;return n.sumSq/n.count-r*r}else return 0},l.aggr.STD=l.aggr.STDDEV=l.aggr.STDEVP=function(e,n,t){return t==1||t==2?l.aggr.VARP(e,n,t):Math.sqrt(l.aggr.VARP(e,n,t))},l._aggrOriginal=l.aggr,l.aggr={},Object.keys(l._aggrOriginal).forEach(function(e){l.aggr[e]=function(n,t,r){if(!(r===3&&typeof t>"u"))return l._aggrOriginal[e].apply(null,arguments)}}),I2.REPLACE=function(e,n,t){return(e||"").split(n).join(t)};for(var z2=[],j2=0;j2<256;j2++)z2[j2]=(j2<16?"0":"")+j2.toString(16);I2.NEWID=I2.UUID=I2.GEN_RANDOM_UUID=function(){var e=Math.random()*4294967295|0,n=Math.random()*4294967295|0,t=Math.random()*4294967295|0,r=Math.random()*4294967295|0;return z2[e&255]+z2[e>>8&255]+z2[e>>16&255]+z2[e>>24&255]+"-"+z2[n&255]+z2[n>>8&255]+"-"+z2[n>>16&15|64]+z2[n>>24&255]+"-"+z2[t&63|128]+z2[t>>8&255]+"-"+z2[t>>16&255]+z2[t>>24&255]+z2[r&255]+z2[r>>8&255]+z2[r>>16&255]+z2[r>>24&255]},T.CaseValue=function(e){return Object.assign(this,e)},T.CaseValue.prototype.toString=function(){var e="CASE ";return this.expression&&(e+=this.expression.toString()),this.whens&&(e+=this.whens.map(function(n){return" WHEN "+n.when.toString()+" THEN "+n.then.toString()}).join()),e+=" END",e},T.CaseValue.prototype.findAggregator=function(e){this.expression&&this.expression.findAggregator&&this.expression.findAggregator(e),this.whens&&this.whens.length>0&&this.whens.forEach(function(n){n.when.findAggregator&&n.when.findAggregator(e),n.then.findAggregator&&n.then.findAggregator(e)}),this.elses&&this.elses.findAggregator&&this.elses.findAggregator(e)},T.CaseValue.prototype.toJS=function(e,n,t){let r=`(((${e}, params, alasql) => {
        let y, r;`;return this.expression?(r+=`let v = ${this.expression.toJS(e,n,t)};`,this.whens.forEach((i,s)=>{const u=`v === ${i.when.toJS(e,n,t)}`,f=`r = ${i.then.toJS(e,n,t)}`;r+=`${s===0?"if":" else if"} (${u}) { ${f}; }`})):this.whens.forEach((i,s)=>{const u=i.when.toJS(e,n,t),f=`r = ${i.then.toJS(e,n,t)}`;r+=`${s===0?"if":" else if"} (${u}) { ${f}; }`}),this.elses&&(r+=` else { r = ${this.elses.toJS(e,n,t)}; }`),r+="; return r; }))("+e+", params, alasql)",r},T.Json=function(e){return Object.assign(this,e)},T.Json.prototype.toString=function(){var e="";return e+=At(this.value),e+="",e};const At=l.utils.JSONtoString=function(e){if(typeof e=="string")return`"${e}"`;if(typeof e=="number"||typeof e=="boolean")return String(e);if(typeof e=="bigint")return`${e.toString()}n`;if(Array.isArray(e))return`[${e.map(n=>At(n)).join(",")}]`;if(typeof e=="object")if(!e.toJS||e instanceof T.Json){const n=[];for(const t in e){const r=typeof t=="string"?`"${t}"`:String(t),i=At(e[t]);n.push(`${r}:${i}`)}return`{${n.join(",")}}`}else{if(e.toString)return e.toString();throw new Error(`1: Cannot show JSON object ${JSON.stringify(e)}`)}else throw new Error(`2: Cannot show JSON object ${JSON.stringify(e)}`)};function B3(e,n,t,r){var i="";if(typeof e=="string")i='"'+e+'"';else if(typeof e=="number")i="("+e+")";else if(typeof e=="boolean")i=e;else if(typeof e=="bigint")i=e.toString()+"n";else if(typeof e=="object")if(Array.isArray(e))i+=`[${e.map(s=>B3(s,n,t,r)).join(",")}]`;else if(!e.toJS||e instanceof T.Json){let s=[];for(const u in e){let f=typeof u=="string"?`"${u}"`:u.toString(),h=B3(e[u],n,t,r);s.push(`${f}:${h}`)}i=`{${s.join(",")}}`}else if(e.toJS)i=e.toJS(n,t,r);else throw new Error(`Cannot parse JSON object ${JSON.stringify(e)}`);else throw new Error("2Can not parse JSON object "+JSON.stringify(e));return i}T.Json.prototype.toJS=function(e,n,t){return B3(this.value,e,n,t)},T.Convert=function(e){return Object.assign(this,e)},T.Convert.prototype.toString=function(){var e="CONVERT(";return e+=this.dbtypeid,typeof this.dbsize<"u"&&(e+="("+this.dbsize,this.dbprecision&&(e+=","+this.dbprecision),e+=")"),e+=","+this.expression.toString(),this.style&&(e+=","+this.style),e+=")",e},T.Convert.prototype.toJS=function(e,n,t){return`alasql.stdfn.CONVERT(${this.expression.toJS(e,n,t)}, {
        dbtypeid: "${this.dbtypeid}",
        dbsize: ${this.dbsize},
        dbprecision: ${this.dbprecision},
        style: ${this.style}
    })`};function wi(e){var n=e.getMonth()+1,t=e.getYear(),r=e.getFullYear(),i=e.getDate(),s=e.toString().substr(4,3),u=("0"+i).substr(-2),f=("0"+n).substr(-2),h=("0"+t).substr(-2),g=("0"+e.getHours()).substr(-2),m=("0"+e.getMinutes()).substr(-2),b=("0"+e.getSeconds()).substr(-2),c=("00"+e.getMilliseconds()).substr(-3);return{month:n,year:t,fullYear:r,date:i,day:s,formattedDate:u,formattedMonth:f,formattedYear:h,formattedHour:g,formattedMinutes:m,formattedSeconds:b,formattedMilliseconds:c}}l.stdfn.CONVERT=function(e,n){var t=e,r=n.dbtypeid?.toUpperCase(),i,s;if((n.style||n.dbtypeid=="Date"||["DATE","DATETIME","DATETIME2"].indexOf(r)>-1)&&(/\d{8}/.test(t)?i=new Date(+t.substr(0,4),+t.substr(4,2)-1,+t.substr(6,2)):i=Ze(t),s=wi(i)),n.style)switch(n.style){case 1:t=s.formattedMonth+"/"+s.formattedDate+"/"+s.formattedYear;break;case 2:t=s.formattedYear+"."+s.formattedMonth+"."+s.formattedDate;break;case 3:t=s.formattedDate+"/"+s.formattedMonth+"/"+s.formattedYear;break;case 4:t=s.formattedDate+"."+s.formattedMonth+"."+s.formattedYear;break;case 5:t=s.formattedDate+"-"+s.formattedMonth+"-"+s.formattedYear;break;case 6:t=s.formattedDate+" "+s.day.toLowerCase()+" "+s.formattedYear;break;case 7:t=s.day+" "+s.formattedDate+","+s.formattedYear;break;case 8:case 108:t=s.formattedHour+":"+s.formattedMinutes+":"+s.formattedSeconds;break;case 10:t=s.formattedMonth+"-"+s.formattedDate+"-"+s.formattedYear;break;case 11:t=s.formattedYear+"/"+s.formattedMonth+"/"+s.formattedDate;break;case 12:t=s.formattedYear+s.formattedMonth+s.formattedDate;break;case 101:t=s.formattedMonth+"/"+s.formattedDate+"/"+s.fullYear;break;case 102:t=s.fullYear+"."+s.formattedMonth+"."+s.formattedDate;break;case 103:t=s.formattedDate+"/"+s.formattedMonth+"/"+s.fullYear;break;case 104:t=s.formattedDate+"."+s.formattedMonth+"."+s.fullYear;break;case 105:t=s.formattedDate+"-"+s.formattedMonth+"-"+s.fullYear;break;case 106:t=s.formattedDate+" "+s.day.toLowerCase()+" "+s.fullYear;break;case 107:t=s.day+" "+s.formattedDate+","+s.fullYear;break;case 110:t=s.formattedMonth+"-"+s.formattedDate+"-"+s.fullYear;break;case 111:t=s.fullYear+"/"+s.formattedMonth+"/"+s.formattedDate;break;case 112:t=s.fullYear+s.formattedMonth+s.formattedDate;break;default:throw new Error("The CONVERT style "+n.style+" is not realized yet.")}switch(r){case"DATE":return`${s.formattedYear}.${s.formattedMonth}.${s.formattedDate}`;case"DATETIME":case"DATETIME2":return`${s.fullYear}.${s.formattedMonth}.${s.formattedDate} ${s.formattedHour}:${s.formattedMinutes}:${s.formattedSeconds}.${s.formattedMilliseconds}`;case"MONEY":var u=+t;return(u|0)+u*100%100/100;case"BOOLEAN":return!!t;case"INT":case"INTEGER":case"SMALLINT":case"BIGINT":case"SERIAL":case"SMALLSERIAL":case"BIGSERIAL":return t|0;case"STRING":case"VARCHAR":case"NVARCHAR":case"CHARACTER VARIABLE":return n.dbsize?String(t).substr(0,n.dbsize):String(t);case"CHAR":case"CHARACTER":case"NCHAR":return(t+" ".repeat(n.dbsize)).substr(0,n.dbsize);case"NUMBER":case"FLOAT":case"DECIMAL":case"NUMERIC":var u=+t;return n.dbsize!==void 0&&(u=parseFloat(u.toPrecision(n.dbsize))),n.dbprecision!==void 0&&(u=parseFloat(u.toFixed(n.dbprecision))),u;case"JSON":if(typeof t=="object")return t;try{return JSON.parse(t)}catch{throw new Error("Cannot convert string to JSON")}case"Date":return t;default:return t}},T.ColumnDef=function(e){return Object.assign(this,e)},T.ColumnDef.prototype.toString=function(){let e=this.columnid;return this.dbtypeid&&(e+=" "+this.dbtypeid),this.dbsize&&(e+="("+this.dbsize,this.dbprecision&&(e+=","+this.dbprecision),e+=")"),this.primarykey&&(e+=" PRIMARY KEY"),this.notnull&&(e+=" NOT NULL"),e},T.CreateTable=function(e){return Object.assign(this,e)},T.CreateTable.prototype.toString=function(){let e=`CREATE${this.temporary?" TEMPORARY":""}${this.view?" VIEW":` ${this.class?"CLASS":"TABLE"}`}${this.ifnotexists?" IF NOT EXISTS":""} ${this.table.toString()}`;return this.viewcolumns&&(e+=`(${this.viewcolumns.map(n=>n.toString()).join(",")})`),this.as?e+=` AS ${this.as}`:e+=` (${this.columns.map(n=>n.toString()).join(",")})`,this.view&&this.select&&(e+=` AS ${this.select.toString()}`),e},T.CreateTable.prototype.execute=function(e,n,t){var r=l.databases[this.table.databaseid||e],i=this.table.tableid;if(!i)throw new Error("Table name is not defined");var s=this.columns,u=this.constraints||[];if(this.ifnotexists&&r.tables[i])return t?t(0):0;if(r.tables[i])throw new Error("Can not create table '"+i+"', because it already exists in the database '"+r.databaseid+"'");var f=r.tables[i]=new l.Table;this.class&&(f.isclass=!0);var h=[],g=[];if(s&&s.forEach(function(c){var d=c.dbtypeid;l.fn[d]||(d=d.toUpperCase()),["SERIAL","SMALLSERIAL","BIGSERIAL"].indexOf(d)>-1&&(c.identity={value:1,step:1});var v={columnid:c.columnid,dbtypeid:d,dbsize:c.dbsize,dbprecision:c.dbprecision,notnull:c.notnull,identity:c.identity};if(c.identity&&(f.identities[c.columnid]={value:+c.identity.value,step:+c.identity.step}),c.check&&f.checks.push({id:c.check.constrantid,fn:new Function("r","var y;return "+c.check.expression.toJS("r",""))}),c.default&&h.push(JSON.stringify(""+c.columnid)+":"+c.default.toJS("r","")),c.primarykey){var E=f.pk={};E.columns=[c.columnid],E.onrightfns=`r[${JSON.stringify(c.columnid)}]`,E.onrightfn=new Function("r","var y;return "+E.onrightfns),E.hh=ot(E.onrightfns),f.uniqs[E.hh]={}}if(c.unique){var O={};f.uk=f.uk||[],f.uk.push(O),O.columns=[c.columnid],O.onrightfns=`r[${JSON.stringify(c.columnid)}]`,O.onrightfn=new Function("r","var y;return "+O.onrightfns),O.hh=ot(O.onrightfns),f.uniqs[O.hh]={}}if(c.foreignkey){var R=c.foreignkey.table,A=l.databases[R.databaseid||e].tables[R.tableid];if(typeof R.columnid>"u")if(A.pk.columns&&A.pk.columns.length>0)R.columnid=A.pk.columns[0];else throw new Error("FOREIGN KEY allowed only to tables with PRIMARY KEYs");var N=function(w){var T1={};if(typeof w[c.columnid]>"u")return!0;T1[R.columnid]=w[c.columnid];var v1=A.pk.onrightfn(T1);if(!A.uniqs[A.pk.hh][v1])throw new Error("Foreign key violation");return!0};f.checks.push({fn:N})}c.onupdate&&g.push(`r[${JSON.stringify(c.columnid)}]=`+c.onupdate.toJS("r","")),f.columns.push(v),f.xcolumns[v.columnid]=v}),f.defaultfns=h.join(","),f.onupdatefns=g.join(";"),u.forEach(function(c){var d;if(c.type==="PRIMARY KEY"){if(f.pk)throw new Error("Primary key already exists");var v=f.pk={};v.columns=c.columns,v.onrightfns=v.columns.map(function(A){return`r[${JSON.stringify(A)}]`}).join("+'`'+"),v.onrightfn=new Function("r","var y;return "+v.onrightfns),v.hh=ot(v.onrightfns),f.uniqs[v.hh]={}}else if(c.type==="CHECK")d=new Function("r","var y;return "+c.expression.toJS("r",""));else if(c.type==="UNIQUE"){var E={};f.uk=f.uk||[],f.uk.push(E),E.columns=c.columns,E.onrightfns=E.columns.map(function(A){return`r[${JSON.stringify(A)}]`}).join("+'`'+"),E.onrightfn=new Function("r","var y;return "+E.onrightfns),E.hh=ot(E.onrightfns),f.uniqs[E.hh]={}}else if(c.type==="FOREIGN KEY"){var O=c.fktable;c.fkcolumns&&c.fkcolumns.length>0&&(O.fkcolumns=c.fkcolumns);var R=l.databases[O.databaseid||e].tables[O.tableid];if(typeof O.fkcolumns>"u"&&(O.fkcolumns=R.pk.columns),O.columns=c.columns,O.fkcolumns.length>O.columns.length)throw new Error("Invalid foreign key on table "+f.tableid);d=function(A){var N={};if(O.fkcolumns.forEach(function(v1,O1){A[O.columns[O1]]!=null&&(N[v1]=A[O.columns[O1]])}),Object.keys(N).length===0)return!0;if(Object.keys(N).length!==O.columns.length)throw new Error("Invalid foreign key on table "+f.tableid);var w=l.databases[O.databaseid||e].tables[O.tableid],T1=w.pk.onrightfn(N);if(!w.uniqs[w.pk.hh][T1])throw new Error("Foreign key violation");return!0}}d&&f.checks.push({fn:d,id:c.constraintid,fk:c.type==="FOREIGN KEY"})}),this.view&&this.viewcolumns){var m=this;this.viewcolumns.forEach(function(c,d){m.select.columns[d].as=c.columnid})}if(this.view&&this.select&&(f.view=!0,f.select=this.select.compile(this.table.databaseid||e)),r.engineid)return l.engines[r.engineid].createTable(this.table.databaseid||e,i,this.ifnotexists,t);f.insert=function(c,d){var v=l.inserted;l.inserted=[c];var E=this,O=!1,R=!1;for(var A in E.beforeinsert){var N=E.beforeinsert[A];N&&(N.funcid?l.fn[N.funcid](c)===!1&&(R=R||!0):N.statement&&N.statement.execute(e)===!1&&(R=R||!0))}if(!R){var w=!1;for(A in E.insteadofinsert)w=!0,N=E.insteadofinsert[A],N&&(N.funcid?l.fn[N.funcid](c):N.statement&&N.statement.execute(e));if(!w){for(var T1 in E.identities){var v1=E.identities[T1];c[T1]=v1.value}if(E.checks&&E.checks.length>0&&E.checks.forEach(function(B1){if(!B1.fn(c))throw new Error("Violation of CHECK constraint "+(B1.id||""))}),E.columns.forEach(function(B1){if(B1.notnull&&typeof c[B1.columnid]>"u")throw new Error("Wrong NULL value in NOT NULL column "+B1.columnid)}),E.pk){var O1=E.pk,G1=O1.onrightfn(c);if(typeof E.uniqs[O1.hh][G1]<"u")if(d)O=E.uniqs[O1.hh][G1];else throw new Error("Cannot insert record, because it already exists in primary key index")}if(E.uk&&E.uk.length&&E.uk.forEach(function(B1){var Y1=B1.onrightfn(c);if(typeof E.uniqs[B1.hh][Y1]<"u")if(d)O=E.uniqs[B1.hh][Y1];else throw new Error("Cannot insert record, because it already exists in unique index")}),O)E.update(function(B1){for(var Y1 in c)B1[Y1]=c[Y1]},E.data.indexOf(O),n);else{E.data.push(c);for(var T1 in E.identities){var v1=E.identities[T1];v1.value+=v1.step}if(E.pk){var O1=E.pk,G1=O1.onrightfn(c);E.uniqs[O1.hh][G1]=c}E.uk&&E.uk.length&&E.uk.forEach(function(B1){var Y1=B1.onrightfn(c);E.uniqs[B1.hh][Y1]=c})}for(var A in E.afterinsert){var N=E.afterinsert[A];N&&(N.funcid?l.fn[N.funcid](c):N.statement&&N.statement.execute(e))}l.inserted=v}}},f.delete=function(c){var d=this,v=d.data[c],E=!1;for(var O in d.beforedelete){var R=d.beforedelete[O];R&&(R.funcid?l.fn[R.funcid](v)===!1&&(E=E||!0):R.statement&&R.statement.execute(e)===!1&&(E=E||!0))}if(E)return!1;var A=!1;for(var O in d.insteadofdelete){A=!0;var R=d.insteadofdelete[O];R&&(R.funcid?l.fn[R.funcid](v):R.statement&&R.statement.execute(e))}if(!A){if(this.pk){var N=this.pk,w=N.onrightfn(v);if(typeof this.uniqs[N.hh][w]>"u")throw new Error("Something wrong with primary key index on table");this.uniqs[N.hh][w]=void 0}d.uk&&d.uk.length&&d.uk.forEach(function(T1){var v1=T1.onrightfn(v);if(typeof d.uniqs[T1.hh][v1]>"u")throw new Error("Something wrong with unique index on table");d.uniqs[T1.hh][v1]=void 0})}},f.deleteall=function(){this.data.length=0,this.pk&&(this.uniqs[this.pk.hh]={}),f.uk&&f.uk.length&&f.uk.forEach(function(c){f.uniqs[c.hh]={}})},f.update=function(c,d,v){var E=Wt(this.data[d]),O;if(this.pk&&(O=this.pk,O.pkaddr=O.onrightfn(E,v),typeof this.uniqs[O.hh][O.pkaddr]>"u"))throw new Error("Something wrong with index on table");f.uk&&f.uk.length&&f.uk.forEach(function(T1){if(T1.ukaddr=T1.onrightfn(E),typeof f.uniqs[T1.hh][T1.ukaddr]>"u")throw new Error("Something wrong with unique index on table")}),c(E,v,l);var R=!1;for(var A in f.beforeupdate){var N=f.beforeupdate[A];N&&(N.funcid?l.fn[N.funcid](this.data[d],E)===!1&&(R=R||!0):N.statement&&N.statement.execute(e)===!1&&(R=R||!0))}if(R)return!1;var w=!1;for(var A in f.insteadofupdate){w=!0;var N=f.insteadofupdate[A];N&&(N.funcid?l.fn[N.funcid](this.data[d],E):N.statement&&N.statement.execute(e))}if(!w){if(f.checks&&f.checks.length>0&&f.checks.forEach(function(T1){if(!T1.fn(E))throw new Error("Violation of CHECK constraint "+(T1.id||""))}),f.columns.forEach(function(T1){if(T1.notnull&&typeof E[T1.columnid]>"u")throw new Error("Wrong NULL value in NOT NULL column "+T1.columnid)}),this.pk&&(O.newpkaddr=O.onrightfn(E),typeof this.uniqs[O.hh][O.newpkaddr]<"u"&&O.newpkaddr!==O.pkaddr))throw new Error("Record already exists");f.uk&&f.uk.length&&f.uk.forEach(function(T1){if(T1.newukaddr=T1.onrightfn(E),typeof f.uniqs[T1.hh][T1.newukaddr]<"u"&&T1.newukaddr!==T1.ukaddr)throw new Error("Record already exists")}),this.pk&&(this.uniqs[O.hh][O.pkaddr]=void 0,this.uniqs[O.hh][O.newpkaddr]=E),f.uk&&f.uk.length&&f.uk.forEach(function(T1){f.uniqs[T1.hh][T1.ukaddr]=void 0,f.uniqs[T1.hh][T1.newukaddr]=E}),this.data[d]=E;for(var A in f.afterupdate){var N=f.afterupdate[A];N&&(N.funcid?l.fn[N.funcid](this.data[d],E):N.statement&&N.statement.execute(e))}}};var b;return l.options.nocount||(b=1),t&&(b=t(b)),b},l.fn.Date=Object,l.fn.Date=Date,l.fn.Number=Number,l.fn.String=String,l.fn.Boolean=Boolean,I2.EXTEND=l.utils.extend,I2.CHAR=String.fromCharCode.bind(String),I2.ASCII=function(e){return e.charCodeAt(0)},I2.COALESCE=function(){for(var e=0;e<arguments.length;e++)if(arguments[e]!==null&&!(typeof arguments[e]>"u")&&!(typeof arguments[e]=="number"&&isNaN(arguments[e])))return arguments[e]},I2.USER=function(){return"alasql"},I2.OBJECT_ID=function(e){return!!l.tables[e]},I2.DATE=function(e){return!isNaN(e)&&e.length===8?new Date(+e.substr(0,4),+e.substr(4,2)-1,+e.substr(6,2)):Ze(e)},I2.NOW=function(){if(l.options.dateAsString){var e=new Date,n=e.getFullYear()+"-"+("0"+(e.getMonth()+1)).substr(-2)+"-"+("0"+e.getDate()).substr(-2);return n+=" "+("0"+e.getHours()).substr(-2)+":"+("0"+e.getMinutes()).substr(-2)+":"+("0"+e.getSeconds()).substr(-2),n+="."+("00"+e.getMilliseconds()).substr(-3),n}return new Date},I2.GETDATE=I2.NOW,I2.CURRENT_TIMESTAMP=I2.NOW,I2.CURDATE=I2.CURRENT_DATE=function(){var e=new Date;if(e.setHours(0,0,0,0),l.options.dateAsString){var n=e.getFullYear()+"-"+("0"+(e.getMonth()+1)).substr(-2)+"-"+("0"+e.getDate()).substr(-2);return n}return e},I2.SECOND=function(n){var n=Ze(n);return n.getSeconds()},I2.MINUTE=function(n){var n=Ze(n);return n.getMinutes()},I2.HOUR=function(n){var n=Ze(n);return n.getHours()},I2.DAYOFWEEK=I2.WEEKDAY=function(n){var n=Ze(n);return n.getDay()},I2.DAY=I2.DAYOFMONTH=function(n){var n=Ze(n);return n.getDate()},I2.MONTH=function(n){var n=Ze(n);return n.getMonth()+1},I2.YEAR=function(n){var n=Ze(n);return n.getFullYear()};var C4={year:1e3*3600*24*365,quarter:1e3*3600*24*365/4,month:1e3*3600*24*30,week:1e3*3600*24*7,day:1e3*3600*24,dayofyear:1e3*3600*24,weekday:1e3*3600*24,hour:1e3*3600,minute:1e3*60,second:1e3,millisecond:1,microsecond:.001};l.stdfn.DATEDIFF=function(e,n,t){var r=Ze(t).getTime()-Ze(n).getTime();return r/C4[e.toLowerCase()]|0},l.stdfn.DATEADD=function(i,n,t){var r=Ze(t),i=i.toLowerCase();switch(i){case"year":r.setFullYear(r.getFullYear()+n);break;case"quarter":r.setMonth(r.getMonth()+n*3);break;case"month":r.setMonth(r.getMonth()+n);break;default:r=new Date(r.getTime()+n*C4[i]);break}return r},l.stdfn.INTERVAL=function(e,n){return e*C4[n.toLowerCase()]},l.stdfn.DATE_ADD=l.stdfn.ADDDATE=function(e,n){var t=Ze(e).getTime()+n;return new Date(t)},l.stdfn.DATE_SUB=l.stdfn.SUBDATE=function(e,n){var t=Ze(e).getTime()-n;return new Date(t)};var ki=/^\d{4}\.\d{2}\.\d{2} \d{2}:\d{2}:\d{2}/;function Ze(e){return typeof e=="string"&&ki.test(e)&&(e=e.replace(".","-").replace(".","-")),new Date(e)}T.DropTable=function(e){return Object.assign(this,e)},T.DropTable.prototype.toString=function(){var e="DROP ";return this.view?e+="VIEW":e+="TABLE",this.ifexists&&(e+=" IF EXISTS"),e+=" "+this.tables.toString(),e},T.DropTable.prototype.execute=function(e,n,t){var r=this.ifexists,i=0,s=0,u=this.tables.length;return this.tables.forEach(function(f){var h=l.databases[f.databaseid||e],g=f.tableid;if(!r||r&&h.tables[g]){if(h.tables[g])h.engineid?l.engines[h.engineid].dropTable(f.databaseid||e,g,r,function(m){delete h.tables[g],i+=m,s++,s==u&&t&&t(i)}):(delete h.tables[g],i++,s++,s==u&&t&&t(i));else if(!l.options.dropifnotexists)throw new Error(`Can not drop table ${JSON.stringify(f.tableid)} because it does not exist in the database.`)}else s++,s==u&&t&&t(i)}),i},T.TruncateTable=function(e){return Object.assign(this,e)},T.TruncateTable.prototype.toString=function(){var e="TRUNCATE TABLE";return e+=" "+this.table.toString(),e},T.TruncateTable.prototype.execute=function(e,n,t){var r=l.databases[this.table.databaseid||e],i=this.table.tableid;if(r.engineid)return l.engines[r.engineid].truncateTable(this.table.databaseid||e,i,this.ifexists,t);if(r.tables[i])r.tables[i].data=[];else throw new Error("Cannot truncate table becaues it does not exist");return t?t(0):0},T.CreateVertex=function(e){return Object.assign(this,e)},T.CreateVertex.prototype.toString=function(){var e="CREATE VERTEX ";return this.class&&(e+=this.class+" "),this.sharp&&(e+="#"+this.sharp+" "),this.sets?e+=this.sets.toString():this.content?e+=this.content.toString():this.select&&(e+=this.select.toString()),e},T.CreateVertex.prototype.toJS=function(e){var n="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return n},T.CreateVertex.prototype.compile=function(e){var n=e,t=this.sharp;if(typeof this.name<"u")var r="x.name="+this.name.toJS(),i=new Function("x",r);if(this.sets&&this.sets.length>0)var r=this.sets.map(function(h){return`x[${JSON.stringify(h.column.columnid)}]=`+h.expression.toJS("x","")}).join(";"),s=new Function("x,params,alasql",r);var u=function(f,h){var g,m=l.databases[n],b;typeof t<"u"?b=t:b=m.counter++;var c={$id:b,$node:"VERTEX"};return m.objects[c.$id]=c,g=c,i&&i(c),s&&s(c,f,l),h&&(g=h(g)),g};return u},T.CreateEdge=function(e){return Object.assign(this,e)},T.CreateEdge.prototype.toString=function(){var e="CREATE EDGE ";return this.class&&(e+=this.class+" "),e},T.CreateEdge.prototype.toJS=function(e){var n="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return n},T.CreateEdge.prototype.compile=function(e){var n=e,t=new Function("params,alasql","var y;return "+this.from.toJS()),r=new Function("params,alasql","var y;return "+this.to.toJS());if(typeof this.name<"u")var i="x.name="+this.name.toJS(),s=new Function("x",i);if(this.sets&&this.sets.length>0)var i=this.sets.map(function(g){return`x[${JSON.stringify(g.column.columnid)}]=`+g.expression.toJS("x","")}).join(";"),u=new Function("x,params,alasql","var y;"+i);return(h,g)=>{let m=0,b=l.databases[n],c={$id:b.counter++,$node:"EDGE"},d=t(h,l),v=r(h,l);return c.$in=[d.$id],c.$out=[v.$id],d.$out=d.$out||[],d.$out.push(c.$id),v.$in=v.$in||[],v.$in.push(c.$id),b.objects[c.$id]=c,m=c,s?.(c),u?.(c,h,l),g?g(m):m}},T.CreateGraph=function(e){return Object.assign(this,e)},T.CreateGraph.prototype.toString=function(){var e="CREATE GRAPH ";return this.class&&(e+=this.class+" "),e},T.CreateGraph.prototype.execute=function(e,n,t){var r=[];return this.from&&l.from[this.from.funcid]&&(this.graph=l.from[this.from.funcid.toUpperCase()]),this.graph.forEach(u=>{if(!u.source)s(u);else{let f={};u.as!==void 0&&(l.vars[u.as]=f),u.prop!==void 0&&(f.name=u.prop),u.sharp!==void 0&&(f.$id=u.sharp),u.name!==void 0&&(f.name=u.name),u.class!==void 0&&(f.$class=u.class);let h=l.databases[e];f.$id=f.$id!==void 0?f.$id:h.counter++,f.$node="EDGE",u.json!==void 0&&Object.assign(f,new Function("params, alasql",`return ${u.json.toJS()}`)(n,l));const g=(c,d)=>{let v,E;if(c.vars)E=l.vars[c.vars],v=typeof E=="object"?E:h.objects[E];else{let O=c.sharp||c.prop;v=h.objects[O],v===void 0&&l.options.autovertex&&(c.prop||c.name)&&(v=i(c.prop||c.name)||s(c))}return d&&v&&typeof v.$out>"u"&&(v.$out=[]),!d&&v&&typeof v.$in>"u"&&(v.$in=[]),v};let m=g(u.source,!0),b=g(u.target,!1);if(f.$in=[m.$id],f.$out=[b.$id],m.$out.push(f.$id),b.$in.push(f.$id),h.objects[f.$id]=f,f.$class!==void 0){let c=l.databases[e].tables[f.$class];if(c===void 0)throw new Error("No such class. Please use CREATE CLASS");c.data.push(f)}r.push(f.$id)}}),t&&(r=t(r)),r;function i(u){var f=l.databases[l.useid].objects;for(var h in f)if(f[h].name===u)return f[h]}function s(u){var f={};typeof u.as<"u"&&(l.vars[u.as]=f),typeof u.prop<"u"&&(f.$id=u.prop,f.name=u.prop),typeof u.sharp<"u"&&(f.$id=u.sharp),typeof u.name<"u"&&(f.name=u.name),typeof u.class<"u"&&(f.$class=u.class);var h=l.databases[e];if(typeof f.$id>"u"&&(f.$id=h.counter++),f.$node="VERTEX",typeof u.json<"u"&&V2(f,new Function("params,alasql","var y;return "+u.json.toJS())(n,l)),h.objects[f.$id]=f,typeof f.$class<"u"){if(typeof l.databases[e].tables[f.$class]>"u")throw new Error("No such class. Pleace use CREATE CLASS");l.databases[e].tables[f.$class].data.push(f)}return r.push(f.$id),f}},T.CreateGraph.prototype.compile1=function(e){const n=e,t=new Function("params, alasql",`return ${this.from.toJS()}`),r=new Function("params, alasql",`return ${this.to.toJS()}`);let i,s;if(this.name!==void 0){const f=`x.name = ${this.name.toJS()}`;i=new Function("x",f)}if(this.sets&&this.sets.length>0){const f=this.sets.map(h=>`x[${JSON.stringify(h.column.columnid)}] = ${h.expression.toJS("x","")}`).join(";");s=new Function("x, params, alasql",`var y; ${f}`)}return(f,h)=>{let g=0;const m=l.databases[n],b={$id:m.counter++,$node:"EDGE"},c=t(f,l),d=r(f,l);return b.$in=[c.$id],b.$out=[d.$id],c.$out=c.$out||[],c.$out.push(b.$id),d.$in=d.$in||[],d.$in.push(b.$id),m.objects[b.$id]=b,g=b,i&&i(b),s&&s(b,f,l),h&&(g=h(g)),g}},T.AlterTable=function(e){return Object.assign(this,e)},T.AlterTable.prototype.toString=function(){let e="ALTER TABLE "+this.table.toString();return this.renameto&&(e+=" RENAME TO "+this.renameto),e},T.AlterTable.prototype.execute=function(e,n,t){let r=l.databases[e];if(r.dbversion=Date.now(),this.renameto){var i=this.table.tableid,s=this.renameto,u=1;if(r.tables[s])throw new Error(`Can not rename a table "${i}" to "${s}" because the table with this name already exists`);if(s===i)throw new Error(`Can not rename a table "${i}" to itself`);return r.tables[s]=r.tables[i],delete r.tables[i],u=1,t&&t(u),u}if(this.addcolumn){r=l.databases[this.table.databaseid||e],r.dbversion++;var f=this.table.tableid,h=r.tables[f],g=this.addcolumn.columnid;if(h.xcolumns[g])throw new Error(`Cannot add column "${g}" because it already exists in table "${f}"`);var m={columnid:g,dbtypeid:this.addcolumn.dbtypeid,dbsize:this.dbsize,dbprecision:this.dbprecision,dbenum:this.dbenum,defaultfns:null},b=function(){};h.columns.push(m),h.xcolumns[g]=m;for(let R=0,A=h.data.length;R<A;R++)h.data[R][g]=b();return t?t(1):1}if(this.modifycolumn){let R=l.databases[this.table.databaseid||e];R.dbversion++;var f=this.table.tableid,h=R.tables[f],g=this.modifycolumn.columnid;if(!h.xcolumns[g])throw new Error(`Cannot modify column "${g}" because it was not found in table "${f}"`);return m=h.xcolumns[g],m.dbtypeid=this.dbtypeid,m.dbsize=this.dbsize,m.dbprecision=this.dbprecision,m.dbenum=this.dbenum,t?t(1):1}if(this.renamecolumn){let R=l.databases[this.table.databaseid||e];R.dbversion++;var f=this.table.tableid,h=R.tables[f],g=this.renamecolumn,c=this.to,m;if(!h.xcolumns[g])throw new Error('Column "'+g+'" is not found in the table "'+f+'"');if(h.xcolumns[c])throw new Error('Column "'+c+'" already exists in the table "'+f+'"');if(g!=c){for(var d=0;d<h.columns.length;d++)h.columns[d].columnid==g&&(h.columns[d].columnid=c);h.xcolumns[c]=h.xcolumns[g],delete h.xcolumns[g];for(var v=0,E=h.data.length;v<E;v++)h.data[v][c]=h.data[v][g],delete h.data[v][g];return h.data.length}return t?t(0):0}if(this.dropcolumn){let R=l.databases[this.table.databaseid||e];R.dbversion++;for(var f=this.table.tableid,h=R.tables[f],g=this.dropcolumn,O=!1,d=0;d<h.columns.length;d++)if(h.columns[d].columnid==g){O=!0,h.columns.splice(d,1);break}if(!O)throw new Error(`Cannot drop column "${g}" because it was not found in the table ${f}"`);for(delete h.xcolumns[g],v=0,E=h.data.length;v<E;v++)delete h.data[v][g];return t?t(h.data.length):h.data.length}throw Error("Unknown ALTER TABLE method")},T.CreateIndex=function(e){return Object.assign(this,e)},T.CreateIndex.prototype.toString=function(){var e="CREATE";return this.unique&&(e+=" UNIQUE"),e+=" INDEX "+this.indexid+" ON "+this.table.toString(),e+="("+this.columns.toString()+")",e},T.CreateIndex.prototype.execute=function(e,n,t){var r=l.databases[e],i=this.table.tableid,s=r.tables[i],u=this.indexid;r.indices[u]=i;var f=this.columns.map(function(O){return O.expression.toJS("r","")}).join("+'`'+"),h=new Function("r,params,alasql","return "+f);if(this.unique){s.uniqdefs[u]={rightfns:f};var g=s.uniqs[u]={};if(s.data.length>0)for(var m=0,b=s.data.length;m<b;m++){var c=f(s.data[m]);g[c]||(g[c]={num:0}),g[c].num++}}else{var d=ot(f);s.inddefs[u]={rightfns:f,hh:d},s.indices[d]={};var v=s.indices[d]={};if(s.data.length>0)for(var m=0,b=s.data.length;m<b;m++){var c=h(s.data[m],n,l);v[c]||(v[c]=[]),v[c].push(s.data[m])}}var E=1;return t&&(E=t(E)),E},T.Reindex=function(e){return Object.assign(this,e)},T.Reindex.prototype.toString=function(){var e="REINDEX "+this.indexid;return e},T.Reindex.prototype.execute=function(e,n,t){var r=l.databases[e],i=this.indexid,s=r.indices[i],u=r.tables[s];u.indexColumns();var f=1;return t&&(f=t(f)),f},T.DropIndex=function(e){return Object.assign(this,e)},T.DropIndex.prototype.toString=function(){return"DROP INDEX"+this.indexid},T.DropIndex.prototype.compile=function(e){var n=this.indexid;return function(){return 1}},T.WithSelect=function(e){return Object.assign(this,e)},T.WithSelect.prototype.toString=function(){var e="WITH ";return e+=this.withs.map(function(n){return n.name+" AS ("+n.select.toString()+")"}).join(",")+" ",e+=this.select.toString(),e},T.WithSelect.prototype.execute=function(e,n,t){var r=this,i=[];r.withs.forEach(function(u){i.push(l.databases[e].tables[u.name]);var f=l.databases[e].tables[u.name]=new pr({tableid:u.name});f.data=u.select.execute(e,n)});var s=1;return s=this.select.execute(e,n,function(u){return r.withs.forEach(function(f,h){i[h]?l.databases[e].tables[f.name]=i[h]:delete l.databases[e].tables[f.name]}),t&&(u=t(u)),u}),s},T.If=function(e){return Object.assign(this,e)},T.If.prototype.toString=function(){var e="IF ";return e+=this.expression.toString(),e+=" "+this.thenstat.toString(),this.elsestat&&(e+=" ELSE "+this.thenstat.toString()),e},T.If.prototype.execute=function(e,n,t){var r,i=new Function("params,alasql,p","var y;return "+this.expression.toJS("({})","",null)).bind(this);return i(n,l)?r=this.thenstat.execute(e,n,t):this.elsestat?r=this.elsestat.execute(e,n,t):t&&(r=t(r)),r},T.While=function(e){return Object.assign(this,e)},T.While.prototype.toString=function(){var e="WHILE ";return e+=this.expression.toString(),e+=" "+this.loopstat.toString(),e},T.While.prototype.execute=function(e,n,t){var r=this,i=[],s=new Function("params,alasql,p","var y;return "+this.expression.toJS());if(t){var u=!1,f=function(g){u?i.push(g):u=!0,setTimeout(function(){s(n,l)?r.loopstat.execute(e,n,f):i=t(i)},0)};f()}else for(;s(n,l);){var h=r.loopstat.execute(e,n);i.push(h)}return i},T.Break=function(e){return Object.assign(this,e)},T.Break.prototype.toString=function(){var e="BREAK";return e},T.Break.prototype.execute=function(e,n,t,r){var i=1;return t&&(i=t(i)),i},T.Continue=function(e){return Object.assign(this,e)},T.Continue.prototype.toString=function(){var e="CONTINUE";return e},T.Continue.prototype.execute=function(e,n,t,r){var i=1;return t&&(i=t(i)),i},T.BeginEnd=function(e){return Object.assign(this,e)},T.BeginEnd.prototype.toString=function(){var e="BEGIN "+this.statements.toString()+" END";return e},T.BeginEnd.prototype.execute=function(e,n,t,r){var i=this,s=[],u=0;f();function f(){i.statements[u].execute(e,n,function(h){if(s.push(h),u++,u<i.statements.length)return f();t&&(s=t(s))})}return s},T.Insert=function(e){return Object.assign(this,e)},T.Insert.prototype.toString=function(){var e="INSERT ";if(this.orreplace&&(e+="OR REPLACE "),this.replaceonly&&(e="REPLACE "),e+="INTO "+this.into.toString(),this.columns&&(e+="("+this.columns.toString()+")"),this.values){var n=this.values.map(function(t){return"("+t.toString()+")"});e+=" VALUES "+n.join(",")}return this.select&&(e+=" "+this.select.toString()),e},T.Insert.prototype.toJS=function(e,n,t){var r="this.queriesfn["+(this.queriesidx-1)+"](this.params,null,"+e+")";return r},T.Insert.prototype.compile=function(e){var n=this;e=n.into.databaseid||e;var t=l.databases[e],r=n.into.tableid,i=t.tables[r];if(!i)throw"Table '"+r+"' could not be found";var u="",s="",u="db.tables['"+r+"'].dirty=true;",f="var a,aa=[],x;",h;if(this.values){this.exists&&(this.existsfn=this.exists.map(function(E){var O=E.compile(e);return O.query.modifier="RECORDSET",O})),this.queries&&(this.queriesfn=this.queries.map(function(E){var O=E.compile(e);return O.query.modifier="RECORDSET",O})),n.values.forEach(function(E){var O=[];n.columns?n.columns.forEach(function(R,A){var N="'"+R.columnid+"':";i.xcolumns&&i.xcolumns[R.columnid]?["INT","FLOAT","NUMBER","MONEY"].indexOf(i.xcolumns[R.columnid].dbtypeid)>=0?N+="(x="+E[A].toJS()+",x==undefined?undefined:+x)":l.fn[i.xcolumns[R.columnid].dbtypeid]?(N+="(new "+i.xcolumns[R.columnid].dbtypeid+"(",N+=E[A].toJS(),N+="))"):N+=E[A].toJS():N+=E[A].toJS(),O.push(N)}):Array.isArray(E)&&i.columns&&i.columns.length>0?i.columns.forEach(function(R,A){var N="'"+R.columnid+"':";["INT","FLOAT","NUMBER","MONEY"].indexOf(R.dbtypeid)>=0?N+="+"+E[A].toJS():l.fn[R.dbtypeid]?(N+="(new "+R.dbtypeid+"(",N+=E[A].toJS(),N+="))"):N+=E[A].toJS(),O.push(N)}):s=B3(E),t.tables[r].defaultfns&&O.unshift(t.tables[r].defaultfns),s?u+="a="+s+";":u+="a={"+O.join(",")+"};",t.tables[r].isclass&&(u+="var db=alasql.databases['"+e+"'];",u+='a.$class="'+r+'";',u+="a.$id=db.counter++;",u+="db.objects[a.$id]=a;"),t.tables[r].insert?(u+="var db=alasql.databases['"+e+"'];",u+="db.tables['"+r+"'].insert(a,"+(n.orreplace?"true":"false")+");"):u+="aa.push(a);"}),h=f+u,t.tables[r].insert||(u+="alasql.databases['"+e+"'].tables['"+r+"'].data=alasql.databases['"+e+"'].tables['"+r+"'].data.concat(aa);"),t.tables[r].insert&&t.tables[r].isclass?u+="return a.$id;":u+="return "+n.values.length;var g=new Function("db, params, alasql","var y;"+f+u).bind(this)}else if(this.select){this.select.modifier="RECORDSET",this.queries&&(this.select.queries=this.queries);var m=this.select.compile(e);if(t.engineid&&l.engines[t.engineid].intoTable){var b=function(E,O){var R=m(E),A=l.engines[t.engineid].intoTable(t.databaseid,r,R.data,null,O);return A};return b}else var c="return alasql.utils.extend(r,{"+i.defaultfns+"})",d=new Function("r,db,params,alasql",c),g=function(O,R,A){var N=m(R).data;if(O.tables[r].insert)for(var w=0,T1=N.length;w<T1;w++){var v1=Wt(N[w]);d(v1,O,R,A),O.tables[r].insert(v1,n.orreplace)}else O.tables[r].data=O.tables[r].data.concat(N);if(!A.options.nocount)return N.length}}else if(this.default)var v="db.tables['"+r+"'].data.push({"+i.defaultfns+"});return 1;",g=new Function("db,params,alasql",v);else throw new Error("Wrong INSERT parameters");if(t.engineid&&l.engines[t.engineid].intoTable&&l.options.autocommit)var b=function(O,R){var A=new Function("db,params","var y;"+h+"return aa;")(t,O),N=l.engines[t.engineid].intoTable(t.databaseid,r,A,null,R);return N};else var b=function(O,R){var A=l.databases[e];l.options.autocommit&&A.engineid&&l.engines[A.engineid].loadTableData(e,r);var N=g(A,O,l);return l.options.autocommit&&A.engineid&&l.engines[A.engineid].saveTableData(e,r),l.options.nocount&&(N=void 0),R&&R(N),N};return b},T.Insert.prototype.execute=function(e,n,t){return this.compile(e)(n,t)},T.CreateTrigger=function(e){return Object.assign(this,e)},T.CreateTrigger.prototype.toString=function(){var e="CREATE TRIGGER "+this.trigger+" ";return this.when&&(e+=this.when+" "),e+=this.action+" ON ",this.table.databaseid&&(e+=this.table.databaseid+"."),e+=this.table.tableid+" ",e+=this.statement.toString(),e};const vr=["beforeinsert","afterinsert","insteadofinsert","beforedelete","afterdelete","insteadofdelete","beforeupdate","afterupdate","insteadofupdate"];T.CreateTrigger.prototype.execute=function(e,n,t){let r=1;const i=this.trigger;e=this.table.databaseid||e;const s=l.databases[e],{tableid:u}=this.table,f={action:this.action,when:this.when,statement:this.statement,funcid:this.funcid,tableid:u};s.triggers[i]=f;const h=`${this.when}${this.action}`.toLowerCase();return vr.includes(h)&&(s.tables[u]=s.tables[u]||{},s.tables[u][h]=s.tables[u][h]||{},s.tables[u][h][i]=f),t&&(r=t(r)),r},T.DropTrigger=function(e){return Object.assign(this,e)},T.DropTrigger.prototype.toString=function(){var e="DROP TRIGGER "+this.trigger;return e},T.DropTrigger.prototype.execute=function(e,n,t){let r=0;const i=l.databases[e],s=this.trigger,u=i.triggers[s];if(u){const{tableid:f}=u;if(f)r=1,vr.forEach(h=>{delete i.tables[f][h][s]}),delete i.triggers[s];else throw new Error("Trigger Table not found")}else throw new Error("Trigger not found");return t&&(r=t(r)),r},T.Delete=function(e){return Object.assign(this,e)},T.Delete.prototype.toString=function(){var e="DELETE FROM "+this.table.toString();return this.where&&(e+=" WHERE "+this.where.toString()),e},T.Delete.prototype.compile=function(e){e=this.table.databaseid||e;var n=this.table.tableid,t,r=l.databases[e];if(this.where){this.exists&&(this.existsfn=this.exists.map(function(s){var u=s.compile(e);return u.query.modifier="RECORDSET",u})),this.queries&&(this.queriesfn=this.queries.map(function(s){var u=s.compile(e);return u.query.modifier="RECORDSET",u}));var i=new Function("r,params,alasql","var y;return ("+this.where.toJS("r","")+")").bind(this);t=function(s,u){if(r.engineid&&l.engines[r.engineid].deleteFromTable)return l.engines[r.engineid].deleteFromTable(e,n,i,s,u);l.options.autocommit&&r.engineid&&(r.engineid=="LOCALSTORAGE"||r.engineid=="FILESTORAGE")&&l.engines[r.engineid].loadTableData(e,n);for(var f=r.tables[n],h=f.data.length,g=[],m=0,b=f.data.length;m<b;m++)i(f.data[m],s,l)?f.delete&&f.delete(m,s,l):g.push(f.data[m]);f.data=g;for(var c in f.afterdelete){var d=f.afterdelete[c];d&&(d.funcid?l.fn[d.funcid]():d.statement&&d.statement.execute(e))}var v=h-f.data.length;return l.options.autocommit&&r.engineid&&(r.engineid=="LOCALSTORAGE"||r.engineid=="FILESTORAGE")&&l.engines[r.engineid].saveTableData(e,n),u&&(v=u(v)),v}}else t=function(s,u){l.options.autocommit&&r.engineid&&l.engines[r.engineid].loadTableData(e,n);var f=r.tables[n];f.dirty=!0;var h=r.tables[n].data.length;r.tables[n].data.length=0;for(var g in r.tables[n].uniqs)r.tables[n].uniqs[g]={};for(var g in r.tables[n].indices)r.tables[n].indices[g]={};return l.options.autocommit&&r.engineid&&l.engines[r.engineid].saveTableData(e,n),u&&u(h),h};return t},T.Delete.prototype.execute=function(e,n,t){return this.compile(e)(n,t)},T.Update=function(e){return Object.assign(this,e)},T.Update.prototype.toString=function(){var e="UPDATE "+this.table.toString();return this.columns&&(e+=" SET "+this.columns.toString()),this.where&&(e+=" WHERE "+this.where.toString()),e},T.SetColumn=function(e){return Object.assign(this,e)},T.SetColumn.prototype.toString=function(){return this.column.toString()+"="+this.expression.toString()},T.Update.prototype.compile=function(e){e=this.table.databaseid||e;var n=this.table.tableid;if(this.where){this.exists&&(this.existsfn=this.exists.map(function(u){var f=u.compile(e);return f.query.modifier="RECORDSET",f})),this.queries&&(this.queriesfn=this.queries.map(function(u){var f=u.compile(e);return f.query.modifier="RECORDSET",f}));var t=new Function("r,params,alasql","var y;return "+this.where.toJS("r","")).bind(this)}var r=l.databases[e].tables[n].onupdatefns||"";r+=";",this.columns.forEach(function(u){r+="r['"+u.column.columnid+"']="+u.expression.toJS("r","")+";"});var i=new Function("r,params,alasql","var y;"+r),s=function(u,f){var h=l.databases[e];if(h.engineid&&l.engines[h.engineid].updateTable)return l.engines[h.engineid].updateTable(e,n,i,t,u,f);l.options.autocommit&&h.engineid&&l.engines[h.engineid].loadTableData(e,n);var g=h.tables[n];if(!g)throw new Error("Table '"+n+"' not exists");for(var m=0,b=0,c=g.data.length;b<c;b++)(!t||t(g.data[b],u,l))&&(g.update?g.update(i,b,u):i(g.data[b],u,l),m++);return l.options.autocommit&&h.engineid&&l.engines[h.engineid].saveTableData(e,n),f&&f(m),m};return s},T.Update.prototype.execute=function(e,n,t){return this.compile(e)(n,t)},T.Merge=function(e){return Object.assign(this,e)},T.Merge.prototype.toString=function(){let e=`MERGE ${this.into.tableid} `;return this.into.as&&(e+=`AS ${this.into.as} `),e+=`USING ${this.using.tableid} `,this.using.as&&(e+=`AS ${this.using.as} `),e+=`ON ${this.on.toString()} `,this.matches.forEach(n=>{e+="WHEN ",n.matched||(e+="NOT "),e+="MATCHED ",n.bytarget&&(e+="BY TARGET "),n.bysource&&(e+="BY SOURCE "),n.expr&&(e+=`AND ${n.expr.toString()} `),e+="THEN ",n.action.delete&&(e+="DELETE "),n.action.insert&&(e+="INSERT ",n.action.columns&&(e+=`(${n.action.columns.toString()}) `),n.action.values&&(e+=`VALUES (${n.action.values.toString()}) `),n.action.defaultvalues&&(e+="DEFAULT VALUES ")),n.action.update&&(e+="UPDATE ",e+=n.action.update.map(t=>t.toString()).join(", ")+" ")}),e},T.Merge.prototype.execute=function(e,n,t){var r=1;return t&&(r=t(r)),r},T.CreateDatabase=function(e){return Object.assign(this,e)},T.CreateDatabase.prototype.toString=function(){let e="CREATE ";return this.engineid&&(e+=`${this.engineid} `),e+="DATABASE ",this.ifnotexists&&(e+="IF NOT EXISTS "),e+=`${this.databaseid} `,this.args&&this.args.length>0&&(e+=`(${this.args.map(n=>n.toString()).join(", ")}) `),this.as&&(e+=`AS ${this.as}`),e},T.CreateDatabase.prototype.execute=function(e,n,t){var r;if(this.args&&this.args.length>0&&(r=this.args.map(function(f){return new Function("params,alasql","var y;return "+f.toJS())(n,l)})),this.engineid){var i=l.engines[this.engineid].createDatabase(this.databaseid,this.args,this.ifnotexists,this.as,t);return i}else{var s=this.databaseid;if(l.databases[s])throw new Error("Database '"+s+"' already exists");var u=new l.Database(s),i=1;return t?t(i):i}},T.AttachDatabase=function(e){return Object.assign(this,e)},T.AttachDatabase.prototype.toString=function(e){let n="ATTACH";return this.engineid&&(n+=` ${this.engineid}`),n+=` DATABASE ${this.databaseid}`,e&&(n+="(",e.length>0&&(n+=e.map(t=>t.toString()).join(", ")),n+=")"),this.as&&(n+=` AS ${this.as}`),n},T.AttachDatabase.prototype.execute=function(e,n,t){if(!l.engines[this.engineid])throw new Error('Engine "'+this.engineid+'" is not defined.');var r=l.engines[this.engineid].attachDatabase(this.databaseid,this.as,this.args,n,t);return r},T.DetachDatabase=function(e){return Object.assign(this,e)},T.DetachDatabase.prototype.toString=function(){var e="DETACH";return e+=" DATABASE "+this.databaseid,e},T.DetachDatabase.prototype.execute=function(e,n,t){if(!l.databases[this.databaseid].engineid)throw new Error('Cannot detach database "'+this.engineid+'", because it was not attached.');var r,i=this.databaseid;if(i===l.DEFAULTDATABASEID)throw new Error("Drop of default database is prohibited");if(l.databases[i]){var s=l.databases[i].engineid&&l.databases[i].engineid=="FILESTORAGE",u=l.databases[i].filename||"";delete l.databases[i],s&&(l.databases[i]={},l.databases[i].isDetached=!0,l.databases[i].filename=u),i===l.useid&&l.use(),r=1}else if(this.ifexists)r=0;else throw new Error("Database '"+i+"' does not exist");return t&&t(r),r},T.UseDatabase=function(e){return Object.assign(this,e)},T.UseDatabase.prototype.toString=function(){return"USE DATABASE "+this.databaseid},T.UseDatabase.prototype.execute=function(e,n,t){var r=this.databaseid;if(!l.databases[r])throw new Error("Database '"+r+"' does not exist");l.use(r);var i=1;return t&&t(i),i},T.DropDatabase=function(e){return Object.assign(this,e)},T.DropDatabase.prototype.toString=function(){var e="DROP";return this.ifexists&&(e+=" IF EXISTS"),e+=" DATABASE "+this.databaseid,e},T.DropDatabase.prototype.execute=function(e,n,t){if(this.engineid)return l.engines[this.engineid].dropDatabase(this.databaseid,this.ifexists,t);let r;const i=this.databaseid;if(i===l.DEFAULTDATABASEID)throw new Error("Drop of default database is prohibited");if(l.databases[i]){if(l.databases[i].engineid)throw new Error(`Cannot drop database '${i}', because it is attached. Detach it.`);delete l.databases[i],i===l.useid&&l.use(),r=1}else if(this.ifexists)r=0;else throw new Error(`Database '${i}' does not exist`);return t&&t(r),r},T.Declare=function(e){return Object.assign(this,e)},T.Declare.prototype.toString=function(){let e="DECLARE ";return this.declares&&this.declares.length>0&&(e+=this.declares.map(n=>{let t=`@${n.variable} ${n.dbtypeid}`;return n.dbsize&&(t+=`(${n.dbsize}`,n.dbprecision&&(t+=`,${n.dbprecision}`),t+=")"),n.expression&&(t+=` = ${n.expression.toString()}`),t}).join(",")),e},T.Declare.prototype.execute=function(e,n,t){var r=1,i=this;return i.declares&&i.declares.length>0&&i.declares.forEach(function(s){var u=s.dbtypeid;l.fn[u]||(u=u.toUpperCase()),l.declares[s.variable]={dbtypeid:u,dbsize:s.dbsize,dbprecision:s.dbprecision},s.expression&&(l.vars[s.variable]=new Function("params,alasql","return "+s.expression.toJS("({})","",null)).bind(i)(n,l),l.declares[s.variable]&&(l.vars[s.variable]=l.stdfn.CONVERT(l.vars[s.variable],l.declares[s.variable])))}),t&&(r=t(r)),r},T.ShowDatabases=function(e){return Object.assign(this,e)},T.ShowDatabases.prototype.toString=function(){var e="SHOW DATABASES";return this.like&&(e+="LIKE "+this.like.toString()),e},T.ShowDatabases.prototype.execute=function(e,n,t){if(this.engineid)return l.engines[this.engineid].showDatabases(this.like,t);var r=this,i=[];for(var s in l.databases)i.push({databaseid:s});return r.like&&i&&i.length>0&&(i=i.filter(function(u){return l.utils.like(r.like.value,u.databaseid)})),t&&t(i),i},T.ShowTables=function(e){return Object.assign(this,e)},T.ShowTables.prototype.toString=function(){var e="SHOW TABLES";return this.databaseid&&(e+=" FROM "+this.databaseid),this.like&&(e+=" LIKE "+this.like.toString()),e},T.ShowTables.prototype.execute=function(e,n,t){var r=l.databases[this.databaseid||e],i=this,s=[];for(var u in r.tables)s.push({tableid:u});return i.like&&s&&s.length>0&&(s=s.filter(function(f){return l.utils.like(i.like.value,f.tableid)})),t&&t(s),s},T.ShowColumns=function(e){return Object.assign(this,e)},T.ShowColumns.prototype.toString=function(){var e="SHOW COLUMNS";return this.table.tableid&&(e+=" FROM "+this.table.tableid),this.databaseid&&(e+=" FROM "+this.databaseid),e},T.ShowColumns.prototype.execute=function(e,n,t){var r=l.databases[this.databaseid||e],i=r.tables[this.table.tableid];if(i&&i.columns){var s=i.columns.map(function(u){return{columnid:u.columnid,dbtypeid:u.dbtypeid,dbsize:u.dbsize}});return t&&t(s),s}else return t&&t([]),[]},T.ShowIndex=function(e){return Object.assign(this,e)},T.ShowIndex.prototype.toString=function(){var e="SHOW INDEX";return this.table.tableid&&(e+=" FROM "+this.table.tableid),this.databaseid&&(e+=" FROM "+this.databaseid),e},T.ShowIndex.prototype.execute=function(e,n,t){var r=l.databases[this.databaseid||e],i=r.tables[this.table.tableid],s=[];if(i&&i.indices)for(var u in i.indices)s.push({hh:u,len:Object.keys(i.indices[u]).length});return t&&t(s),s},T.ShowCreateTable=function(e){return Object.assign(this,e)},T.ShowCreateTable.prototype.toString=function(){var e="SHOW CREATE TABLE "+this.table.tableid;return this.databaseid&&(e+=" FROM "+this.databaseid),e},T.ShowCreateTable.prototype.execute=function(e){var n=l.databases[this.databaseid||e],t=n.tables[this.table.tableid];if(t){var r="CREATE TABLE "+this.table.tableid+" (",i=[];return t.columns&&(t.columns.forEach(function(s){var u=s.columnid+" "+s.dbtypeid;s.dbsize&&(u+="("+s.dbsize+")"),s.primarykey&&(u+=" PRIMARY KEY"),i.push(u)}),r+=i.join(", ")),r+=")",r}else throw new Error('There is no such table "'+this.table.tableid+'"')},T.SetVariable=function(e){return Object.assign(this,e)},T.SetVariable.prototype.toString=function(){var e="SET ";return typeof this.value<"u"&&(e+=this.variable.toUpperCase()+" "+(this.value?"ON":"OFF")),this.expression&&(e+=this.method+this.variable+" = "+this.expression.toString()),e},T.SetVariable.prototype.execute=function(e,n,t){if(typeof this.value<"u"){let i=this.value;i==="ON"?i=!0:i==="OFF"&&(i=!1),l.options[this.variable]=i}else if(this.expression){this.exists&&(this.existsfn=this.exists.map(s=>{let u=s.compile(e);return u.query&&!u.query.modifier&&(u.query.modifier="RECORDSET"),u})),this.queries&&(this.queriesfn=this.queries.map(s=>{let u=s.compile(e);return u.query&&!u.query.modifier&&(u.query.modifier="RECORDSET"),u}));let i=new Function("params, alasql","return "+this.expression.toJS("({})","",null)).bind(this)(n,l);if(l.declares[this.variable]&&(i=l.stdfn.CONVERT(i,l.declares[this.variable])),this.props&&this.props.length>0){let s;this.method==="@"?s=`alasql.vars['${this.variable}']`:s=`params['${this.variable}']`,this.props.forEach(u=>{typeof u=="string"?s+=`['${u}']`:typeof u=="number"?s+=`[${u}]`:s+=`[${u.toJS()}]`}),new Function("value, params, alasql",`${s} = value`)(i,n,l)}else this.method==="@"?l.vars[this.variable]=i:n[this.variable]=i}let r=1;return t&&(r=t(r)),r},l.test=function(e,n,t){if(arguments.length===0){l.log(l.con.results);return}var r=Date.now();if(arguments.length===1){t(),l.con.log(Date.now()-r);return}arguments.length===2&&(t=n,n=1);for(var i=0;i<n;i++)t();l.con.results[e]=Date.now()-r},l.log=function(e,n){var t=l.useid,r=l.options.logtarget;M1.isNode&&(r="console");var i;if(typeof e=="string"?i=l(e,n):i=e,r==="console"||M1.isNode)typeof e=="string"&&l.options.logprompt&&console.log(t+">",e),Array.isArray(i)&&console.table?console.table(i):console.log(At(i));else{var s;r==="output"?s=document.getElementsByTagName("output")[0]:typeof r=="string"?s=document.getElementById(r):s=r;var u="";if(typeof e=="string"&&l.options.logprompt&&(u+="<pre><code>"+l.pretty(e)+"</code></pre>"),Array.isArray(i))if(i.length===0)u+="<p>[ ]</p>";else if(typeof i[0]!="object"||Array.isArray(i[0]))for(var f=0,h=i.length;f<h;f++)u+="<p>"+R4(i[f])+"</p>";else u+=R4(i);else u+=R4(i);s.innerHTML+=u}},l.clear=function(){var e=l.options.logtarget;if(M1.isNode||M1.isMeteorServer)console.clear&&console.clear();else{var n;e==="output"?n=document.getElementsByTagName("output")[0]:typeof e=="string"?n=document.getElementById(e):n=e,n.innerHTML=""}},l.write=function(e){var n=l.options.logtarget;if(M1.isNode||M1.isMeteorServer)console.log&&console.log(e);else{var t;n==="output"?t=document.getElementsByTagName("output")[0]:typeof n=="string"?t=document.getElementById(n):t=n,t.innerHTML+=e}};function R4(e){var n="";if(e===void 0)n+="undefined";else if(Array.isArray(e)){n+="<style>",n+="table {border:1px black solid; border-collapse: collapse; border-spacing: 0px;}",n+="td,th {border:1px black solid; padding-left:5px; padding-right:5px}",n+="th {background-color: #EEE}",n+="</style>",n+="<table>";var t=[];for(var r in e[0])t.push(r);n+="<tr><th>#",t.forEach(function(u){n+="<th>"+u});for(var i=0,s=e.length;i<s;i++)n+="<tr><th>"+(i+1),t.forEach(function(u){n+="<td> ",e[i][u]==+e[i][u]?(n+='<div style="text-align:right">',typeof e[i][u]>"u"?n+="NULL":n+=e[i][u],n+="</div>"):typeof e[i][u]>"u"?n+="NULL":typeof e[i][u]=="string"?n+=e[i][u]:n+=At(e[i][u])});n+="</table>"}else n+="<p>"+At(e)+"</p>";return n}function I4(e,n,t){if(!(t<=0)){var r=n-e.scrollTop,i=r/t*10;setTimeout(function(){e.scrollTop!==n&&(e.scrollTop=e.scrollTop+i,I4(e,n,t-10))},10)}}l.prompt=function(e,n,t){if(M1.isNode)throw new Error("The prompt not realized for Node.js");var r=0;if(typeof e=="string"&&(e=document.getElementById(e)),typeof n=="string"&&(n=document.getElementById(n)),n.textContent=l.useid,t){l.prompthistory.push(t),r=l.prompthistory.length;try{var i=Date.now();l.log(t),l.write('<p style="color:blue">'+(Date.now()-i)+" ms</p>")}catch(u){l.write("<p>"+l.useid+"&gt;&nbsp;<b>"+t+"</b></p>"),l.write('<p style="color:red">'+u+"<p>")}}var s=e.getBoundingClientRect().top+document.getElementsByTagName("body")[0].scrollTop;I4(document.getElementsByTagName("body")[0],s,500),e.onkeydown=function(u){if(u.which===13){var f=e.value,h=l.useid;e.value="",l.prompthistory.push(f),r=l.prompthistory.length;try{var g=Date.now();l.log(f),l.write('<p style="color:blue">'+(Date.now()-g)+" ms</p>")}catch(b){l.write("<p>"+h+"&gt;&nbsp;"+l.pretty(f,!1)+"</p>"),l.write('<p style="color:red">'+b+"<p>")}e.focus(),n.textContent=l.useid;var m=e.getBoundingClientRect().top+document.getElementsByTagName("body")[0].scrollTop;I4(document.getElementsByTagName("body")[0],m,500)}else u.which===38?(r--,r<0&&(r=0),l.prompthistory[r]&&(e.value=l.prompthistory[r],u.preventDefault())):u.which===40&&(r++,r>=l.prompthistory.length?(r=l.prompthistory.length,e.value=""):l.prompthistory[r]&&(e.value=l.prompthistory[r],u.preventDefault()))}},T.BeginTransaction=function(e){return Object.assign(this,e)},T.BeginTransaction.prototype.toString=function(){return"BEGIN TRANSACTION"},T.BeginTransaction.prototype.execute=function(e,n,t){var r=1;return l.databases[e].engineid?l.engines[l.databases[l.useid].engineid].begin(e,t):(t&&(r=t(r)),r)},T.CommitTransaction=function(e){return Object.assign(this,e)},T.CommitTransaction.prototype.toString=function(){return"COMMIT TRANSACTION"},T.CommitTransaction.prototype.execute=function(e,n,t){var r=1;return l.databases[e].engineid?l.engines[l.databases[l.useid].engineid].commit(e,t):(t&&(r=t(r)),r)},T.RollbackTransaction=function(e){return Object.assign(this,e)},T.RollbackTransaction.prototype.toString=function(){return"ROLLBACK TRANSACTION"},T.RollbackTransaction.prototype.execute=function(e,n,t){var r=1;return l.databases[e].engineid?l.engines[l.databases[e].engineid].rollback(e,t):(t&&(r=t(r)),r)},l.options.tsql&&(l.stdfn.OBJECT_ID=function(e,n){typeof n>"u"&&(n="T"),n=n.toUpperCase();var t=e.split("."),r=l.useid,i=t[0];t.length==2&&(r=t[0],i=t[1]);var s=l.databases[r].tables;r=l.databases[r].databaseid;for(var u in s)if(u==i)return s[u].view&&n=="V"||!s[u].view&&n=="T"?r+"."+u:void 0}),l.options.mysql&&(l.fn.TIMESTAMPDIFF=function(e,n,t){return l.stdfn.DATEDIFF(e,n,t)}),(l.options.mysql||l.options.sqlite)&&(l.from.INFORMATION_SCHEMA=function(e,n,t,r,i){if(e=="VIEWS"||e=="TABLES"){var s=[];for(var u in l.databases){var f=l.databases[u].tables;for(var h in f)(f[h].view&&e=="VIEWS"||!f[h].view&&e=="TABLES")&&s.push({TABLE_CATALOG:u,TABLE_NAME:h})}return t&&(s=t(s,r,i)),s}throw new Error("Unknown INFORMATION_SCHEMA table")}),l.options.postgres,l.options.oracle,l.options.sqlite,l.into.SQL=function(e,n,t,r,i){var s;typeof e=="object"&&(n=e,e=void 0);var u={};if(l.utils.extend(u,n),typeof u.tableid>"u")throw new Error("Table for INSERT TO is not defined.");var f="";r.length===0&&typeof t[0]=="object"&&(r=Object.keys(t[0]).map(function(m){return{columnid:m}}));for(var h=0,g=t.length;h<g;h++)f+="INSERT INTO "+n.tableid+"(",f+=r.map(function(m){return m.columnid}).join(","),f+=") VALUES (",f+=r.map(function(m){var b=t[h][m.columnid];return m.typeid?(m.typeid==="STRING"||m.typeid==="VARCHAR"||m.typeid==="NVARCHAR"||m.typeid==="CHAR"||m.typeid==="NCHAR")&&(b="'"+ur(b)+"'"):typeof b=="string"&&(b="'"+ur(b)+"'"),b}),f+=`);
`;return e=l.utils.autoExtFilename(e,"sql",n),s=l.utils.saveFile(e,f),i&&(s=i(s)),s},l.into.HTML=function(e,n,t,r,i){var s=1;if(typeof document!="object"){var u={headers:!0};l.utils.extend(u,n);var f=document.querySelector(e);if(!f)throw new Error("Selected HTML element is not found");r.length===0&&typeof t[0]=="object"&&(r=Object.keys(t[0]).map(function(E){return{columnid:E}}));var h=document.createElement("table"),g=document.createElement("thead");if(h.appendChild(g),u.headers){for(var m=document.createElement("tr"),b=0;b<r.length;b++){var c=document.createElement("th");c.textContent=r[b].columnid,m.appendChild(c)}g.appendChild(m)}var d=document.createElement("tbody");h.appendChild(d);for(var v=0;v<t.length;v++){for(var m=document.createElement("tr"),b=0;b<r.length;b++){var c=document.createElement("td");c.textContent=t[v][r[b].columnid],m.appendChild(c)}d.appendChild(m)}l.utils.domEmptyChildren(f),f.appendChild(h)}return i&&(s=i(s)),s},l.into.JSON=function(e,n,t,r,i){var s=1;typeof e=="object"&&(n=e,e=void 0);var u=JSON.stringify(t);return e=l.utils.autoExtFilename(e,"json",n),s=l.utils.saveFile(e,u),i&&(s=i(s)),s},l.into.TXT=function(e,n,t,r,i){r.length===0&&t.length>0&&(r=Object.keys(t[0]).map(function(h){return{columnid:h}})),typeof e=="object"&&(n=e,e=void 0);var s=t.length,u="";if(t.length>0){var f=r[0].columnid;u+=t.map(function(h){return h[f]}).join(`
`)}return e=l.utils.autoExtFilename(e,"txt",n),s=l.utils.saveFile(e,u),i&&(s=i(s)),s},l.into.TAB=l.into.TSV=function(e,n,t,r,i){var s={};return l.utils.extend(s,n),s.separator="	",e=l.utils.autoExtFilename(e,"tab",n),s.autoExt=!1,l.into.CSV(e,s,t,r,i)},l.into.CSV=function(e,n,t,r,i){r.length===0&&t.length>0&&(r=Object.keys(t[0]).map(function(h){return{columnid:h}})),typeof e=="object"&&(n=e,e=void 0);var s={headers:!0};s.separator=";",s.quote='"',s.utf8Bom=!0,n&&!n.headers&&typeof n.headers<"u"&&(s.utf8Bom=!1),l.utils.extend(s,n);var u=t.length,f=s.utf8Bom?"\uFEFF":"";return s.headers&&(f+=s.quote+r.map(function(h){return h.columnid.trim()}).join(s.quote+s.separator+s.quote)+s.quote+`\r
`),t.forEach(function(h){f+=r.map(function(g){var m=h[g.columnid];return s.quote!==""&&(m=(m+"").replace(new RegExp("\\"+s.quote,"g"),s.quote+s.quote)),+m!=m&&(m=s.quote+m+s.quote),m}).join(s.separator)+`\r
`}),e=l.utils.autoExtFilename(e,"csv",n),u=l.utils.saveFile(e,f,null,{disableAutoBom:!0}),i&&(u=i(u)),u},l.into.XLS=function(e,n,t,r,i){typeof e=="object"&&(n=e,e=void 0);var s={};n&&n.sheets&&(s=n.sheets);var u={headers:!0};typeof s.Sheet1<"u"?u=s[0]:typeof n<"u"&&(u=n),typeof u.sheetid>"u"&&(u.sheetid="Sheet1");var f=g();e=l.utils.autoExtFilename(e,"xls",n);var h=l.utils.saveFile(e,f);return i&&(h=i(h)),h;function g(){var b='<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" 		xmlns="http://www.w3.org/TR/REC-html40"><head> 		<meta charset="utf-8" /> 		<!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets> ';if(b+=" <x:ExcelWorksheet><x:Name>"+u.sheetid+"</x:Name><x:WorksheetOptions><x:DisplayGridlines/>     </x:WorksheetOptions> 		</x:ExcelWorksheet>",b+="</x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>",b+="<body",typeof u.style<"u"&&(b+=' style="',typeof u.style=="function"?b+=u.style(u):b+=u.style,b+='"'),b+=">",b+="<table>",typeof u.caption<"u"){var c=u.caption;typeof c=="string"&&(c={title:c}),b+="<caption",typeof c.style<"u"&&(b+=' style="',typeof c.style=="function"?b+=c.style(u,c):b+=c.style,b+='" '),b+=">",b+=c.title,b+="</caption>"}return typeof u.columns<"u"?r=u.columns:r.length==0&&t.length>0&&typeof t[0]=="object"&&(Array.isArray(t[0])?r=t[0].map(function(d,v){return{columnid:v}}):r=Object.keys(t[0]).map(function(d){return{columnid:d}})),r.forEach(function(d,v){typeof u.column<"u"&&V2(d,u.column),typeof d.width>"u"&&(u.column&&u.column.width!="undefined"?d.width=u.column.width:d.width="120px"),typeof d.width=="number"&&(d.width=d.width+"px"),typeof d.columnid>"u"&&(d.columnid=v),typeof d.title>"u"&&(d.title=""+d.columnid.trim()),u.headers&&Array.isArray(u.headers)&&(d.title=u.headers[v])}),b+="<colgroups>",r.forEach(function(d){b+='<col style="width: '+d.width+'"></col>'}),b+="</colgroups>",u.headers&&(b+="<thead>",b+="<tr>",r.forEach(function(d,v){b+="<th ",typeof d.style<"u"&&(b+=' style="',typeof d.style=="function"?b+=d.style(u,d,v):b+=d.style,b+='" '),b+=">",typeof d.title<"u"&&(typeof d.title=="function"?b+=d.title(u,d,v):b+=d.title),b+="</th>"}),b+="</tr>",b+="</thead>"),b+="<tbody>",t&&t.length>0&&t.forEach(function(d,v){if(!(v>u.limit)){b+="<tr";var E={};V2(E,u.row),u.rows&&u.rows[v]&&V2(E,u.rows[v]),typeof E<"u"&&typeof E.style<"u"&&(b+=' style="',typeof E.style=="function"?b+=E.style(u,d,v):b+=E.style,b+='" '),b+=">",r.forEach(function(O,R){var A={};V2(A,u.cell),V2(A,E.cell),typeof u.column<"u"&&V2(A,u.column.cell),V2(A,O.cell),u.cells&&u.cells[v]&&u.cells[v][R]&&V2(A,u.cells[v][R]);var N=d[O.columnid];typeof A.value=="function"&&(N=A.value(N,u,d,O,A,v,R));var w=A.typeid;typeof w=="function"&&(w=w(N,u,d,O,A,v,R)),typeof w>"u"&&(typeof N=="number"?w="number":typeof N=="string"?w="string":typeof N=="boolean"?w="boolean":typeof N=="object"&&N instanceof Date&&(w="date"));var T1="";w=="money"?T1='mso-number-format:"\\#\\,\\#\\#0\\\\ _\u0440_\\.";white-space:normal;':w=="number"?T1=" ":w=="date"?T1='mso-number-format:"Short Date";':n.types&&n.types[w]&&n.types[w].typestyle&&(T1=n.types[w].typestyle),T1=T1||'mso-number-format:"\\@";',b+="<td style='"+T1+"' ",typeof A.style<"u"&&(b+=' style="',typeof A.style=="function"?b+=A.style(N,u,d,O,v,R):b+=A.style,b+='" '),b+=">";var v1=A.format;if(typeof N>"u")b+="";else if(typeof v1<"u")if(typeof v1=="function")b+=v1(N);else if(typeof v1=="string")b+=N;else throw new Error("Unknown format type. Should be function or string");else w=="number"||w=="date"?b+=N.toString():w=="money"?b+=(+N).toFixed(2):b+=N;b+="</td>"}),b+="</tr>"}}),b+="</tbody>",b+="</table>",b+="</body>",b+="</html>",b}function m(b){var c=' style="';return b&&typeof b.style<"u"&&(c+=b.style+";"),c+='" ',c}},l.into.XLSXML=function(e,n,t,r,i){n=n||{},typeof e=="object"&&(n=e,e=void 0);var s={},u,f;n&&n.sheets?(s=n.sheets,u=t,f=r):(s.Sheet1=n,u=[t],f=[r]),e=l.utils.autoExtFilename(e,"xls",n);var h=l.utils.saveFile(e,g());return i&&(h=i(h)),h;function g(){var m='<?xml version="1.0"?> 		<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" 		 xmlns:o="urn:schemas-microsoft-com:office:office" 		 xmlns:x="urn:schemas-microsoft-com:office:excel" 		 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" 		 xmlns:html="http://www.w3.org/TR/REC-html40"> 		 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"> 		 </DocumentProperties> 		 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office"> 		  <AllowPNG/> 		 </OfficeDocumentSettings> 		 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"> 		  <ActiveSheet>0</ActiveSheet> 		 </ExcelWorkbook> 		 <Styles> 		  <Style ss:ID="Default" ss:Name="Normal"> 		   <Alignment ss:Vertical="Bottom"/> 		   <Borders/> 		   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="12" ss:Color="#000000"/> 		   <Interior/> 		   <NumberFormat/> 		   <Protection/> 		  </Style>',b="",c=" </Styles>",d={},v=62;function E(O1){var G1="";for(var B1 in O1){G1+="<"+B1;for(var Y1 in O1[B1])G1+=" ",Y1.substr(0,2)=="x:"?G1+=Y1:G1+="ss:",G1+=Y1+"="+JSON.stringify(O1[B1][Y1]);G1+="/>"}var k=ot(G1);return d[k]||(d[k]={styleid:v},b+=`<Style ss:ID=${JSON.stringify("s"+v)}>`,b+=G1.replace(/<\s*\/\s*Style /gi,"<"),b+="</Style>",v++),"s"+d[k].styleid}function O(O1){try{return Object.values(O1)}catch{return Object.keys(O1).map(function(B1){return O1[B1]})}}var R=0;for(var A in s){var N=s[A],w=typeof N.dataidx<"u"?N.dataidx:R++,T1=O(u[w]),v1=void 0;typeof N.columns<"u"?v1=N.columns:(v1=f[w],(v1===void 0||v1.length==0&&T1.length>0)&&typeof T1[0]=="object"&&(Array.isArray(T1[0])?v1=T1[0].map(function(O1,G1){return{columnid:G1}}):v1=Object.keys(T1[0]).map(function(O1){return{columnid:O1}}))),v1.forEach(function(O1,G1){typeof N.column<"u"&&V2(O1,N.column),typeof O1.width>"u"&&(N.column&&typeof N.column.width<"u"?O1.width=N.column.width:O1.width=120),typeof O1.width=="number"&&(O1.width=O1.width),typeof O1.columnid>"u"&&(O1.columnid=G1),typeof O1.title>"u"&&(O1.title=""+O1.columnid.trim()),N.headers&&Array.isArray(N.headers)&&(O1.title=N.headers[G1])}),c+="<Worksheet ss:Name="+JSON.stringify(A)+'> 	  			<Table ss:ExpandedColumnCount="'+v1.length+'" ss:ExpandedRowCount="'+((N.headers?1:0)+Math.min(T1.length,N.limit||T1.length))+'" x:FullColumns="1" 	   			x:FullRows="1" ss:DefaultColumnWidth="65" ss:DefaultRowHeight="15">',v1.forEach(function(O1,G1){c+=`
					<Column 
						ss:Index="${G1+1}" 
						ss:AutoFitWidth="0" 
						ss:Width=${JSON.stringify(""+O1.width)}
					/>`}),N.headers&&(c+='<Row ss:AutoFitHeight="0">',v1.forEach(function(O1,G1){if(c+="<Cell ",typeof O1.style<"u"){var B1={};typeof O1.style=="function"?V2(B1,O1.style(N,O1,G1)):V2(B1,O1.style),c+="ss:StyleID="+JSON.stringify(E(B1))}c+='><Data ss:Type="String">',typeof O1.title<"u"&&(typeof O1.title=="function"?c+=O1.title(N,O1,G1):c+=O1.title),c+="</Data></Cell>"}),c+="</Row>"),T1&&T1.length>0&&T1.forEach(function(O1,G1){if(!(G1>N.limit)){var B1={};if(V2(B1,N.row),N.rows&&N.rows[G1]&&V2(B1,N.rows[G1]),c+="<Row ",typeof B1<"u"){var Y1={};typeof B1.style<"u"&&(typeof B1.style=="function"?V2(Y1,B1.style(N,O1,G1)):V2(Y1,B1.style),c+="ss:StyleID="+JSON.stringify(E(Y1)))}c+=">",v1.forEach(function(k,s2){var K1={};V2(K1,N.cell),V2(K1,B1.cell),typeof N.column<"u"&&V2(K1,N.column.cell),V2(K1,k.cell),N.cells&&N.cells[G1]&&N.cells[G1][s2]&&V2(K1,N.cells[G1][s2]);var V1=O1[k.columnid];typeof K1.value=="function"&&(V1=K1.value(V1,N,O1,k,K1,G1,s2));var T2=K1.typeid;typeof T2=="function"&&(T2=T2(V1,N,O1,k,K1,G1,s2)),typeof T2>"u"&&(typeof V1=="number"?T2="number":typeof V1=="string"?T2="string":typeof V1=="boolean"?T2="boolean":typeof V1=="object"&&V1 instanceof Date&&(T2="date"));var p2="String";T2=="number"?p2="Number":T2=="date"&&(p2="Date");var O2="";T2=="money"?O2='mso-number-format:"\\#\\,\\#\\#0\\\\ _\u0440_\\.";white-space:normal;':T2=="number"?O2=" ":T2=="date"?O2='mso-number-format:"Short Date";':n.types&&n.types[T2]&&n.types[T2].typestyle&&(O2=n.types[T2].typestyle),O2=O2||'mso-number-format:"\\@";',c+="<Cell ";var N2={};typeof K1.style<"u"&&(typeof K1.style=="function"?V2(N2,K1.style(V1,N,O1,k,G1,s2)):V2(N2,K1.style),c+="ss:StyleID="+JSON.stringify(E(N2))),c+=">",c+="<Data ss:Type="+JSON.stringify(p2)+">";var A2=K1.format;if(typeof V1>"u")c+="";else if(typeof A2<"u")if(typeof A2=="function")c+=A2(V1);else if(typeof A2=="string")c+=V1;else throw new Error("Unknown format type. Should be function or string");else T2=="number"||T2=="date"?c+=V1.toString():T2=="money"?c+=(+V1).toFixed(2):c+=V1;c+="</Data></Cell>"}),c+="</Row>"}}),c+="</Table></Worksheet>"}return c+="</Workbook>",m+b+c}},l.into.XLSX=function(e,n,t,r,i){var s=1;n=n||{},Xt(r,[{columnid:"_"}])&&(t=t.map(function(b){return b._}),r=void 0),e=l.utils.autoExtFilename(e,"xlsx",n);var u=p3();typeof e=="object"&&(n=e,e=void 0);var f={SheetNames:[],Sheets:{}};return n.sourcefilename?l.utils.loadBinaryFile(n.sourcefilename,!!i,function(b){f=u.read(b,{type:"binary",...l.options.excel,...n}),h()}):h(),i&&(s=i(s)),s;function h(){typeof n=="object"&&Array.isArray(n)?t&&t.length>0&&t.forEach(function(b,c){g(n[c],b,void 0,c+1)}):g(n,t,r,1),m(i)}function g(b,c,d,v){var E={sheetid:"Sheet "+v,headers:!0};l.utils.extend(E,b);var O=Object.keys(c).length;(!d||d.length==0)&&(O>0?d=Object.keys(c[0]).map(function(K1){return{columnid:K1}}):d=[]);var R={};f.SheetNames.indexOf(E.sheetid)>-1||(f.SheetNames.push(E.sheetid),f.Sheets[E.sheetid]={}),R=f.Sheets[E.sheetid];var A="A1";E.range&&(A=E.range);var N=l.utils.xlscn(A.match(/[A-Z]+/)[0]),w=+A.match(/[0-9]+/)[0]-1;if(f.Sheets[E.sheetid]["!ref"])var T1=f.Sheets[E.sheetid]["!ref"],v1=l.utils.xlscn(T1.match(/[A-Z]+/)[0]),O1=+T1.match(/[0-9]+/)[0]-1;else var v1=1,O1=1;var G1=d.length?0:1,B1=Math.max(N+d.length-1+G1,v1),Y1=Math.max(w+O+2,O1),k=w+1;f.Sheets[E.sheetid]["!ref"]="A1:"+l.utils.xlsnc(B1)+Y1,E.headers&&(d.forEach(function(K1,V1){R[l.utils.xlsnc(N+V1)+""+k]={v:K1.columnid.trim()}}),k++);for(var s2=0;s2<O;s2++)d.forEach(function(K1,V1){var T2={v:c[s2][K1.columnid]};typeof c[s2][K1.columnid]=="number"?T2.t="n":typeof c[s2][K1.columnid]=="string"?T2.t="s":typeof c[s2][K1.columnid]=="boolean"?T2.t="b":typeof c[s2][K1.columnid]=="object"&&c[s2][K1.columnid]instanceof Date&&(T2.t="d"),R[l.utils.xlsnc(N+V1)+""+k]=T2}),k++}function m(b){var c;if(typeof e>"u")s=f;else if(c=p3(),M1.isNode||M1.isMeteorServer)c.writeFile(f,e);else{var d={bookType:"xlsx",bookSST:!1,type:"binary"},v=c.write(f,d),E=function(O){for(var R=new ArrayBuffer(O.length),A=new Uint8Array(R),N=0;N!=O.length;++N)A[N]=O.charCodeAt(N)&255;return R};Qt(new Blob([E(v)],{type:"application/octet-stream"}),e)}}},l.from.METEOR=function(e,n,t,r,i){var s=e.find(n).fetch();return t&&(s=t(s,r,i)),s},l.from.TABLETOP=function(e,n,t,r,i){var s=[],u={headers:!0,simpleSheet:!0,key:e};return l.utils.extend(u,n),u.callback=function(f){s=f,t&&(s=t(s,r,i))},Tabletop.init(u),null},l.from.HTML=function(e,n,t,r,i){var s={};l.utils.extend(s,n);var u=document.querySelector(e);if(!u&&u.tagName!=="TABLE")throw new Error("Selected HTML element is not a TABLE");var f=[],h=s.headers;if(h&&!Array.isArray(h)){h=[];for(var g=u.querySelector("thead tr").children,m=0;m<g.length;m++)g.item(m).style&&g.item(m).style.display==="none"&&s.skipdisplaynone?h.push(void 0):h.push(g.item(m).textContent)}for(var b=u.querySelectorAll("tbody tr"),c=0;c<b.length;c++){var d=b.item(c).children,v={};for(m=0;m<d.length;m++)d.item(m).style&&d.item(m).style.display==="none"&&s.skipdisplaynone||(h?v[h[m]]=d.item(m).textContent:v[m]=d.item(m).textContent);f.push(v)}return t&&(f=t(f,r,i)),f},l.from.RANGE=function(e,n,t,r,i){for(var s=[],u=e;u<=n;u++)s.push(u);return t&&(s=t(s,r,i)),s},l.from.FILE=function(e,n,t,r,i){var s;if(typeof e=="string")s=e;else if(e instanceof Event)s=e.target.files[0].name;else throw new Error("Wrong usage of FILE() function");var u=s.split("."),f=u[u.length-1].toUpperCase();if(l.from[f])return l.from[f](e,n,t,r,i);throw new Error("Cannot recognize file type for loading")},l.from.JSON=function(e,n,t,r,i){var s;return e=l.utils.autoExtFilename(e,"json",n),l.utils.loadFile(e,!!t,function(u){s=JSON.parse(u),t&&(s=t(s,r,i))}),s};const Ar=e=>function(n,t,r,i,s){let u=[];return n=l.utils.autoExtFilename(n,e,t),l.utils.loadFile(n,!!r,function(f){f.split(/\r?\n/).forEach((h,g)=>{const m=h.trim();if(m!=="")try{u.push(JSON.parse(m))}catch(b){throw new Error(`Could not parse JSON at line ${g}: ${b.toString()}`)}}),r&&(u=r(u,i,s))},f=>{const h=f instanceof Error?f:new Error(f);if(s&&s.cb){s.cb(null,h);return}throw h}),u};l.from.JSONL=Ar("jsonl"),l.from.NDJSON=Ar("ndjson"),l.from.TXT=function(e,n,t,r,i){var s;return e=l.utils.autoExtFilename(e,"txt",n),l.utils.loadFile(e,!!t,function(u){s=u.split(/\r?\n/),s[s.length-1]===""&&s.pop();for(var f=0,h=s.length;f<h;f++)s[f]==+s[f]&&(s[f]=+s[f]),s[f]=[s[f]];t&&(s=t(s,r,i))}),s},l.from.TAB=l.from.TSV=function(e,n,t,r,i){return n=n||{},n.separator="	",e=l.utils.autoExtFilename(e,"tab",n),n.autoext=!1,l.from.CSV(e,n,t,r,i)},l.from.CSV=function(e,n,t,r,i){e=""+e;var s={separator:",",quote:'"',headers:!0,raw:!1};l.utils.extend(s,n);var u,f=[];function h(g){var m=s.separator.charCodeAt(0),b=s.quote.charCodeAt(0),c={},d={},v=[],E=g.length,O=0,R=0,A,N;function w(){if(O>=E)return d;if(N)return N=!1,c;var G1=O;if(g.charCodeAt(G1)===b){for(var B1=G1;B1++<E;)if(g.charCodeAt(B1)===b){if(g.charCodeAt(B1+1)!==b)break;++B1}O=B1+2;var Y1=g.charCodeAt(B1+1);return Y1===13?(N=!0,g.charCodeAt(B1+2)===10&&++O):Y1===10&&(N=!0),g.substring(G1+1,B1).replace(/""/g,'"')}for(;O<E;){var Y1=g.charCodeAt(O++),k=1;if(Y1===10)N=!0;else if(Y1===13)N=!0,g.charCodeAt(O)===10&&(++O,++k);else if(Y1!==m)continue;return g.substring(G1,O-k)}return g.substring(G1)}for(;(A=w())!==d;){for(var T1=[];A!==c&&A!==d;)T1.push(A.trim()),A=w();if(s.headers){if(R===0){if(typeof s.headers=="boolean")f=T1;else if(Array.isArray(s.headers)){f=s.headers;var v1={};f.forEach(function(G1,B1){v1[G1]=T1[B1],!s.raw&&typeof v1[G1]<"u"&&v1[G1].length!==0&&v1[G1].trim()==+v1[G1]&&(v1[G1]=+v1[G1])}),v.push(v1)}}else{var v1={};f.forEach(function(B1,Y1){v1[B1]=T1[Y1],!s.raw&&typeof v1[B1]<"u"&&v1[B1].length!==0&&v1[B1].trim()==+v1[B1]&&(v1[B1]=+v1[B1])}),v.push(v1)}R++}else{var v1={};T1.forEach(function(B1,Y1){v1[Y1]=T1[Y1],!s.raw&&typeof v1[Y1]<"u"&&v1[Y1].length!==0&&v1[Y1].trim()==+v1[Y1]&&(v1[Y1]=+v1[Y1])}),v.push(v1)}}if(u=v,s.headers&&i&&i.sources&&i.sources[r]){var O1=i.sources[r].columns=[];f.forEach(function(G1){O1.push({columnid:G1})})}t&&(u=t(u,r,i))}return new RegExp(`
`).test(e)?h(e):(e=l.utils.autoExtFilename(e,"csv",n),l.utils.loadFile(e,!!t,h,g=>i.cb(null,g))),u};function w4(e,n,t,r,i,s){var u={};t=t||{},l.utils.extend(u,t),typeof u.headers>"u"&&(u.headers=!0);var f;function h(m){for(var b="",c=0,d=10240;c<m.byteLength/d;++c)b+=String.fromCharCode.apply(null,new Uint8Array(m.slice(c*d,c*d+d)));return b+=String.fromCharCode.apply(null,new Uint8Array(m.slice(c*d))),b}function g(m){return m&&l.options.casesensitive===!1?m.toLowerCase():m}return n=l.utils.autoExtFilename(n,"xls",t),l.utils.loadBinaryFile(n,!!r,function(m){if(m instanceof ArrayBuffer)var b=h(m),c=e.read(btoa(b),{type:"base64",...l.options.excel,...t});else var c=e.read(m,{type:"binary",...l.options.excel,...t});var d;typeof u.sheetid>"u"?d=c.SheetNames[0]:typeof u.sheetid=="number"?d=c.SheetNames[u.sheetid]:d=u.sheetid;var v,E=[];if(typeof u.range>"u"?v=c.Sheets[d]["!ref"]:(v=u.range,c.Sheets[d][v]&&(v=c.Sheets[d][v])),v){for(var O=v.split(":"),R=O[0].match(/[A-Z]+/)[0],A=+O[0].match(/[0-9]+/)[0],N=O[1].match(/[A-Z]+/)[0],w=+O[1].match(/[0-9]+/)[0],T1={},v1=l.utils.xlscn(R),O1=l.utils.xlscn(N),G1=v1;G1<=O1;G1++){var B1=l.utils.xlsnc(G1);u.headers?c.Sheets[d][B1+""+A]?T1[B1]=g(c.Sheets[d][B1+""+A].v):T1[B1]=g(B1):T1[B1]=B1}u.headers&&A++;for(var Y1=A;Y1<=w;Y1++){for(var k={},G1=v1;G1<=O1;G1++){var B1=l.utils.xlsnc(G1);c.Sheets[d][B1+""+Y1]&&(k[T1[B1]]=c.Sheets[d][B1+""+Y1].v)}E.push(k)}}else E.push([]);E.length>0&&E[E.length-1]&&Object.keys(E[E.length-1]).length==0&&E.pop(),r&&(E=r(E,i,s))},function(m){if(s&&s.cb){s.cb(null,m);return}throw m}),f}l.from.XLS=function(e,n,t,r,i){return n=n||{},e=l.utils.autoExtFilename(e,"xls",n),n.autoExt=!1,w4(p3(),e,n,t,r,i)},l.from.XLSX=function(e,n,t,r,i){return n=n||{},e=l.utils.autoExtFilename(e,"xlsx",n),n.autoExt=!1,w4(p3(),e,n,t,r,i)},l.from.ODS=function(e,n,t,r,i){return n=n||{},e=l.utils.autoExtFilename(e,"ods",n),n.autoExt=!1,w4(p3(),e,n,t,r,i)},l.from.XML=function(e,n,t,r,i){var s;return l.utils.loadFile(e,!!t,function(u){s=xi(u).root,t&&(s=t(s,r,i))}),s};function xi(e){return e=e.trim(),e=e.replace(/<!--[\s\S]*?-->/g,""),n();function n(){return{declaration:t(),root:r()}}function t(){var m=f(/^<\?xml\s*/);if(m){for(var b={attributes:{}};!(h()||g("?>"));){var c=s();if(!c)return b;b.attributes[c.name]=c.value}return f(/\?>\s*/),b}}function r(){var m=f(/^<([\w-:.]+)\s*/);if(m){for(var b={name:m[1],attributes:{},children:[]};!(h()||g(">")||g("?>")||g("/>"));){var c=s();if(!c)return b;b.attributes[c.name]=c.value}if(f(/^\s*\/>\s*/))return b;f(/\??>\s*/),b.content=i();for(var d;d=r();)b.children.push(d);return f(/^<\/[\w-:.]+>\s*/),b}}function i(){var m=f(/^([^<]*)/);return m?m[1]:""}function s(){var m=f(/([\w:-]+)\s*=\s*("[^"]*"|'[^']*'|\w+)\s*/);if(m)return{name:m[1],value:u(m[2])}}function u(m){return m.replace(/^['"]|['"]$/g,"")}function f(m){var b=e.match(m);if(b)return e=e.slice(b[0].length),b}function h(){return e.length==0}function g(m){return e.indexOf(m)==0}}l.from.GEXF=function(e,n,t,r,i){var s;return l("SEARCH FROM XML("+e+")",[],function(u){s=u,t&&(s=t(s))}),s},T.Print=function(e){return Object.assign(this,e)},T.Print.prototype.toString=function(){var e="PRINT";return this.statement&&(e+=" "+this.statement.toString()),e},T.Print.prototype.execute=function(e,n,t){var r=this,i=1;if(l.precompile(this,e,n),this.exprs&&this.exprs.length>0){var s=this.exprs.map(function(f){var h=new Function("params,alasql,p","var y;return "+f.toJS("({})","",null)).bind(r),g=h(n,l);return At(g)});console.log.apply(console,s)}else if(this.select){var u=this.select.execute(e,n);console.log(At(u))}else console.log();return t&&(i=t(i)),i},T.Source=function(e){return Object.assign(this,e)},T.Source.prototype.toString=function(){var e="SOURCE";return this.url&&(e+=" '"+this.url+" '"),e},T.Source.prototype.execute=function(e,n,t){var r;return G3(this.url,!!t,function(i){return r=l(i),t&&(r=t(r)),r},function(i){throw i}),r},T.Require=function(e){return Object.assign(this,e)},T.Require.prototype.toString=function(){var e="REQUIRE";return this.paths&&this.paths.length>0&&(e+=this.paths.map(function(n){return n.toString()}).join(",")),this.plugins&&this.plugins.length>0&&(e+=this.plugins.map(function(n){return n.toUpperCase()}).join(",")),e},T.Require.prototype.execute=function(e,n,t){var r=this,i=0,s="";return this.paths&&this.paths.length>0?this.paths.forEach(function(u){G3(u.value,!!t,function(f){i++,s+=f,!(i<r.paths.length)&&(new Function("params,alasql",s)(n,l),t&&(i=t(i)))})}):this.plugins&&this.plugins.length>0?this.plugins.forEach(function(u){l.plugins[u]||G3(l.path+"/alasql-"+u.toLowerCase()+".js",!!t,function(f){i++,s+=f,!(i<r.plugins.length)&&(new Function("params,alasql",s)(n,l),l.plugins[u]=!0,t&&(i=t(i)))})}):t&&(i=t(i)),i},T.Assert=function(e){return Object.assign(this,e)},T.Source.prototype.toString=function(){var e="ASSERT";return this.value&&(e+=" "+JSON.stringify(this.value)),e},T.Assert.prototype.execute=function(e){if(!Xt(l.res,this.value))throw new Error((this.message||"Assert wrong")+": "+JSON.stringify(l.res)+" == "+JSON.stringify(this.value));return 1};var dt=l.engines.INDEXEDDB=function(){""};async function E3(e){const n=globalThis.indexedDB;if(!n)throw new Error("IndexedDB is not supported in this browser");if(n.databases)return(await n.databases()).find(s=>s.name===e)||0;const t=n.open(e);return new Promise(function(r,i){t.onsuccess=()=>{t.result.close(),r({name:e,version:t.result.version})},t.onupgradeneeded=s=>{s.target.transaction.abort(),r(0)},t.onerror=()=>{i(new Error("IndexedDB error"))},t.onblocked=()=>{r({name:e,version:t.result.version})}})}dt.showDatabases=function(e,n){if(!indexedDB.databases){n(null,new Error("SHOW DATABASE is not supported in this browser"));return}indexedDB.databases().then(t=>{const r=[],i=e&&new RegExp(e.value.replace(/\%/g,".*"),"g");for(var s=0;s<t.length;s++)(!e||t[s].name.match(i))&&r.push({databaseid:t[s].name});n(r)})},dt.createDatabase=async function(e,n,t,r,i){if(await E3(e).catch(u=>{throw i&&i(null,u),u}))if(t)i&&i(0);else{const u=new Error(`IndexedDB: Cannot create new database "${e}" because it already exists`);i&&i(null,u)}else{const u=indexedDB.open(e,1);u.onsuccess=()=>{u.result.close(),i(1)}}},dt.dropDatabase=async function(e,n,t){if(await E3(e).catch(i=>{throw t&&t(null,i),i})){const i=indexedDB.deleteDatabase(e);i.onsuccess=()=>{t&&t(1)}}else n?t&&t(0):t&&t(null,new Error(`IndexedDB: Cannot drop new database "${e}" because it does not exist'`))},dt.attachDatabase=async function(e,n,t,r,i){if(!await E3(e).catch(g=>{throw i&&i(null,g),g})){const g=new Error(`IndexedDB: Cannot attach database "${e}" because it does not exist`);throw i&&i(null,g),g}const u=await new Promise((g,m)=>{const b=indexedDB.open(e);b.onsuccess=()=>{g(b.result.objectStoreNames),b.result.close()}}),f=new l.Database(n||e);f.engineid="INDEXEDDB",f.ixdbid=e,f.tables=[];for(var h=0;h<u.length;h++)f.tables[u[h]]={};i&&i(1)},dt.createTable=async function(e,n,t,r){const i=l.databases[e].ixdbid,s=await E3(i).catch(f=>{throw r&&r(null,f),f});if(!s){const f=new Error('IndexedDB: Cannot create table in database "'+i+'" because it does not exist');throw r&&r(null,f),f}const u=indexedDB.open(i,s.version+1);u.onupgradeneeded=function(f){u.result.createObjectStore(n,{autoIncrement:!0})},u.onsuccess=function(f){u.result.close(),r&&r(1)},u.onerror=f=>{r(null,f)},u.onblocked=function(f){r(null,new Error(`Cannot create table "${n}" because database "${e}"  is blocked`))}},dt.dropTable=async function(e,n,t,r){const i=l.databases[e].ixdbid,s=await E3(i).catch(h=>{throw r&&r(null,h),h});if(!s){const h=new Error('IndexedDB: Cannot drop table in database "'+i+'" because it does not exist');throw r&&r(null,h),h}const u=indexedDB.open(i,s.version+1);let f;u.onupgradeneeded=function(h){var g=u.result;g.objectStoreNames.contains(n)?(g.deleteObjectStore(n),delete l.databases[e].tables[n]):t||(f=new Error(`IndexedDB: Cannot drop table "${n}" because it does not exist`),h.target.transaction.abort())},u.onsuccess=function(h){u.result.close(),r&&r(1)},u.onerror=function(h){r&&r(null,f||h)},u.onblocked=function(h){r(null,new Error(`Cannot drop table "${n}" because database "${e}" is blocked`))}},dt.intoTable=function(e,n,t,r,i){const s=l.databases[e].ixdbid,u=indexedDB.open(s);var f=l.databases[e],h=f.tables[n];u.onupgradeneeded=g=>{g.target.transaction.abort();const m=new Error(`Cannot insert into table "${n}" because database "${e}" does not exist`);i&&i(null,m)},u.onsuccess=()=>{for(var g=u.result,m=g.transaction([n],"readwrite"),b=m.objectStore(n),c=0,d=t.length;c<d;c++)b.add(t[c]);m.oncomplete=function(){g.close();for(var v in h.afterinsert)if(h.afterinsert[v]){var E=h.afterinsert[v];E.funcid?l.fn[E.funcid](t):E.statement&&E.statement.execute(e)}i&&i(d)}}},dt.fromTable=function(e,n,t,r,i){const s=l.databases[e].ixdbid,u=indexedDB.open(s);u.onupgradeneeded=f=>{f.target.transaction.abort();const h=new Error(`Cannot select from table "${n}" because database "${e}" does not exist`);t&&t(null,h)},u.onsuccess=()=>{const f=[],h=u.result,g=h.transaction([n]).objectStore(n).openCursor();g.onsuccess=()=>{const m=g.result;if(m){const b=typeof m=="object"?m.value:{[m.key]:m.value};f.push(b),m.continue()}else h.close(),t&&t(f,r,i)}}},dt.deleteFromTable=function(e,n,t,r,i){const s=l.databases[e].ixdbid,u=indexedDB.open(s);u.onsuccess=()=>{const f=u.result,h=f.transaction([n],"readwrite").objectStore(n).openCursor();let g=0;h.onsuccess=()=>{var m=h.result;m?((!t||t(m.value,r,l))&&(m.delete(),g++),m.continue()):(f.close(),i&&i(g))}}},dt.updateTable=function(e,n,t,r,i,s){const u=l.databases[e].ixdbid,f=indexedDB.open(u);f.onsuccess=function(){const h=f.result,g=h.transaction([n],"readwrite").objectStore(n).openCursor();let m=0;g.onsuccess=()=>{var b=g.result;if(b){if(!r||r(b.value,i)){var c=b.value;t(c,i),b.update(c),m++}b.continue()}else h.close(),s&&s(m)}}};var S2=l.engines.LOCALSTORAGE=function(){};S2.get=function(e){var n=localStorage.getItem(e);if(!(typeof n>"u")){var t;try{t=JSON.parse(n)}catch{throw new Error("Cannot parse JSON object from localStorage"+n)}return t}},S2.set=function(e,n){typeof n>"u"?localStorage.removeItem(e):localStorage.setItem(e,JSON.stringify(n))},S2.storeTable=function(e,n){var t=l.databases[e],r=t.tables[n],i={};i.columns=r.columns,i.data=r.data,i.identities=r.identities,S2.set(t.lsdbid+"."+n,i)},S2.restoreTable=function(e,n){var t=l.databases[e],r=S2.get(t.lsdbid+"."+n),i=new l.Table;for(var s in r)i[s]=r[s];return t.tables[n]=i,i.indexColumns(),i},S2.removeTable=function(e,n){var t=l.databases[e];localStorage.removeItem(t.lsdbid+"."+n)},S2.createDatabase=function(e,n,t,r,i){var s=1,u=S2.get("alasql");if(t&&u&&u.databases&&u.databases[e])s=0;else{if(u||(u={databases:{}}),u.databases&&u.databases[e])throw new Error('localStorage: Cannot create new database "'+e+'" because it already exists');u.databases[e]=!0,S2.set("alasql",u),S2.set(e,{databaseid:e,tables:{}})}return i&&(s=i(s)),s},S2.dropDatabase=function(e,n,t){var r=1,i=S2.get("alasql");if(n&&i&&i.databases&&!i.databases[e])r=0;else{if(!i){if(n)return t?t(0):0;throw new Error("There is no any AlaSQL databases in localStorage")}if(i.databases&&!i.databases[e])throw new Error('localStorage: Cannot drop database "'+e+'" because there is no such database');delete i.databases[e],S2.set("alasql",i);var s=S2.get(e);for(var u in s.tables)localStorage.removeItem(e+"."+u);localStorage.removeItem(e)}return t&&(r=t(r)),r},S2.attachDatabase=function(e,n,t,r,i){var s=1;if(l.databases[n])throw new Error('Unable to attach database as "'+n+'" because it already exists');n||(n=e);var u=new l.Database(n);if(u.engineid="LOCALSTORAGE",u.lsdbid=e,u.tables=S2.get(e).tables,!l.options.autocommit&&u.tables)for(var f in u.tables)S2.restoreTable(n,f);return i&&(s=i(s)),s},S2.showDatabases=function(e,n){var t=[],r=S2.get("alasql");if(e)var i=new RegExp(e.value.replace(/%/g,".*"),"g");if(r&&r.databases){for(var s in r.databases)t.push({databaseid:s});e&&t&&t.length>0&&(t=t.filter(function(u){return u.databaseid.match(i)}))}return n&&(t=n(t)),t},S2.createTable=function(e,n,t,r){var i=1,s=l.databases[e].lsdbid,u=S2.get(s+"."+n);if(u&&!t)throw new Error('Table "'+n+'" alsready exists in localStorage database "'+s+'"');var f=S2.get(s),h=l.databases[e].tables[n];return f.tables[n]=!0,S2.set(s,f),S2.storeTable(e,n),r&&(i=r(i)),i},S2.truncateTable=function(e,n,t,r){var i=1,s=l.databases[e].lsdbid,u;if(l.options.autocommit?u=S2.get(s):u=l.databases[e],!t&&!u.tables[n])throw new Error('Cannot truncate table "'+n+'" in localStorage, because it does not exist');var f=S2.restoreTable(e,n);return f.data=[],S2.storeTable(e,n),r&&(i=r(i)),i},S2.dropTable=function(e,n,t,r){var i=1,s=l.databases[e].lsdbid,u;if(l.options.autocommit?u=S2.get(s):u=l.databases[e],!t&&!u.tables[n])throw new Error('Cannot drop table "'+n+'" in localStorage, because it does not exist');return delete u.tables[n],S2.set(s,u),S2.removeTable(e,n),r&&(i=r(i)),i},S2.fromTable=function(e,n,t,r,i){var s=l.databases[e].lsdbid,u=S2.restoreTable(e,n).data;return t&&(u=t(u,r,i)),u},S2.intoTable=function(e,n,t,r,i){var s=l.databases[e].lsdbid,u=t.length,f=S2.restoreTable(e,n);for(var h in f.identities){var g=f.identities[h];for(var m in t)t[m][h]=g.value,g.value+=g.step}return f.data||(f.data=[]),f.data=f.data.concat(t),S2.storeTable(e,n),i&&(u=i(u)),u},S2.loadTableData=function(e,n){var t=l.databases[e],r=l.databases[e].lsdbid;S2.restoreTable(e,n)},S2.saveTableData=function(e,n){var t=l.databases[e],r=l.databases[e].lsdbid;S2.storeTable(r,n),t.tables[n].data=void 0},S2.commit=function(e,n){var t=l.databases[e],r=l.databases[e].lsdbid,i={databaseid:r,tables:{}};if(t.tables)for(var s in t.tables)i.tables[s]=!0,S2.storeTable(e,s);return S2.set(r,i),n?n(1):1},S2.begin=S2.commit,S2.rollback=function(e,n){return;var t,r,i;if(i.tables)for(var s in i.tables)S2.restoreTable(e,s)};var g3=l.engines.SQLITE=function(){};g3.createDatabase=function(e,n,t,r,i){throw new Error("Connot create SQLITE database in memory. Attach it.")},g3.dropDatabase=function(e){throw new Error("This is impossible to drop SQLite database. Detach it.")},g3.attachDatabase=function(e,n,t,r,i){var s=1;if(l.databases[n])throw new Error('Unable to attach database as "'+n+'" because it already exists');if(t[0]&&t[0]instanceof T.StringValue||t[0]instanceof T.ParamValue){if(t[0]instanceof T.StringValue)var u=t[0].value;else if(t[0]instanceof T.ParamValue)var u=r[t[0].param];return l.utils.loadBinaryFile(u,!0,function(f){var h=new l.Database(n||e);h.engineid="SQLITE",h.sqldbid=e;var g=h.sqldb=new SQL.Database(f);h.tables=[];var m=g.exec("SELECT * FROM sqlite_master WHERE type='table'")[0].values;m.forEach(function(b){h.tables[b[1]]={};var c=h.tables[b[1]].columns=[],d=l.parse(b[4]),v=d.statements[0].columns;v&&v.length>0&&v.forEach(function(E){c.push(E)})}),i(1)},function(f){throw new Error('Cannot open SQLite database file "'+t[0].value+'"')}),s}else throw new Error("Cannot attach SQLite database without a file");return s},g3.fromTable=function(e,n,t,r,i){var s=l.databases[e].sqldb.exec("SELECT * FROM "+n),u=i.sources[r].columns=[];s[0].columns.length>0&&s[0].columns.forEach(function(h){u.push({columnid:h})});var f=[];s[0].values.length>0&&s[0].values.forEach(function(h){var g={};u.forEach(function(m,b){g[m.columnid]=h[b]}),f.push(g)}),t&&t(f,r,i)},g3.intoTable=function(e,n,t,r,i){for(var s=l.databases[e].sqldb,u=0,f=t.length;u<f;u++){var h="INSERT INTO "+n+" (",g=t[u],m=Object.keys(g);h+=m.join(","),h+=") VALUES (",h+=m.map(function(c){var d=g[c];return typeof d=="string"&&(d="'"+d+"'"),d}).join(","),h+=")",s.exec(h)}var b=f;return i&&i(b),b};var J2=l.engines.FILESTORAGE=l.engines.FILE=function(){};if(J2.createDatabase=function(e,n,t,r,i){var s=1,u=n[0].value;return l.utils.fileExists(u,function(f){if(f){if(t)return s=0,i&&(s=i(s)),s;throw new Error("Cannot create new database file, because it already exists")}else{var h={tables:{}};l.utils.saveFile(u,JSON.stringify(h),function(g){i&&(s=i(s))})}}),s},J2.dropDatabase=function(e,n,t){var r,i="";if(typeof e=="object"&&e.value)i=e.value;else{var s=l.databases[e]||{};i=s.filename||"",delete l.databases[e]}return l.utils.fileExists(i,function(u){if(u)r=1,l.utils.deleteFile(i,function(){r=1,t&&(r=t(r))});else{if(!n)throw new Error("Cannot drop database file, because it does not exist");r=0,t&&(r=t(r))}}),r},J2.attachDatabase=function(e,n,t,r,i){var s=1;if(l.databases[n])throw new Error('Unable to attach database as "'+n+'" because it already exists');var u=new l.Database(n||e);return u.engineid="FILESTORAGE",u.filename=t[0].value,G3(u.filename,!!i,function(f){try{u.data=JSON.parse(f)}catch{throw new Error("Data in FileStorage database are corrupted")}if(u.tables=u.data.tables,!l.options.autocommit&&u.tables)for(var h in u.tables)u.tables[h].data=u.data[h];i&&(s=i(s))}),s},J2.createTable=function(e,n,t,r){var i=l.databases[e],s=i.data[n],u=1;if(s&&!t)throw new Error('Table "'+n+'" alsready exists in the database "'+fsdbid+'"');var f=l.databases[e].tables[n];return i.data.tables[n]={columns:f.columns},i.data[n]=[],J2.updateFile(e),r&&r(u),u},J2.updateFile=function(e){var n=l.databases[e];if(n.issaving){n.postsave=!0;return}n.issaving=!0,n.postsave=!1,l.utils.saveFile(n.filename,JSON.stringify(n.data),function(){n.issaving=!1,n.postsave&&setTimeout(function(){J2.updateFile(e)},50)})},J2.dropTable=function(e,n,t,r){var i=1,s=l.databases[e];if(!t&&!s.tables[n])throw new Error('Cannot drop table "'+n+'" in fileStorage, because it does not exist');return delete s.tables[n],delete s.data.tables[n],delete s.data[n],J2.updateFile(e),r&&r(i),i},J2.fromTable=function(e,n,t,r,i){var s=l.databases[e],u=s.data[n];return t&&(u=t(u,r,i)),u},J2.intoTable=function(e,n,t,r,i){var s=l.databases[e],u=t.length,f=s.data[n];return f||(f=[]),s.data[n]=f.concat(t),J2.updateFile(e),i&&i(u),u},J2.loadTableData=function(e,n){var t=l.databases[e];t.tables[n].data=t.data[n]},J2.saveTableData=function(e,n){var t=l.databases[e];t.data[n]=t.tables[n].data,t.tables[n].data=null,J2.updateFile(e)},J2.commit=function(e,n){var t=l.databases[e],r={tables:{}};if(t.tables)for(var i in t.tables)t.data.tables[i]={columns:t.tables[i].columns},t.data[i]=t.tables[i].data;return J2.updateFile(e),n?n(1):1},J2.begin=J2.commit,J2.rollback=function(e,n){var t=1,r=l.databases[e];r.dbversion++,i();function i(){setTimeout(function(){if(r.issaving)return i();l.loadFile(r.filename,!!n,function(s){r.data=s,r.tables={};for(var u in r.data.tables){var f=new l.Table({columns:r.data.tables[u].columns});V2(f,r.data.tables[u]),r.tables[u]=f,l.options.autocommit||(r.tables[u].data=r.data[u]),r.tables[u].indexColumns()}delete l.databases[e],l.databases[e]=new l.Database(e),V2(l.databases[e],r),l.databases[e].engineid="FILESTORAGE",l.databases[e].filename=r.filename,n&&(t=n(t))})},100)}},M1.isBrowser&&!M1.isWebWorker){if(l=l||!1,!l)throw new Error("alasql was not found");l.worker=function(){throw new Error("Can find webworker in this enviroment")},typeof Worker<"u"&&(l.worker=function(e,n,t){if(e===!0&&(e=void 0),typeof e>"u"){for(var r=document.getElementsByTagName("script"),i=0;i<r.length;i++)if(r[i].src.substr(-16).toLowerCase()==="alasql-worker.js"){e=r[i].src.substr(0,r[i].src.length-16)+"alasql.js";break}else if(r[i].src.substr(-20).toLowerCase()==="alasql-worker.min.js"){e=r[i].src.substr(0,r[i].src.length-20)+"alasql.min.js";break}else if(r[i].src.substr(-9).toLowerCase()==="alasql.js"){e=r[i].src;break}else if(r[i].src.substr(-13).toLowerCase()==="alasql.min.js"){e=r[i].src.substr(0,r[i].src.length-13)+"alasql.min.js";break}}if(typeof e>"u")throw new Error("Path to alasql.js is not specified");if(e!==!1){var s="importScripts('";s+=e,s+="');self.onmessage = function(event) {alasql(event.data.sql,event.data.params, function(data){postMessage({id:event.data.id, data:data});});}";var u=new Blob([s],{type:"text/plain"});if(l.webworker=new Worker(URL.createObjectURL(u)),l.webworker.onmessage=function(h){var g=h.data.id;l.buffer[g](h.data.data),delete l.buffer[g]},l.webworker.onerror=function(h){throw h},arguments.length>1){var f="REQUIRE "+n.map(function(h){return'"'+h+'"'}).join(",");l(f,[],t)}}else if(e===!1){delete l.webworker;return}});/*! @source http://purl.eligrey.com/github/FileSaver.js/blob/master/FileSaver.js */var Qt=Qt||function(e){"use strict";if(!(typeof e>"u"||typeof navigator<"u"&&/MSIE [1-9]\./.test(navigator.userAgent))){var n=e.document,t=function(){return e.URL||e.webkitURL||e},r=n.createElementNS("http://www.w3.org/1999/xhtml","a"),i="download"in r,s=function(R){var A=new MouseEvent("click");R.dispatchEvent(A)},u=/constructor/i.test(e.HTMLElement)||e.safari,f=/CriOS\/[\d]+/.test(navigator.userAgent),h=function(R){(e.setImmediate||e.setTimeout)(function(){throw R},0)},g="application/octet-stream",m=1e3*40,b=function(R){var A=function(){typeof R=="string"?t().revokeObjectURL(R):R.remove()};setTimeout(A,m)},c=function(R,A,N){A=[].concat(A);for(var w=A.length;w--;){var T1=R["on"+A[w]];if(typeof T1=="function")try{T1.call(R,N||R)}catch(v1){h(v1)}}},d=function(R){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(R.type)?new Blob(["\uFEFF",R],{type:R.type}):R},v=function(R,A,N){N||(R=d(R));var w=this,T1=R.type,v1=T1===g,O1,G1=function(){c(w,"writestart progress write writeend".split(" "))},B1=function(){if((f||v1&&u)&&e.FileReader){var Y1=new FileReader;Y1.onloadend=function(){var s2=f?Y1.result:Y1.result.replace(/^data:[^;]*;/,"data:attachment/file;"),K1=e.open(s2,"_blank");K1||(e.location.href=s2),s2=void 0,w.readyState=w.DONE,G1()},Y1.readAsDataURL(R),w.readyState=w.INIT;return}if(O1||(O1=t().createObjectURL(R)),v1)e.location.href=O1;else{var k=e.open(O1,"_blank");k||(e.location.href=O1)}w.readyState=w.DONE,G1(),b(O1)};if(w.readyState=w.INIT,i){O1=t().createObjectURL(R),setTimeout(function(){r.href=O1,r.download=A,s(r),G1(),b(O1),w.readyState=w.DONE});return}B1()},E=v.prototype,O=function(R,A,N){return new v(R,A||R.name||"download",N)};return typeof navigator<"u"&&navigator.msSaveOrOpenBlob?function(R,A,N){return A=A||R.name||"download",N||(R=d(R)),navigator.msSaveOrOpenBlob(R,A)}:(E.abort=function(){},E.readyState=E.INIT=0,E.WRITING=1,E.DONE=2,E.error=E.onwritestart=E.onprogress=E.onwrite=E.onabort=E.onerror=E.onwriteend=null,O)}}(typeof self<"u"&&self||typeof window<"u"&&window||this.content);typeof module<"u"&&module.exports?module.exports.saveAs=Qt:typeof define<"u"&&define!==null&&define.amd!==null&&define("FileSaver.js",function(){return Qt}),(M1.isCordova||M1.isMeteorServer||M1.isNode)&&console.log("It looks like you are using the browser version of AlaSQL. Please use the alasql.fs.js file instead."),l.utils.saveAs=Qt}return new b3("alasql"),l.use("alasql"),l});
