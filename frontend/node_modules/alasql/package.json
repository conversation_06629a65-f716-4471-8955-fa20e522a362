{"name": "alasql", "description": "Use SQL to select and filter javascript data - including relational joins and search in nested objects (JSON). Export to and import from Excel and CSV", "version": "4.6.6", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "main": "dist/alasql.fs.js", "browser": "dist/alasql.min.js", "directories": {"test": "test"}, "typings": "types/alasql.d.ts", "scripts": {"test": "sh build.sh && yarn test-only", "test-ci": "(yarn test-format || 1) && yarn test && yarn install-g && alasql 'select 1 as Succes'", "test-only": "node node_modules/mocha/bin/mocha.js ./test --reporter dot", "#test-only": "(command -v bun && bun node_modules/.bin/mocha ./test --reporter dot) || npx bun node_modules/.bin/mocha ./test --reporter dot", "test-browser": "node test/browserTestRunner.js 7387", "test-cover": "# istanbul cover  -x 'lib/zt/zt.js' --dir test/coverage _mocha", "build": "yarn format && yarn build-only", "build-only": "sh build.sh", "install-g": "yarn build && npm uninstall alasql -g && npm install -g .", "release": "yarn version", "jison": "jison ./src/alasqlparser.jison -o ./src/alasqlparser.js", "fmt": "yarn pretty-commit --write", "format": "yarn pretty-since-dev --write", "format-all": "yarn pretty-all --write", "test-format": "yarn pretty-since-dev --list-different || echo 'Please correct file formatting using `yarn format` and try again.'", "pretty-since-dev": "{ git diff --name-only --diff-filter=d origin/develop ; git diff --name-only --diff-filter=d --staged origin/develop ; } | sort | uniq | grep -vE '^dist/|^test/coverage|^lib/|.min.js$' | grep -E '\\.(scss|css|js|ts|vue|json)$' | xargs npx prettier", "pretty-commit": "{ git diff --name-only  --diff-filter=d ; git diff --name-only  --diff-filter=d --staged ; } | sort | uniq | grep -vE '^dist/|^test/coverage|^lib/|.min.js$' | grep -E '\\.(scss|css|js|ts|vue|json)$' | xargs npx prettier", "pretty-all": "git ls-tree --full-tree --name-only -r HEAD | grep -vE '^dist/|^test/coverage|^lib/|.min.js$' | grep -E '\\.(scss|css|js|ts|vue|json)$' | xargs npx prettier", "push": "git push --force-with-lease && git push --no-verify --tags #", "repush": "yarn rebase && yarn push", "amend": "git reset --soft HEAD~1 && sleep 1 && git add --all && git commit --file .git/COMMIT_EDITMSG # This works with husky hooks", "commit": "cmdmix 'git add --all && git commit -am \"%1\"'", "add": "git add --all", "goto": "git fetch && git checkout", "todo": "git ls-tree --full-tree --name-only -r head | xargs grep -inEro '\\Wtodo[ :].*' #", "fresh": "cmdmix 'yarn goto '%1' && yarn pull-hard'", "pre-pr": "cmdmix 'yarn fresh '%1' && yarn repush'", "prepare": "husky install", "preversion": "yarn && yarn test", "postversion": "npm publish && git push && git push --tags && echo \"Successfully released version $npm_package_version\""}, "dependencies": {"cross-fetch": "4.1.0", "yargs": "16"}, "devDependencies": {"blueimp-md5": "2.19.0", "cmdmix": "2.2.2", "dom-storage": "2.1.0", "esbuild": "0.25.2", "git-branch-is": "4.0.0", "husky": "9.1.7", "jison": "^0.4.18", "mocha": "11.1.0", "mocha.parallel": "0.15.6", "open": "10.1.0", "prettier": "3.5.3", "react-native-fetch-blob": "^0.10.8", "react-native-fs": "^2.20.0", "rexreplace": "7.1.12", "strftime": "0.10.3", "tabletop": "1.6.3", "uglify-js": "3.19.3"}, "resolutions": {"got": "14", "axios": "1", "json5": "2", "underscore": "1", "glob-parent": "6", "decode-uri-component": "0.4", "semver": "7", "follow-redirects": "1"}, "engines": {"node": ">=15"}, "repository": {"type": "git", "url": "http://github.com/alasql/alasql.git"}, "bugs": {"url": "https://github.com/alasql/alasql/issues"}, "bin": {"alasql": "./bin/alasql-cli.js"}, "homepage": "https://github.com/alasql/alasql", "keywords": ["SQL", "javascript", "database", "Excel", "XLSX", "XLS", "CSV"], "license": "MIT", "prettier": {"useTabs": true, "printWidth": 100, "singleQuote": true, "arrowParens": "avoid", "trailingComma": "es5", "bracketSpacing": false}, "husky": {"hooks": {"pre-push": "yarn test-format || (echo please format using 'yarn format' && exit 1)"}}}