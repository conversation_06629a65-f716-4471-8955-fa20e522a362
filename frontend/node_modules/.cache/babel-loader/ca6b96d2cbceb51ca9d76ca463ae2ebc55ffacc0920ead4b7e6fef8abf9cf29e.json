{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { GetTicketDetails, GetProcessMasterByAPI, GetSalesTicketProcessUser, ReAssignSalesTicket, AssignSalesTicket, UploadFile, UpdateTicketRemarks, GetSalesTicketLog, GetDocumentUrl } from '../services/feedbackService';\nimport './TicketDetails.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TicketDetails = () => {\n  _s();\n  const {\n    ticketId\n  } = useParams();\n  const [ticketDetails, setTicketDetails] = useState(null);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [hrComments, setHRComments] = useState('');\n  const [isSupport, setIsSupport] = useState(null);\n  const [activeTab, setActiveTab] = useState(1);\n  const [statusList, setStatusList] = useState([]);\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: {\n      SourceID: 0\n    },\n    Spoc: undefined\n  });\n  const [sourceList, setSourceList] = useState([]);\n  const [spocList, setSpocList] = useState([]);\n  const [updateAssignmentFlag, setUpdateAssignmentFlag] = useState(0);\n  const [logList, setLogList] = useState([]);\n  const [fileAttachments, setFileAttachments] = useState([]);\n  useEffect(() => {\n    fetchInitialData();\n  }, [ticketId]);\n  const fetchInitialData = async () => {\n    await getTicketDetails();\n    await getProcessList();\n    await getStatusMaster();\n  };\n  const getTicketDetails = async () => {\n    const res = await GetTicketDetails({\n      ticketId\n    });\n    if (res) {\n      setTicketDetails(res);\n      setCommentList(res.Commentlist || []);\n      setSelected(prev => ({\n        ...prev,\n        Status: {\n          StatusID: res.StatusID\n        },\n        IssueType: {\n          ISSUEID: res.IssueID\n        },\n        Source: {\n          SourceID: res.ProcessID\n        }\n      }));\n      fetchTicketLog(res.TicketID);\n    }\n  };\n  const getProcessList = async () => {\n    const data = await GetProcessMasterByAPI();\n    if (data) {\n      setSourceList([{\n        Name: 'Select',\n        SourceID: 0\n      }, ...data]);\n    }\n  };\n  const getStatusMaster = async () => {\n    const list = [{\n      StatusID: 1,\n      StatusName: 'New'\n    }, {\n      StatusID: 2,\n      StatusName: 'InProgress'\n    }, {\n      StatusID: 3,\n      StatusName: 'Resolved'\n    }, {\n      StatusID: 5,\n      StatusName: 'Reopen'\n    }];\n    setStatusList(list);\n  };\n  const fetchTicketLog = async ticketId => {\n    const res = await GetSalesTicketLog({\n      ticketId,\n      userId: 0,\n      logtype: 0\n    });\n    if (res) setLogList(res);\n  };\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    const uploads = [];\n    files.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = event => {\n        uploads.push({\n          FileName: file.name,\n          AttachemntContent: btoa(event.target.result),\n          AttachmentURL: '',\n          ContentType: file.type\n        });\n        if (uploads.length === files.length) {\n          setFileAttachments(uploads);\n        }\n      };\n      reader.readAsBinaryString(file);\n    });\n  };\n  const updateRemarks = async replyType => {\n    var _selected$Status, _selected$Status2;\n    if (!ticketReply || ticketReply.length <= 10) {\n      alert('Query should be more than 10 characters.');\n      return;\n    }\n    const request = {\n      TicketID: ticketId,\n      Comments: ticketReply,\n      StatusID: (_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID,\n      CreatedBy: 0,\n      ReplyType: replyType,\n      FileURL: '',\n      FileName: '',\n      IsStatusChanged: ticketDetails.StatusID !== ((_selected$Status2 = selected.Status) === null || _selected$Status2 === void 0 ? void 0 : _selected$Status2.StatusID)\n    };\n    if (fileAttachments.length > 0) {\n      const res = await UploadFile(fileAttachments);\n      if ((res === null || res === void 0 ? void 0 : res.length) > 0) {\n        request.FileURL = res[0].AttachmentURL;\n        request.FileName = res[0].FileName;\n      }\n    }\n    const result = await UpdateTicketRemarks(request);\n    if (result) {\n      alert('Updated successfully');\n      setTicketReply('');\n      getTicketDetails();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ticket-details\",\n    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Ticket Details\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(TicketDetails, \"HaMtnwN0PB6+SViIfsaVqVs6EVw=\", false, function () {\n  return [useParams];\n});\n_c = TicketDetails;\nexport default TicketDetails;\nvar _c;\n$RefreshReg$(_c, \"TicketDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "GetTicketDetails", "GetProcessMasterByAPI", "GetSalesTicketProcessUser", "ReAssignSalesTicket", "AssignSalesTicket", "UploadFile", "UpdateTicketRemarks", "GetSalesTicketLog", "GetDocumentUrl", "jsxDEV", "_jsxDEV", "TicketDetails", "_s", "ticketId", "ticketDetails", "setTicketDetails", "commentList", "setCommentList", "ticketReply", "setTicketReply", "hrComments", "setHRComments", "isSupport", "setIsSupport", "activeTab", "setActiveTab", "statusList", "setStatusList", "selected", "setSelected", "Status", "undefined", "IssueType", "SubIssueType", "Source", "SourceID", "Spoc", "sourceList", "setSourceList", "spocList", "setSpocList", "updateAssignmentFlag", "setUpdateAssignmentFlag", "logList", "setLogList", "fileAttachments", "setFileAttachments", "fetchInitialData", "getTicketDetails", "getProcessList", "getStatusMaster", "res", "Commentlist", "prev", "StatusID", "ISSUEID", "IssueID", "ProcessID", "fetchTicketLog", "TicketID", "data", "Name", "list", "StatusName", "userId", "logtype", "handleFileChange", "e", "files", "Array", "from", "target", "uploads", "for<PERSON>ach", "file", "reader", "FileReader", "onload", "event", "push", "FileName", "name", "AttachemntContent", "btoa", "result", "AttachmentURL", "ContentType", "type", "length", "readAsBinaryString", "updateRemarks", "replyType", "_selected$Status", "_selected$Status2", "alert", "request", "Comments", "CreatedBy", "ReplyType", "FileURL", "IsStatusChanged", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  GetTicketDetails,\n  GetProcessMasterByAPI,\n  GetSalesTicketProcessUser,\n  ReAssignSalesTicket,\n  AssignSalesTicket,\n  UploadFile,\n  UpdateTicketRemarks,\n  GetSalesTicketLog,\n  GetDocumentUrl,\n} from '../services/feedbackService';\nimport './TicketDetails.css';\n\nconst TicketDetails = () => {\n  const { ticketId } = useParams();\n\n  const [ticketDetails, setTicketDetails] = useState(null);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [hrComments, setHRComments] = useState('');\n  const [isSupport, setIsSupport] = useState(null);\n  const [activeTab, setActiveTab] = useState(1);\n  const [statusList, setStatusList] = useState([]);\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: { SourceID: 0 },\n    Spoc: undefined,\n  });\n  const [sourceList, setSourceList] = useState([]);\n  const [spocList, setSpocList] = useState([]);\n  const [updateAssignmentFlag, setUpdateAssignmentFlag] = useState(0);\n  const [logList, setLogList] = useState([]);\n  const [fileAttachments, setFileAttachments] = useState([]);\n\n  useEffect(() => {\n    fetchInitialData();\n  }, [ticketId]);\n\n  const fetchInitialData = async () => {\n    await getTicketDetails();\n    await getProcessList();\n    await getStatusMaster();\n  };\n\n  const getTicketDetails = async () => {\n    const res = await GetTicketDetails({ ticketId });\n    if (res) {\n      setTicketDetails(res);\n      setCommentList(res.Commentlist || []);\n      setSelected((prev) => ({\n        ...prev,\n        Status: { StatusID: res.StatusID },\n        IssueType: { ISSUEID: res.IssueID },\n        Source: { SourceID: res.ProcessID },\n      }));\n      fetchTicketLog(res.TicketID);\n    }\n  };\n\n  const getProcessList = async () => {\n    const data = await GetProcessMasterByAPI();\n    if (data) {\n      setSourceList([{ Name: 'Select', SourceID: 0 }, ...data]);\n    }\n  };\n\n  const getStatusMaster = async () => {\n    const list = [\n      { StatusID: 1, StatusName: 'New' },\n      { StatusID: 2, StatusName: 'InProgress' },\n      { StatusID: 3, StatusName: 'Resolved' },\n      { StatusID: 5, StatusName: 'Reopen' },\n    ];\n    setStatusList(list);\n  };\n\n  const fetchTicketLog = async (ticketId) => {\n    const res = await GetSalesTicketLog({ ticketId, userId: 0, logtype: 0 });\n    if (res) setLogList(res);\n  };\n\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    const uploads = [];\n    files.forEach((file) => {\n      const reader = new FileReader();\n      reader.onload = (event) => {\n        uploads.push({\n          FileName: file.name,\n          AttachemntContent: btoa(event.target.result),\n          AttachmentURL: '',\n          ContentType: file.type,\n        });\n        if (uploads.length === files.length) {\n          setFileAttachments(uploads);\n        }\n      };\n      reader.readAsBinaryString(file);\n    });\n  };\n\n  const updateRemarks = async (replyType) => {\n    if (!ticketReply || ticketReply.length <= 10) {\n      alert('Query should be more than 10 characters.');\n      return;\n    }\n\n    const request = {\n      TicketID: ticketId,\n      Comments: ticketReply,\n      StatusID: selected.Status?.StatusID,\n      CreatedBy: 0,\n      ReplyType: replyType,\n      FileURL: '',\n      FileName: '',\n      IsStatusChanged: ticketDetails.StatusID !== selected.Status?.StatusID,\n    };\n\n    if (fileAttachments.length > 0) {\n      const res = await UploadFile(fileAttachments);\n      if (res?.length > 0) {\n        request.FileURL = res[0].AttachmentURL;\n        request.FileName = res[0].FileName;\n      }\n    }\n\n    const result = await UpdateTicketRemarks(request);\n    if (result) {\n      alert('Updated successfully');\n      setTicketReply('');\n      getTicketDetails();\n    }\n  };\n\n  return (\n    <div className=\"ticket-details\">\n      <h2>Ticket Details</h2>\n      {/* TODO: Implement tabs, assignment section, comment timeline, log table */}\n    </div>\n  );\n};\n\nexport default TicketDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,gBAAgB,EAChBC,qBAAqB,EACrBC,yBAAyB,EACzBC,mBAAmB,EACnBC,iBAAiB,EACjBC,UAAU,EACVC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,QACT,6BAA6B;AACpC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAS,CAAC,GAAGd,SAAS,CAAC,CAAC;EAEhC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IACvCiC,MAAM,EAAEC,SAAS;IACjBC,SAAS,EAAED,SAAS;IACpBE,YAAY,EAAEF,SAAS;IACvBG,MAAM,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAC;IACvBC,IAAI,EAAEL;EACR,CAAC,CAAC;EACF,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACdiD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAClC,QAAQ,CAAC,CAAC;EAEd,MAAMkC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMC,gBAAgB,CAAC,CAAC;IACxB,MAAMC,cAAc,CAAC,CAAC;IACtB,MAAMC,eAAe,CAAC,CAAC;EACzB,CAAC;EAED,MAAMF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMG,GAAG,GAAG,MAAMnD,gBAAgB,CAAC;MAAEa;IAAS,CAAC,CAAC;IAChD,IAAIsC,GAAG,EAAE;MACPpC,gBAAgB,CAACoC,GAAG,CAAC;MACrBlC,cAAc,CAACkC,GAAG,CAACC,WAAW,IAAI,EAAE,CAAC;MACrCvB,WAAW,CAAEwB,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPvB,MAAM,EAAE;UAAEwB,QAAQ,EAAEH,GAAG,CAACG;QAAS,CAAC;QAClCtB,SAAS,EAAE;UAAEuB,OAAO,EAAEJ,GAAG,CAACK;QAAQ,CAAC;QACnCtB,MAAM,EAAE;UAAEC,QAAQ,EAAEgB,GAAG,CAACM;QAAU;MACpC,CAAC,CAAC,CAAC;MACHC,cAAc,CAACP,GAAG,CAACQ,QAAQ,CAAC;IAC9B;EACF,CAAC;EAED,MAAMV,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMW,IAAI,GAAG,MAAM3D,qBAAqB,CAAC,CAAC;IAC1C,IAAI2D,IAAI,EAAE;MACRtB,aAAa,CAAC,CAAC;QAAEuB,IAAI,EAAE,QAAQ;QAAE1B,QAAQ,EAAE;MAAE,CAAC,EAAE,GAAGyB,IAAI,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMV,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMY,IAAI,GAAG,CACX;MAAER,QAAQ,EAAE,CAAC;MAAES,UAAU,EAAE;IAAM,CAAC,EAClC;MAAET,QAAQ,EAAE,CAAC;MAAES,UAAU,EAAE;IAAa,CAAC,EACzC;MAAET,QAAQ,EAAE,CAAC;MAAES,UAAU,EAAE;IAAW,CAAC,EACvC;MAAET,QAAQ,EAAE,CAAC;MAAES,UAAU,EAAE;IAAS,CAAC,CACtC;IACDpC,aAAa,CAACmC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMJ,cAAc,GAAG,MAAO7C,QAAQ,IAAK;IACzC,MAAMsC,GAAG,GAAG,MAAM5C,iBAAiB,CAAC;MAAEM,QAAQ;MAAEmD,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC,CAAC;IACxE,IAAId,GAAG,EAAEP,UAAU,CAACO,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMe,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;IACxC,MAAMI,OAAO,GAAG,EAAE;IAClBJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;MACtB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzBN,OAAO,CAACO,IAAI,CAAC;UACXC,QAAQ,EAAEN,IAAI,CAACO,IAAI;UACnBC,iBAAiB,EAAEC,IAAI,CAACL,KAAK,CAACP,MAAM,CAACa,MAAM,CAAC;UAC5CC,aAAa,EAAE,EAAE;UACjBC,WAAW,EAAEZ,IAAI,CAACa;QACpB,CAAC,CAAC;QACF,IAAIf,OAAO,CAACgB,MAAM,KAAKpB,KAAK,CAACoB,MAAM,EAAE;UACnC1C,kBAAkB,CAAC0B,OAAO,CAAC;QAC7B;MACF,CAAC;MACDG,MAAM,CAACc,kBAAkB,CAACf,IAAI,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAOC,SAAS,IAAK;IAAA,IAAAC,gBAAA,EAAAC,iBAAA;IACzC,IAAI,CAAC3E,WAAW,IAAIA,WAAW,CAACsE,MAAM,IAAI,EAAE,EAAE;MAC5CM,KAAK,CAAC,0CAA0C,CAAC;MACjD;IACF;IAEA,MAAMC,OAAO,GAAG;MACdpC,QAAQ,EAAE9C,QAAQ;MAClBmF,QAAQ,EAAE9E,WAAW;MACrBoC,QAAQ,GAAAsC,gBAAA,GAAEhE,QAAQ,CAACE,MAAM,cAAA8D,gBAAA,uBAAfA,gBAAA,CAAiBtC,QAAQ;MACnC2C,SAAS,EAAE,CAAC;MACZC,SAAS,EAAEP,SAAS;MACpBQ,OAAO,EAAE,EAAE;MACXnB,QAAQ,EAAE,EAAE;MACZoB,eAAe,EAAEtF,aAAa,CAACwC,QAAQ,OAAAuC,iBAAA,GAAKjE,QAAQ,CAACE,MAAM,cAAA+D,iBAAA,uBAAfA,iBAAA,CAAiBvC,QAAQ;IACvE,CAAC;IAED,IAAIT,eAAe,CAAC2C,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMrC,GAAG,GAAG,MAAM9C,UAAU,CAACwC,eAAe,CAAC;MAC7C,IAAI,CAAAM,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEqC,MAAM,IAAG,CAAC,EAAE;QACnBO,OAAO,CAACI,OAAO,GAAGhD,GAAG,CAAC,CAAC,CAAC,CAACkC,aAAa;QACtCU,OAAO,CAACf,QAAQ,GAAG7B,GAAG,CAAC,CAAC,CAAC,CAAC6B,QAAQ;MACpC;IACF;IAEA,MAAMI,MAAM,GAAG,MAAM9E,mBAAmB,CAACyF,OAAO,CAAC;IACjD,IAAIX,MAAM,EAAE;MACVU,KAAK,CAAC,sBAAsB,CAAC;MAC7B3E,cAAc,CAAC,EAAE,CAAC;MAClB6B,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;EAED,oBACEtC,OAAA;IAAK2F,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7B5F,OAAA;MAAA4F,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEpB,CAAC;AAEV,CAAC;AAAC9F,EAAA,CAjIID,aAAa;EAAA,QACIZ,SAAS;AAAA;AAAA4G,EAAA,GAD1BhG,aAAa;AAmInB,eAAeA,aAAa;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}