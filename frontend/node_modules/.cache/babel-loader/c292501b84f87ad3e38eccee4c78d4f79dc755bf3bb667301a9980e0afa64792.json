{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js\";\nimport React from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"meta\", {\n        charSet: \"utf-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"viewport\",\n        content: \"width=device-width, initial-scale=1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"theme-color\",\n        content: \"#000000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Matrix Feedback System\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"link\", {\n        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\",\n        rel: \"stylesheet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-loader-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loader\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please Re-Login Matrix and close this tab.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoginPage", "children", "charSet", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "href", "rel", "className", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet';\n\nconst LoginPage = () => {\n\n    return (\n        <>\n            <Helmet>\n                <meta charSet=\"utf-8\" />\n                <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n                <meta name=\"theme-color\" content=\"#000000\" />\n                <meta name=\"description\" content=\"Matrix Feedback System\" />\n                <link\n                    href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    rel=\"stylesheet\"\n                />\n                <title>Login</title>\n            </Helmet>\n            <div className=\"page-loader-wrapper\">\n                <div className=\"loader\">\n                    <p>Please Re-Login Matrix and close this tab.</p>\n                </div>\n            </div>\n        </>\n    );\n};\n\nexport default LoginPage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAEpB,oBACIH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACIJ,OAAA,CAACF,MAAM;MAAAM,QAAA,gBACHJ,OAAA;QAAMK,OAAO,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxBT,OAAA;QAAMU,IAAI,EAAC,UAAU;QAACC,OAAO,EAAC;MAAqC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtET,OAAA;QAAMU,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CT,OAAA;QAAMU,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAwB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DT,OAAA;QACIY,IAAI,EAAC,2EAA2E;QAChFC,GAAG,EAAC;MAAY;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFT,OAAA;QAAAI,QAAA,EAAO;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eACTT,OAAA;MAAKc,SAAS,EAAC,qBAAqB;MAAAV,QAAA,eAChCJ,OAAA;QAAKc,SAAS,EAAC,QAAQ;QAAAV,QAAA,eACnBJ,OAAA;UAAAI,QAAA,EAAG;QAA0C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAACM,EAAA,GAtBIZ,SAAS;AAwBf,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}