{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport api from '../../services/api';\nimport { GetSalesTicketUserDetails } from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const {\n    token,\n    type,\n    ticketId\n  } = useParams();\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const handleUserDetails = data => {\n    if (data.error) {\n      setError(data.message);\n      window.Location.href = './public/index.html';\n      return false;\n    } else if (!data || data.length === 0 || !data.EMPData) {\n      window.Location.href = './public/index.html';\n      return false;\n    }\n    localStorage.setItem('UserDetails', JSON.stringify({\n      \"EMPData\": data.EMPData,\n      \"Token\": data.token,\n      \"IsLocSet\": 1,\n      \"Location\": data.Location,\n      \"Issue\": {\n        \"IssueID\": 0,\n        \"SubIssueID\": 0\n      }\n    }));\n    const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n    const BU = userDetails.EMPData[0].BU;\n    if (BU === 0) {\n      navigate('/editprofile');\n    } else if (type === \"ticket\") {\n      navigate('/createFeedBack/1/4/ticket');\n    } else if (type === \"payment\") {\n      navigate(`/createFeedBack/3/41/ticket/${leadId}/${payId}/${OrderId}/${parentId}`);\n    } else if (type === \"jag\") {\n      navigate('/createFeedBack/1/33/ticket');\n    } else if (type === \"incentive\") {\n      navigate('/createFeedBack/1/4/ticket');\n    } else if (type === \"notification\") {\n      navigate(`/MyTicketDetails/${ticketId}`);\n    } else if (type === \"ticketview\") {\n      navigate(`/TicketDetails/${token}`);\n    } else if (type === \"MyFeedBack\") {\n      navigate(`/MyFeedBackDetails/${token}`);\n    } else {\n      navigate('/myFeedBack');\n    }\n    return true;\n  };\n  useEffect(() => {\n    try {\n      GetSalesTicketUserDetails().then(response => {\n        handleUserDetails(response);\n      });\n    } catch (err) {\n      setError(err.message || 'Authentication failed');\n      setTimeout(() => navigate('/login'), 3000);\n    }\n  }, [token, type, ticketId, navigate]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      minHeight: \"100vh\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        gutterBottom: true,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Redirecting to login page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    minHeight: \"100vh\",\n    children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mt: 2\n      },\n      children: \"Please wait...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 9\n  }, this);\n};\n_s(LandingPage, \"O6JDog6Q09NYsJS5yTiqIg9+c90=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useNavigate", "Box", "CircularProgress", "Typography", "api", "GetSalesTicketUserDetails", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "token", "type", "ticketId", "navigate", "error", "setError", "handleUserDetails", "data", "message", "window", "Location", "href", "length", "EMPData", "localStorage", "setItem", "JSON", "stringify", "userDetails", "parse", "getItem", "BU", "leadId", "payId", "OrderId", "parentId", "then", "response", "err", "setTimeout", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "color", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport api from '../../services/api';\nimport { GetSalesTicketUserDetails } from '../services/feedbackService';\n\nconst LandingPage = () => {\n    const { token, type, ticketId } = useParams();\n    const navigate = useNavigate();\n    const [error, setError] = useState('');\n\n    const handleUserDetails = (data) => {\n        if (data.error) {\n            setError(data.message);\n            window.Location.href = './public/index.html';\n            return false;\n        } \n        else if (!data || data.length === 0 || !data.EMPData) {\n            window.Location.href = './public/index.html';\n            return false;\n        }\n\n        localStorage.setItem('UserDetails', \n            JSON.stringify({ \n                \"EMPData\": data.EMPData, \n                \"Token\": data.token, \n                \"IsLocSet\": 1, \n                \"Location\": data.Location, \n                \"Issue\": { \"IssueID\": 0, \"SubIssueID\": 0 } \n            })\n        );\n        \n        const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n        const BU = userDetails.EMPData[0].BU;\n\n        \n        if (BU === 0) {\n            navigate('/editprofile');\n        }\n        else if (type === \"ticket\") {\n            navigate('/createFeedBack/1/4/ticket');\n        }\n        else if (type === \"payment\") {\n            navigate(`/createFeedBack/3/41/ticket/${leadId}/${payId}/${OrderId}/${parentId}`);\n        }\n        else if (type === \"jag\") {\n            navigate('/createFeedBack/1/33/ticket');\n        }\n        else if (type === \"incentive\") {\n            navigate('/createFeedBack/1/4/ticket');\n        }\n        else if (type === \"notification\") {\n            navigate(`/MyTicketDetails/${ticketId}`);\n        }\n        else if (type === \"ticketview\") {\n            navigate(`/TicketDetails/${token}`);\n        }\n        else if (type === \"MyFeedBack\") {\n            navigate(`/MyFeedBackDetails/${token}`);\n        }\n        else {\n            navigate('/myFeedBack');\n        }\n\n        return true;\n    };\n\n    useEffect(() => {\n        try {\n            GetSalesTicketUserDetails()\n            .then((response) => {\n                handleUserDetails(response);\n            })\n        } catch (err) {\n            setError(err.message || 'Authentication failed');\n            setTimeout(() => navigate('/login'), 3000);\n        }\n    }, [token, type, ticketId, navigate]);\n\n    if (error) {\n        return (\n            <Box\n                display=\"flex\"\n                flexDirection=\"column\"\n                alignItems=\"center\"\n                justifyContent=\"center\"\n                minHeight=\"100vh\"\n            >\n                <Typography variant=\"h6\" color=\"error\" gutterBottom>\n                    {error}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                    Redirecting to login page...\n                </Typography>\n            </Box>\n        );\n    }\n\n    return (\n        <Box\n            display=\"flex\"\n            flexDirection=\"column\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            minHeight=\"100vh\"\n        >\n            <CircularProgress />\n            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                Please wait...\n            </Typography>\n        </Box>\n    );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACjE,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,yBAAyB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGb,SAAS,CAAC,CAAC;EAC7C,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMkB,iBAAiB,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACH,KAAK,EAAE;MACZC,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACtBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,qBAAqB;MAC5C,OAAO,KAAK;IAChB,CAAC,MACI,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,CAAC,IAAI,CAACL,IAAI,CAACM,OAAO,EAAE;MAClDJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,qBAAqB;MAC5C,OAAO,KAAK;IAChB;IAEAG,YAAY,CAACC,OAAO,CAAC,aAAa,EAC9BC,IAAI,CAACC,SAAS,CAAC;MACX,SAAS,EAAEV,IAAI,CAACM,OAAO;MACvB,OAAO,EAAEN,IAAI,CAACP,KAAK;MACnB,UAAU,EAAE,CAAC;MACb,UAAU,EAAEO,IAAI,CAACG,QAAQ;MACzB,OAAO,EAAE;QAAE,SAAS,EAAE,CAAC;QAAE,YAAY,EAAE;MAAE;IAC7C,CAAC,CACL,CAAC;IAED,MAAMQ,WAAW,GAAGF,IAAI,CAACG,KAAK,CAACL,YAAY,CAACM,OAAO,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMC,EAAE,GAAGH,WAAW,CAACL,OAAO,CAAC,CAAC,CAAC,CAACQ,EAAE;IAGpC,IAAIA,EAAE,KAAK,CAAC,EAAE;MACVlB,QAAQ,CAAC,cAAc,CAAC;IAC5B,CAAC,MACI,IAAIF,IAAI,KAAK,QAAQ,EAAE;MACxBE,QAAQ,CAAC,4BAA4B,CAAC;IAC1C,CAAC,MACI,IAAIF,IAAI,KAAK,SAAS,EAAE;MACzBE,QAAQ,CAAC,+BAA+BmB,MAAM,IAAIC,KAAK,IAAIC,OAAO,IAAIC,QAAQ,EAAE,CAAC;IACrF,CAAC,MACI,IAAIxB,IAAI,KAAK,KAAK,EAAE;MACrBE,QAAQ,CAAC,6BAA6B,CAAC;IAC3C,CAAC,MACI,IAAIF,IAAI,KAAK,WAAW,EAAE;MAC3BE,QAAQ,CAAC,4BAA4B,CAAC;IAC1C,CAAC,MACI,IAAIF,IAAI,KAAK,cAAc,EAAE;MAC9BE,QAAQ,CAAC,oBAAoBD,QAAQ,EAAE,CAAC;IAC5C,CAAC,MACI,IAAID,IAAI,KAAK,YAAY,EAAE;MAC5BE,QAAQ,CAAC,kBAAkBH,KAAK,EAAE,CAAC;IACvC,CAAC,MACI,IAAIC,IAAI,KAAK,YAAY,EAAE;MAC5BE,QAAQ,CAAC,sBAAsBH,KAAK,EAAE,CAAC;IAC3C,CAAC,MACI;MACDG,QAAQ,CAAC,aAAa,CAAC;IAC3B;IAEA,OAAO,IAAI;EACf,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACZ,IAAI;MACAQ,yBAAyB,CAAC,CAAC,CAC1B+B,IAAI,CAAEC,QAAQ,IAAK;QAChBrB,iBAAiB,CAACqB,QAAQ,CAAC;MAC/B,CAAC,CAAC;IACN,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVvB,QAAQ,CAACuB,GAAG,CAACpB,OAAO,IAAI,uBAAuB,CAAC;MAChDqB,UAAU,CAAC,MAAM1B,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACH,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAErC,IAAIC,KAAK,EAAE;IACP,oBACIP,OAAA,CAACN,GAAG;MACAuC,OAAO,EAAC,MAAM;MACdC,aAAa,EAAC,QAAQ;MACtBC,UAAU,EAAC,QAAQ;MACnBC,cAAc,EAAC,QAAQ;MACvBC,SAAS,EAAC,OAAO;MAAAC,QAAA,gBAEjBtC,OAAA,CAACJ,UAAU;QAAC2C,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAACC,YAAY;QAAAH,QAAA,EAC9C/B;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACb7C,OAAA,CAACJ,UAAU;QAAC2C,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,oBACI7C,OAAA,CAACN,GAAG;IACAuC,OAAO,EAAC,MAAM;IACdC,aAAa,EAAC,QAAQ;IACtBC,UAAU,EAAC,QAAQ;IACnBC,cAAc,EAAC,QAAQ;IACvBC,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEjBtC,OAAA,CAACL,gBAAgB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpB7C,OAAA,CAACJ,UAAU;MAAC2C,OAAO,EAAC,IAAI;MAACO,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAAC;IAExC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAAC3C,EAAA,CA1GID,WAAW;EAAA,QACqBT,SAAS,EAC1BC,WAAW;AAAA;AAAAuD,EAAA,GAF1B/C,WAAW;AA4GjB,eAAeA,WAAW;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}