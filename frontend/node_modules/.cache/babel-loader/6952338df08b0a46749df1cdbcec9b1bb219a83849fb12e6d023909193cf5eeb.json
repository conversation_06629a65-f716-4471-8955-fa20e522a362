{"ast": null, "code": "import { CALL_API } from \"./api.service\";\nexport const GetSalesTicketCount = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetSalesTicketCount/${requestData.type}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c = GetSalesTicketCount;\nexport const GetSalesTicketByAgentId = reqData => {\n  const input = {\n    url: `api/SalesTicket/GetSalesTicketByAgentId/${reqData.type}?status=${reqData.status}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c2 = GetSalesTicketByAgentId;\nexport const GetTicketDetails = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetSalesTicketDetailsByID/${requestData.ticketId}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c3 = GetTicketDetails;\nexport const UploadFile = requestData => {\n  const input = {\n    url: `api/SalesTicket/UploadFile`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c4 = UploadFile;\nexport const UpdateTicketRemarks = requestData => {\n  const input = {\n    url: `api/SalesTicket/UpdateSalesTicketByID`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c5 = UpdateTicketRemarks;\nexport const GetDocumentUrl = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetDocumentUrl`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c6 = GetDocumentUrl;\nexport const GetProcessMasterByAPI = () => {\n  const input = {\n    url: `api/SalesTicket/GetProcessMaster`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c7 = GetProcessMasterByAPI;\nexport const GetAllIssueSubIssue = () => {\n  const input = {\n    url: `api/SalesTicket/GetAllIssueSubIssue`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c8 = GetAllIssueSubIssue;\nexport const CreateNewTicket = requestData => {\n  const input = {\n    url: `api/SalesTicket/CreateTicket`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c9 = CreateNewTicket;\nexport const GetAdminTicketList = requestData => {\n  const input = {\n    url: 'api/SalesTicket/SalesTicketDashboardSearch',\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c0 = GetAdminTicketList;\nexport const getStatusMaster = () => {\n  const input = {\n    url: 'api/SalesTicket/GetSalesTicketStatusMaster',\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\nexport const GetSpanCreatedTickets = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetSpanCreatedTickets/${requestData.type}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c1 = GetSpanCreatedTickets;\nexport const GetSalesTicketProcessUser = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetSalesTicketProcessUser/${requestData.ticketId}/${requestData.ProcessId}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c10 = GetSalesTicketProcessUser;\nexport const AssignSalesTicket = requestData => {\n  const input = {\n    url: `api/SalesTicket/AssignSalesTicket`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c11 = AssignSalesTicket;\nexport const ReAssignSalesTicket = requestData => {\n  const input = {\n    url: `api/SalesTicket/ReAssignSalesTicket`,\n    method: 'POST',\n    requestData\n  };\n  return CALL_API(input);\n};\n_c12 = ReAssignSalesTicket;\nexport const GetSalesTicketLog = requestData => {\n  const input = {\n    url: `api/SalesTicket/GetSalesTicketLog/${requestData.ticketId}/${requestData.logtype}`,\n    method: 'GET'\n  };\n  return CALL_API(input);\n};\n_c13 = GetSalesTicketLog;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"GetSalesTicketCount\");\n$RefreshReg$(_c2, \"GetSalesTicketByAgentId\");\n$RefreshReg$(_c3, \"GetTicketDetails\");\n$RefreshReg$(_c4, \"UploadFile\");\n$RefreshReg$(_c5, \"UpdateTicketRemarks\");\n$RefreshReg$(_c6, \"GetDocumentUrl\");\n$RefreshReg$(_c7, \"GetProcessMasterByAPI\");\n$RefreshReg$(_c8, \"GetAllIssueSubIssue\");\n$RefreshReg$(_c9, \"CreateNewTicket\");\n$RefreshReg$(_c0, \"GetAdminTicketList\");\n$RefreshReg$(_c1, \"GetSpanCreatedTickets\");\n$RefreshReg$(_c10, \"GetSalesTicketProcessUser\");\n$RefreshReg$(_c11, \"AssignSalesTicket\");\n$RefreshReg$(_c12, \"ReAssignSalesTicket\");\n$RefreshReg$(_c13, \"GetSalesTicketLog\");", "map": {"version": 3, "names": ["CALL_API", "GetSalesTicketCount", "requestData", "input", "url", "type", "method", "_c", "GetSalesTicketByAgentId", "reqData", "status", "_c2", "GetTicketDetails", "ticketId", "_c3", "UploadFile", "_c4", "UpdateTicketRemarks", "_c5", "GetDocumentUrl", "_c6", "GetProcessMasterByAPI", "_c7", "GetAllIssueSubIssue", "_c8", "CreateNewTicket", "_c9", "GetAdminTicketList", "_c0", "getStatusMaster", "GetSpanCreatedTickets", "_c1", "GetSalesTicketProcessUser", "ProcessId", "_c10", "AssignSalesTicket", "_c11", "ReAssignSalesTicket", "_c12", "GetSalesTicketLog", "logtype", "_c13", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/feedbackService.js"], "sourcesContent": ["import { CALL_API } from \"./api.service\";\n\nexport const GetSalesTicketCount = (requestData) => {\n\tconst input = {\n        url: `api/SalesTicket/GetSalesTicketCount/${requestData.type}`,\n        method: 'GET'\n    }\n\n\treturn CALL_API(input);\n}\t\t\t\t\t\t\t\t\t\t\t\n\nexport const GetSalesTicketByAgentId = (reqData) => {\n\tconst input = {\n        url: `api/SalesTicket/GetSalesTicketByAgentId/${reqData.type}?status=${reqData.status}`,\n        method: 'GET',\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetTicketDetails = ( requestData) => {\n    const input = {\n        url: `api/SalesTicket/GetSalesTicketDetailsByID/${requestData.ticketId}`,\n        method: 'GET',\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const UploadFile = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/UploadFile`,\n        method: 'POST',\n        requestData\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const UpdateTicketRemarks = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/UpdateSalesTicketByID`,\n        method: 'POST',\n        requestData\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetDocumentUrl = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/GetDocumentUrl`,\n        method: 'POST',\n        requestData\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetProcessMasterByAPI = () => {\n    const input = {\n        url: `api/SalesTicket/GetProcessMaster`,\n        method: 'GET'\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetAllIssueSubIssue = () => {\n    const input = {\n        url: `api/SalesTicket/GetAllIssueSubIssue`,\n        method: 'GET'\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const CreateNewTicket = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/CreateTicket`,\n        method: 'POST',\n        requestData\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetAdminTicketList = (requestData) => {\n    const input = {\n        url: 'api/SalesTicket/SalesTicketDashboardSearch',\n        method: 'POST',\n        requestData\n    }\n    return CALL_API(input);\n};\n\nexport const getStatusMaster = () => {\n    const input = {\n        url: 'api/SalesTicket/GetSalesTicketStatusMaster',\n        method: 'GET'\n    }\n    return CALL_API(input);\n};\n\nexport const GetSpanCreatedTickets = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/GetSpanCreatedTickets/${requestData.type}`,\n        method: 'GET'\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetSalesTicketProcessUser = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/GetSalesTicketProcessUser/${requestData.ticketId}/${requestData.ProcessId}`,\n        method: 'GET'    \n    }\n\n\treturn CALL_API(input);\n}\n\nexport const AssignSalesTicket = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/AssignSalesTicket`,\n        method: 'POST',\n        requestData\n    }\n\n    return CALL_API(input);\n}\n\nexport const ReAssignSalesTicket = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/ReAssignSalesTicket`,\n        method: 'POST',\n        requestData\n    }\n\n\treturn CALL_API(input);\n}\n\nexport const GetSalesTicketLog = (requestData) => {\n    const input = {\n        url: `api/SalesTicket/GetSalesTicketLog/${requestData.ticketId}/${requestData.logtype}`,\n        method: 'GET'\n    }\n\n\treturn CALL_API(input);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AAExC,OAAO,MAAMC,mBAAmB,GAAIC,WAAW,IAAK;EACnD,MAAMC,KAAK,GAAG;IACPC,GAAG,EAAE,uCAAuCF,WAAW,CAACG,IAAI,EAAE;IAC9DC,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAI,EAAA,GAPYN,mBAAmB;AAShC,OAAO,MAAMO,uBAAuB,GAAIC,OAAO,IAAK;EACnD,MAAMN,KAAK,GAAG;IACPC,GAAG,EAAE,2CAA2CK,OAAO,CAACJ,IAAI,WAAWI,OAAO,CAACC,MAAM,EAAE;IACvFJ,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAQ,GAAA,GAPYH,uBAAuB;AASpC,OAAO,MAAMI,gBAAgB,GAAKV,WAAW,IAAK;EAC9C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,6CAA6CF,WAAW,CAACW,QAAQ,EAAE;IACxEP,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAW,GAAA,GAPYF,gBAAgB;AAS7B,OAAO,MAAMG,UAAU,GAAIb,WAAW,IAAK;EACvC,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,4BAA4B;IACjCE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAEJ,OAAOF,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAa,GAAA,GARYD,UAAU;AAUvB,OAAO,MAAME,mBAAmB,GAAIf,WAAW,IAAK;EAChD,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,uCAAuC;IAC5CE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAEJ,OAAOF,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAe,GAAA,GARYD,mBAAmB;AAUhC,OAAO,MAAME,cAAc,GAAIjB,WAAW,IAAK;EAC3C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,gCAAgC;IACrCE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAEJ,OAAOF,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAiB,GAAA,GARYD,cAAc;AAU3B,OAAO,MAAME,qBAAqB,GAAGA,CAAA,KAAM;EACvC,MAAMlB,KAAK,GAAG;IACVC,GAAG,EAAE,kCAAkC;IACvCE,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAmB,GAAA,GAPYD,qBAAqB;AASlC,OAAO,MAAME,mBAAmB,GAAGA,CAAA,KAAM;EACrC,MAAMpB,KAAK,GAAG;IACVC,GAAG,EAAE,qCAAqC;IAC1CE,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAqB,GAAA,GAPYD,mBAAmB;AAShC,OAAO,MAAME,eAAe,GAAIvB,WAAW,IAAK;EAC5C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,8BAA8B;IACnCE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAEJ,OAAOF,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAuB,GAAA,GARYD,eAAe;AAU5B,OAAO,MAAME,kBAAkB,GAAIzB,WAAW,IAAK;EAC/C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,4CAA4C;IACjDE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EACD,OAAOF,QAAQ,CAACG,KAAK,CAAC;AAC1B,CAAC;AAACyB,GAAA,GAPWD,kBAAkB;AAS/B,OAAO,MAAME,eAAe,GAAGA,CAAA,KAAM;EACjC,MAAM1B,KAAK,GAAG;IACVC,GAAG,EAAE,4CAA4C;IACjDE,MAAM,EAAE;EACZ,CAAC;EACD,OAAON,QAAQ,CAACG,KAAK,CAAC;AAC1B,CAAC;AAED,OAAO,MAAM2B,qBAAqB,GAAI5B,WAAW,IAAK;EAClD,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,yCAAyCF,WAAW,CAACG,IAAI,EAAE;IAChEC,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAA4B,GAAA,GAPYD,qBAAqB;AASlC,OAAO,MAAME,yBAAyB,GAAI9B,WAAW,IAAK;EACtD,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,6CAA6CF,WAAW,CAACW,QAAQ,IAAIX,WAAW,CAAC+B,SAAS,EAAE;IACjG3B,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAA+B,IAAA,GAPYF,yBAAyB;AAStC,OAAO,MAAMG,iBAAiB,GAAIjC,WAAW,IAAK;EAC9C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,mCAAmC;IACxCE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAED,OAAOF,QAAQ,CAACG,KAAK,CAAC;AAC1B,CAAC;AAAAiC,IAAA,GARYD,iBAAiB;AAU9B,OAAO,MAAME,mBAAmB,GAAInC,WAAW,IAAK;EAChD,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,qCAAqC;IAC1CE,MAAM,EAAE,MAAM;IACdJ;EACJ,CAAC;EAEJ,OAAOF,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAmC,IAAA,GARYD,mBAAmB;AAUhC,OAAO,MAAME,iBAAiB,GAAIrC,WAAW,IAAK;EAC9C,MAAMC,KAAK,GAAG;IACVC,GAAG,EAAE,qCAAqCF,WAAW,CAACW,QAAQ,IAAIX,WAAW,CAACsC,OAAO,EAAE;IACvFlC,MAAM,EAAE;EACZ,CAAC;EAEJ,OAAON,QAAQ,CAACG,KAAK,CAAC;AACvB,CAAC;AAAAsC,IAAA,GAPYF,iBAAiB;AAAA,IAAAhC,EAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA;AAAAC,YAAA,CAAAnC,EAAA;AAAAmC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAR,IAAA;AAAAQ,YAAA,CAAAN,IAAA;AAAAM,YAAA,CAAAJ,IAAA;AAAAI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}