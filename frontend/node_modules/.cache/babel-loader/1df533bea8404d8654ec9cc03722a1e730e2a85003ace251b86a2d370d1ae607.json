{"ast": null, "code": "import { format, parseISO } from \"date-fns\";\nexport const getCookie = name => {\n  if (document) {\n    name = name || 'token';\n    let nameEQ = name + \"=\";\n    let ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) == ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  }\n};\nexport const getUserId = () => {\n  let MatrixToken = getCookie('MatrixToken');\n  if (MatrixToken) {\n    try {\n      MatrixToken = JSON.parse(atob(MatrixToken));\n      return MatrixToken.UserId;\n    } catch (e) {\n      return getCookie('AgentId');\n    }\n  } else {\n    return getCookie('AgentId');\n  }\n};\nexport const getAsteriskToken = () => {\n  let MatrixToken = getCookie('MatrixToken');\n  if (MatrixToken) {\n    try {\n      MatrixToken = JSON.parse(atob(MatrixToken));\n      return MatrixToken.AsteriskToken;\n    } catch (e) {\n      return getCookie('AsteriskToken');\n    }\n  } else {\n    return getCookie('AsteriskToken');\n  }\n};\nexport const getEmployeeID = () => {\n  let MatrixToken = getCookie('MatrixToken');\n  if (MatrixToken) {\n    try {\n      MatrixToken = JSON.parse(atob(MatrixToken));\n      return MatrixToken.EmployeeId;\n    } catch (e) {\n      return '';\n    }\n  } else {\n    return '';\n  }\n};\nexport const formatDate = dateString => {\n  try {\n    if (!dateString) return 'N/A';\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, yyyy h:mm:ss a');\n  } catch (error) {\n    console.error('Date formatting error:', error);\n    return dateString || 'N/A';\n  }\n};\nexport const convertDotNetDate = dotNetDate => {\n  // Extract timestamp using regex\n  const timestamp = parseInt(dotNetDate.match(/\\d+/)[0], 10);\n\n  // Convert to JavaScript Date object\n  const date = new Date(timestamp);\n  return date; // Returns a Date object\n};", "map": {"version": 3, "names": ["format", "parseISO", "<PERSON><PERSON><PERSON><PERSON>", "name", "document", "nameEQ", "ca", "cookie", "split", "i", "length", "c", "char<PERSON>t", "substring", "indexOf", "getUserId", "MatrixToken", "JSON", "parse", "atob", "UserId", "e", "getAsteriskToken", "AsteriskToken", "getEmployeeID", "EmployeeId", "formatDate", "dateString", "date", "error", "console", "convertDotNetDate", "dotNetDate", "timestamp", "parseInt", "match", "Date"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/CommonHelper.js"], "sourcesContent": ["import { format, parseISO } from \"date-fns\";\n\nexport const getCookie = (name) => {\n    if (document) {\n        name = name || 'token';\n        let nameEQ = name + \"=\";\n        let ca = document.cookie.split(';');\n        for (let i = 0; i < ca.length; i++) {\n            let c = ca[i];\n            while (c.charAt(0) == ' ') c = c.substring(1, c.length);\n            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);\n        }\n        return null;\n    }\n}\n\nexport const getUserId = () => {\n    let MatrixToken = getCookie('MatrixToken');\n    if (MatrixToken) {\n        try {\n            MatrixToken = JSON.parse(atob(MatrixToken));\n            return MatrixToken.UserId;\n        } catch (e) {\n            return getCookie('AgentId');\n        }\n    }\n    else {\n        return getCookie('AgentId');\n    }\n}\n\nexport const getAsteriskToken = () => {\n    let MatrixToken = getCookie('MatrixToken');\n    if (MatrixToken) {\n        try {\n            MatrixToken = JSON.parse(atob(MatrixToken));\n            return MatrixToken.AsteriskToken;\n        } catch (e) {\n            return getCookie('AsteriskToken');\n        }\n    }\n    else {\n        return getCookie('AsteriskToken');\n    }\n}\n\nexport const getEmployeeID = () => {\n    let MatrixToken = getCookie('MatrixToken');\n    if (MatrixToken) {\n        try {\n            MatrixToken = JSON.parse(atob(MatrixToken));\n            return MatrixToken.EmployeeId;\n        } catch (e) {\n            return '';\n        }\n    }\n    else {\n        return '';\n    }\n}\n\nexport const formatDate = (dateString) => {\n    try {\n        if (!dateString) return 'N/A';\n        const date = parseISO(dateString);\n        return format(date, 'MMM d, yyyy h:mm:ss a');\n    } catch (error) {\n        console.error('Date formatting error:', error);\n        return dateString || 'N/A';\n    }\n};\n\nexport const convertDotNetDate = (dotNetDate) => {\n    // Extract timestamp using regex\n    const timestamp = parseInt(dotNetDate.match(/\\d+/)[0], 10);\n\n    // Convert to JavaScript Date object\n    const date = new Date(timestamp);\n\n    return date;  // Returns a Date object\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,UAAU;AAE3C,OAAO,MAAMC,SAAS,GAAIC,IAAI,IAAK;EAC/B,IAAIC,QAAQ,EAAE;IACVD,IAAI,GAAGA,IAAI,IAAI,OAAO;IACtB,IAAIE,MAAM,GAAGF,IAAI,GAAG,GAAG;IACvB,IAAIG,EAAE,GAAGF,QAAQ,CAACG,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAChC,IAAIE,CAAC,GAAGL,EAAE,CAACG,CAAC,CAAC;MACb,OAAOE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAED,CAAC,GAAGA,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEF,CAAC,CAACD,MAAM,CAAC;MACvD,IAAIC,CAAC,CAACG,OAAO,CAACT,MAAM,CAAC,IAAI,CAAC,EAAE,OAAOM,CAAC,CAACE,SAAS,CAACR,MAAM,CAACK,MAAM,EAAEC,CAAC,CAACD,MAAM,CAAC;IAC3E;IACA,OAAO,IAAI;EACf;AACJ,CAAC;AAED,OAAO,MAAMK,SAAS,GAAGA,CAAA,KAAM;EAC3B,IAAIC,WAAW,GAAGd,SAAS,CAAC,aAAa,CAAC;EAC1C,IAAIc,WAAW,EAAE;IACb,IAAI;MACAA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,WAAW,CAAC,CAAC;MAC3C,OAAOA,WAAW,CAACI,MAAM;IAC7B,CAAC,CAAC,OAAOC,CAAC,EAAE;MACR,OAAOnB,SAAS,CAAC,SAAS,CAAC;IAC/B;EACJ,CAAC,MACI;IACD,OAAOA,SAAS,CAAC,SAAS,CAAC;EAC/B;AACJ,CAAC;AAED,OAAO,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;EAClC,IAAIN,WAAW,GAAGd,SAAS,CAAC,aAAa,CAAC;EAC1C,IAAIc,WAAW,EAAE;IACb,IAAI;MACAA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,WAAW,CAAC,CAAC;MAC3C,OAAOA,WAAW,CAACO,aAAa;IACpC,CAAC,CAAC,OAAOF,CAAC,EAAE;MACR,OAAOnB,SAAS,CAAC,eAAe,CAAC;IACrC;EACJ,CAAC,MACI;IACD,OAAOA,SAAS,CAAC,eAAe,CAAC;EACrC;AACJ,CAAC;AAED,OAAO,MAAMsB,aAAa,GAAGA,CAAA,KAAM;EAC/B,IAAIR,WAAW,GAAGd,SAAS,CAAC,aAAa,CAAC;EAC1C,IAAIc,WAAW,EAAE;IACb,IAAI;MACAA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,WAAW,CAAC,CAAC;MAC3C,OAAOA,WAAW,CAACS,UAAU;IACjC,CAAC,CAAC,OAAOJ,CAAC,EAAE;MACR,OAAO,EAAE;IACb;EACJ,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ,CAAC;AAED,OAAO,MAAMK,UAAU,GAAIC,UAAU,IAAK;EACtC,IAAI;IACA,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,MAAMC,IAAI,GAAG3B,QAAQ,CAAC0B,UAAU,CAAC;IACjC,OAAO3B,MAAM,CAAC4B,IAAI,EAAE,uBAAuB,CAAC;EAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAOF,UAAU,IAAI,KAAK;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAIC,UAAU,IAAK;EAC7C;EACA,MAAMC,SAAS,GAAGC,QAAQ,CAACF,UAAU,CAACG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;EAE1D;EACA,MAAMP,IAAI,GAAG,IAAIQ,IAAI,CAACH,SAAS,CAAC;EAEhC,OAAOL,IAAI,CAAC,CAAE;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}