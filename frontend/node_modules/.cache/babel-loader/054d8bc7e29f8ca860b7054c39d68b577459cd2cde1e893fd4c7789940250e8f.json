{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\n// Import Bootstrap and its dependencies\nimport 'jquery/dist/jquery.min.js';\nimport 'popper.js/dist/umd/popper.min.js';\nimport 'bootstrap/dist/js/bootstrap.min.js';\nimport 'bootstrap/dist/css/bootstrap.min.css';\n\n// Import Font Awesome\nimport '@fortawesome/fontawesome-free/css/all.min.css';\n\n// Import Google Fonts\nimport 'typeface-open-sans';\n\n// Import custom styles\nimport './styles/tickets.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 3\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 2\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\n// Import Bootstrap and its dependencies\nimport 'jquery/dist/jquery.min.js';\nimport 'popper.js/dist/umd/popper.min.js';\nimport 'bootstrap/dist/js/bootstrap.min.js';\nimport 'bootstrap/dist/css/bootstrap.min.css';\n\n// Import Font Awesome\nimport '@fortawesome/fontawesome-free/css/all.min.css';\n\n// Import Google Fonts\nimport 'typeface-open-sans';\n\n// Import custom styles\nimport './styles/tickets.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n\t<React.StrictMode>\n\t\t<App />\n\t</React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,OAAO,oCAAoC;AAC3C,OAAO,sCAAsC;;AAE7C;AACA,OAAO,+CAA+C;;AAEtD;AACA,OAAO,oBAAoB;;AAE3B;AACA,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACVL,OAAA,CAACJ,KAAK,CAACU,UAAU;EAAAC,QAAA,eAChBP,OAAA,CAACF,GAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACU,CACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}