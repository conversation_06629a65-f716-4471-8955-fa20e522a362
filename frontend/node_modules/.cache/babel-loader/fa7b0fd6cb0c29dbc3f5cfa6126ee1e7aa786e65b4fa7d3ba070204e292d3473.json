{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, LinearProgress, Typography } from '@mui/material';\nimport { GetSalesTicketUserDetails } from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const {\n    type,\n    ticketId\n  } = useParams();\n  const navigate = useNavigate();\n  const handleUserDetails = data => {\n    if (data.error) {\n      navigate('/login');\n      return false;\n    } else if (!data || data.length === 0 || !data.EMPData) {\n      navigate('/login');\n      return false;\n    }\n    localStorage.setItem('UserDetails', JSON.stringify({\n      \"EMPData\": data.EMPData,\n      \"Token\": '',\n      \"IsLocSet\": 1,\n      \"Location\": data.Location,\n      \"Issue\": {\n        \"IssueID\": 0,\n        \"SubIssueID\": 0\n      }\n    }));\n    const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n    const BU = userDetails.EMPData[0].BU;\n    if (BU === 0) {\n      navigate('/editprofile');\n    } else if ([\"ticket\", \"jag\", \"incentive\"].includes(type)) {\n      navigate('/createFeedBack');\n    } else if (type === \"notification\") {\n      navigate(`/MyTicketDetails/${ticketId}`);\n    } else if (type === \"ticketview\") {\n      navigate(`/TicketDetails/${ticketId}`);\n    } else if (type === \"MyFeedBack\") {\n      navigate(`/MyFeedBackDetails/${ticketId}`);\n    } else {\n      navigate('/myFeedBack');\n    }\n    return true;\n  };\n  useEffect(() => {\n    GetSalesTicketUserDetails().then(response => {\n      handleUserDetails(response);\n    }).catch(() => {\n      navigate('/login');\n    });\n  }, [type, ticketId, navigate]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    minHeight: \"100vh\",\n    children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mt: 2\n      },\n      children: \"Please wait...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 9\n  }, this);\n};\n_s(LandingPage, \"O6tWmOX2wgh5u7gOWdcaJUGq9No=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useParams", "useNavigate", "Box", "LinearProgress", "Typography", "GetSalesTicketUserDetails", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "type", "ticketId", "navigate", "handleUserDetails", "data", "error", "length", "EMPData", "localStorage", "setItem", "JSON", "stringify", "Location", "userDetails", "parse", "getItem", "BU", "includes", "then", "response", "catch", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "sx", "mt", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, LinearProgress, Typography } from '@mui/material';\nimport { GetSalesTicketUserDetails } from '../services/feedbackService';\n\nconst LandingPage = () => {\n    const { type, ticketId } = useParams();\n    const navigate = useNavigate();\n\n    const handleUserDetails = (data) => {\n        if (data.error) {\n            navigate('/login');\n            return false;\n        } \n        else if (!data || data.length === 0 || !data.EMPData) {\n            navigate('/login');\n            return false;\n        }\n\n        localStorage.setItem('UserDetails', \n            JSON.stringify({ \n                \"EMPData\": data.EMPData, \n                \"Token\": '', \n                \"IsLocSet\": 1, \n                \"Location\": data.Location, \n                \"Issue\": { \"IssueID\": 0, \"SubIssueID\": 0 } \n            })\n        );\n        \n        const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n        const BU = userDetails.EMPData[0].BU;\n        \n        if (BU === 0) {\n            navigate('/editprofile');\n        }\n        else if ([\"ticket\",\"jag\",\"incentive\"].includes(type)) {\n            navigate('/createFeedBack');\n        }\n        else if (type === \"notification\") {\n            navigate(`/MyTicketDetails/${ticketId}`);\n        }\n        else if (type === \"ticketview\") {\n            navigate(`/TicketDetails/${ticketId}`);\n        }\n        else if (type === \"MyFeedBack\") {\n            navigate(`/MyFeedBackDetails/${ticketId}`);\n        }\n        else {\n            navigate('/myFeedBack');\n        }\n\n        return true;\n    };\n\n    useEffect(() => {\n        GetSalesTicketUserDetails()\n        .then((response) => {\n            handleUserDetails(response);\n        })\n        .catch(() => {\n            navigate('/login');\n        })\n    }, [ type, ticketId, navigate]);\n\n\n    return (\n        <Box\n            display=\"flex\"\n            flexDirection=\"column\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            minHeight=\"100vh\"\n        >\n            <LinearProgress />\n            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                Please wait...\n            </Typography>\n        </Box>\n    );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,GAAG,EAAEC,cAAc,EAAEC,UAAU,QAAQ,eAAe;AAC/D,SAASC,yBAAyB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGX,SAAS,CAAC,CAAC;EACtC,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,iBAAiB,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,KAAK,EAAE;MACZH,QAAQ,CAAC,QAAQ,CAAC;MAClB,OAAO,KAAK;IAChB,CAAC,MACI,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAK,CAAC,IAAI,CAACF,IAAI,CAACG,OAAO,EAAE;MAClDL,QAAQ,CAAC,QAAQ,CAAC;MAClB,OAAO,KAAK;IAChB;IAEAM,YAAY,CAACC,OAAO,CAAC,aAAa,EAC9BC,IAAI,CAACC,SAAS,CAAC;MACX,SAAS,EAAEP,IAAI,CAACG,OAAO;MACvB,OAAO,EAAE,EAAE;MACX,UAAU,EAAE,CAAC;MACb,UAAU,EAAEH,IAAI,CAACQ,QAAQ;MACzB,OAAO,EAAE;QAAE,SAAS,EAAE,CAAC;QAAE,YAAY,EAAE;MAAE;IAC7C,CAAC,CACL,CAAC;IAED,MAAMC,WAAW,GAAGH,IAAI,CAACI,KAAK,CAACN,YAAY,CAACO,OAAO,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMC,EAAE,GAAGH,WAAW,CAACN,OAAO,CAAC,CAAC,CAAC,CAACS,EAAE;IAEpC,IAAIA,EAAE,KAAK,CAAC,EAAE;MACVd,QAAQ,CAAC,cAAc,CAAC;IAC5B,CAAC,MACI,IAAI,CAAC,QAAQ,EAAC,KAAK,EAAC,WAAW,CAAC,CAACe,QAAQ,CAACjB,IAAI,CAAC,EAAE;MAClDE,QAAQ,CAAC,iBAAiB,CAAC;IAC/B,CAAC,MACI,IAAIF,IAAI,KAAK,cAAc,EAAE;MAC9BE,QAAQ,CAAC,oBAAoBD,QAAQ,EAAE,CAAC;IAC5C,CAAC,MACI,IAAID,IAAI,KAAK,YAAY,EAAE;MAC5BE,QAAQ,CAAC,kBAAkBD,QAAQ,EAAE,CAAC;IAC1C,CAAC,MACI,IAAID,IAAI,KAAK,YAAY,EAAE;MAC5BE,QAAQ,CAAC,sBAAsBD,QAAQ,EAAE,CAAC;IAC9C,CAAC,MACI;MACDC,QAAQ,CAAC,aAAa,CAAC;IAC3B;IAEA,OAAO,IAAI;EACf,CAAC;EAEDb,SAAS,CAAC,MAAM;IACZM,yBAAyB,CAAC,CAAC,CAC1BuB,IAAI,CAAEC,QAAQ,IAAK;MAChBhB,iBAAiB,CAACgB,QAAQ,CAAC;IAC/B,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;MACTlB,QAAQ,CAAC,QAAQ,CAAC;IACtB,CAAC,CAAC;EACN,CAAC,EAAE,CAAEF,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAG/B,oBACIL,OAAA,CAACL,GAAG;IACA6B,OAAO,EAAC,MAAM;IACdC,aAAa,EAAC,QAAQ;IACtBC,UAAU,EAAC,QAAQ;IACnBC,cAAc,EAAC,QAAQ;IACvBC,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEjB7B,OAAA,CAACJ,cAAc;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBjC,OAAA,CAACH,UAAU;MAACqC,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EAAC;IAExC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAAC/B,EAAA,CA1EID,WAAW;EAAA,QACcR,SAAS,EACnBC,WAAW;AAAA;AAAA2C,EAAA,GAF1BpC,WAAW;AA4EjB,eAAeA,WAAW;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}