{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js\";\nimport React from 'react';\nimport '../styles/FeedbackTable.css';\nimport { formatDate } from '../services/CommonHelper';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FeedbackTable = ({\n  feedbacks,\n  type = 0\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row clearfix\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-md-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: feedbacks.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover js-basic-example dataTable table-custom\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"thead-dark\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"FeedbackId\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 17,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Created On\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 19,\n                    columnNumber: 45\n                  }, this), [2, 3].includes(type) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Matrix Role\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 21,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"BU\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 22,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true), [3, 4].includes(type) && /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"AssignTo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 25,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Process\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 27,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"SubProcess\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 28,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 29,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Updated On\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 30,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 16,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 15,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: feedbacks.map(feedback => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `/MyFeedbackDetails/${feedback.TicketID}`,\n                      className: \"feedback-link\",\n                      children: feedback.TicketDisplayID\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 37,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(feedback.CreatedOn)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 49\n                  }, this), [2, 3].includes(type) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: feedback.MatrixRole\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 43,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: feedback.BU\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 44,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true), [3, 4].includes(type) && /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [feedback.AssignToDetails.Name != null ? feedback.AssignToDetails.Name : 'Not assigned', feedback.AssignToDetails.EmployeeID != null ? '(' + feedback.AssignToDetails.EmployeeID + ')' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: feedback.Process\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 49,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: feedback.IssueStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: feedback.TicketStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(feedback.UpdatedOn)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 49\n                  }, this)]\n                }, feedback.TicketID, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 45\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-records\",\n              children: \"No record found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = FeedbackTable;\nexport default FeedbackTable;\nvar _c;\n$RefreshReg$(_c, \"FeedbackTable\");", "map": {"version": 3, "names": ["React", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FeedbackTable", "feedbacks", "type", "className", "children", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "map", "feedback", "href", "TicketID", "TicketDisplayID", "CreatedOn", "MatrixRole", "BU", "AssignToDetails", "Name", "EmployeeID", "Process", "IssueStatus", "TicketStatus", "UpdatedOn", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js"], "sourcesContent": ["import React from 'react';\nimport '../styles/FeedbackTable.css';\nimport { formatDate } from '../services/CommonHelper';\n\nconst FeedbackTable = ({ feedbacks, type = 0 }) => {\n\n    return (\n        <div className=\"row clearfix\">\n            <div className=\"col-md-12\">\n                <div className=\"card\">\n                    <div className=\"body\">\n                        <div className=\"table-responsive\">\n                            {feedbacks.length > 0 ?\n                                <table className=\"table table-hover js-basic-example dataTable table-custom\">\n                                    <thead className=\"thead-dark\">\n                                        <tr>\n                                            <th>FeedbackId</th>\n                                            \n                                            <th>Created On</th>\n                                            {[2,3].includes(type) && <>  \n                                                <th>Matrix Role</th>\n                                                <th>BU</th>\n                                            </>}\n                                            {[3,4].includes(type) && \n                                                <th>AssignTo</th>\n                                            }\n                                            <th>Process</th>\n                                            <th>SubProcess</th>\n                                            <th>Status</th>\n                                            <th>Updated On</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {feedbacks.map((feedback) => (\n                                            <tr key={feedback.TicketID}>\n                                                <td>\n                                                    <a href={`/MyFeedbackDetails/${feedback.TicketID}`} className=\"feedback-link\">\n                                                        {feedback.TicketDisplayID}\n                                                    </a>\n                                                </td>\n                                                <td>{formatDate(feedback.CreatedOn)}</td>\n                                                {[2,3].includes(type) && <>\n                                                    <td>{feedback.MatrixRole}</td>\n                                                    <td>{feedback.BU}</td>\n                                                </>}\n                                                {[3,4].includes(type) && \n                                                    <td>{feedback.AssignToDetails.Name != null ? feedback.AssignToDetails.Name : 'Not assigned' }{feedback.AssignToDetails.EmployeeID != null ? '(' + feedback.AssignToDetails.EmployeeID + ')' : ''}</td>\n                                                }\n                                                <td>{feedback.Process}</td>\n                                                <td>{feedback.IssueStatus}</td>\n                                                <td>{feedback.TicketStatus}</td>\n                                                <td>{formatDate(feedback.UpdatedOn)}</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                                :\n                                <div className=\"no-records\">No record found</div>\n                            }\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default FeedbackTable; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,6BAA6B;AACpC,SAASC,UAAU,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,IAAI,GAAG;AAAE,CAAC,KAAK;EAE/C,oBACIL,OAAA;IAAKM,SAAS,EAAC,cAAc;IAAAC,QAAA,eACzBP,OAAA;MAAKM,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBP,OAAA;QAAKM,SAAS,EAAC,MAAM;QAAAC,QAAA,eACjBP,OAAA;UAAKM,SAAS,EAAC,MAAM;UAAAC,QAAA,eACjBP,OAAA;YAAKM,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5BH,SAAS,CAACI,MAAM,GAAG,CAAC,gBACjBR,OAAA;cAAOM,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEP,OAAA;gBAAOM,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBP,OAAA;kBAAAO,QAAA,gBACIP,OAAA;oBAAAO,QAAA,EAAI;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAEnBZ,OAAA;oBAAAO,QAAA,EAAI;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClB,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,QAAQ,CAACR,IAAI,CAAC,iBAAIL,OAAA,CAAAE,SAAA;oBAAAK,QAAA,gBACrBP,OAAA;sBAAAO,QAAA,EAAI;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpBZ,OAAA;sBAAAO,QAAA,EAAI;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,eACb,CAAC,EACF,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,QAAQ,CAACR,IAAI,CAAC,iBACjBL,OAAA;oBAAAO,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAErBZ,OAAA;oBAAAO,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBZ,OAAA;oBAAAO,QAAA,EAAI;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBZ,OAAA;oBAAAO,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfZ,OAAA;oBAAAO,QAAA,EAAI;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRZ,OAAA;gBAAAO,QAAA,EACKH,SAAS,CAACU,GAAG,CAAEC,QAAQ,iBACpBf,OAAA;kBAAAO,QAAA,gBACIP,OAAA;oBAAAO,QAAA,eACIP,OAAA;sBAAGgB,IAAI,EAAE,sBAAsBD,QAAQ,CAACE,QAAQ,EAAG;sBAACX,SAAS,EAAC,eAAe;sBAAAC,QAAA,EACxEQ,QAAQ,CAACG;oBAAe;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLZ,OAAA;oBAAAO,QAAA,EAAKT,UAAU,CAACiB,QAAQ,CAACI,SAAS;kBAAC;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACxC,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,QAAQ,CAACR,IAAI,CAAC,iBAAIL,OAAA,CAAAE,SAAA;oBAAAK,QAAA,gBACrBP,OAAA;sBAAAO,QAAA,EAAKQ,QAAQ,CAACK;oBAAU;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9BZ,OAAA;sBAAAO,QAAA,EAAKQ,QAAQ,CAACM;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,eACxB,CAAC,EACF,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,QAAQ,CAACR,IAAI,CAAC,iBACjBL,OAAA;oBAAAO,QAAA,GAAKQ,QAAQ,CAACO,eAAe,CAACC,IAAI,IAAI,IAAI,GAAGR,QAAQ,CAACO,eAAe,CAACC,IAAI,GAAG,cAAc,EAAGR,QAAQ,CAACO,eAAe,CAACE,UAAU,IAAI,IAAI,GAAG,GAAG,GAAGT,QAAQ,CAACO,eAAe,CAACE,UAAU,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAE1MZ,OAAA;oBAAAO,QAAA,EAAKQ,QAAQ,CAACU;kBAAO;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3BZ,OAAA;oBAAAO,QAAA,EAAKQ,QAAQ,CAACW;kBAAW;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/BZ,OAAA;oBAAAO,QAAA,EAAKQ,QAAQ,CAACY;kBAAY;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChCZ,OAAA;oBAAAO,QAAA,EAAKT,UAAU,CAACiB,QAAQ,CAACa,SAAS;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAjBpCG,QAAQ,CAACE,QAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBtB,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAERZ,OAAA;cAAKM,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACiB,EAAA,GA7DI1B,aAAa;AA+DnB,eAAeA,aAAa;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}