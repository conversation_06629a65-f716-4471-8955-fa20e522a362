{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport FeedbackTable from './FeedbackTable';\nimport { GetSalesTicketByAgentId, GetSalesTicketCount } from '../services/feedbackService';\nimport '../styles/MyFeedback.css';\nimport '../styles/FeedbackStats.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyFeedback = () => {\n  _s();\n  const [stats, setStats] = useState({\n    new: 0,\n    open: 0,\n    resolved: 0,\n    closed: 0\n  });\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    var objRequest = {\n      \"type\": 1\n    };\n    GetSalesTicketCount(objRequest).then(response => {\n      if (response.length > 0) {\n        response.forEach(item => {\n          switch (item.StatusID) {\n            case 1:\n              setStats(prev => ({\n                ...prev,\n                new: item.Count\n              }));\n              break;\n            case 2:\n              setStats(prev => ({\n                ...prev,\n                open: item.Count\n              }));\n              break;\n            case 3:\n              setStats(prev => ({\n                ...prev,\n                resolved: item.Count\n              }));\n              break;\n            case 4:\n              setStats(prev => ({\n                ...prev,\n                closed: item.Count\n              }));\n              break;\n            default:\n              break;\n          }\n        });\n      } else {\n        setStats({\n          new: 0,\n          open: 0,\n          resolved: 0,\n          closed: 0\n        });\n      }\n    }).catch(() => {\n      setStats({\n        new: 0,\n        open: 0,\n        resolved: 0,\n        closed: 0\n      });\n    }).finally(() => {\n      setLoading(false);\n    });\n  }, []);\n  const GetAllTicketList = status => {\n    const request = {\n      \"type\": 1,\n      \"status\": status\n    };\n    GetSalesTicketByAgentId(request).then(response => {\n      if (response.length > 0) {\n        const sortedFeedbacks = [...response].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));\n        setFeedbacks(sortedFeedbacks);\n      } else {\n        setFeedbacks([]);\n      }\n    }).catch(() => {\n      setFeedbacks([]);\n    });\n  };\n  const statCards = [{\n    label: 'New',\n    count: stats.new || 0,\n    Id: 1,\n    color: '#49b1d4'\n  }, {\n    label: 'Open',\n    count: stats.open || 0,\n    Id: 2,\n    color: '#e0e02d'\n  }, {\n    label: 'Resolved',\n    count: stats.resolved || 0,\n    Id: 3,\n    color: '#53e653'\n  }, {\n    label: 'Closed',\n    count: stats.closed || 0,\n    Id: 4,\n    color: '#2e7b2e'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"my-feedback\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontWeight: '600'\n      },\n      children: \"My FeedBack\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"feedback-stats\",\n      children: statCards.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          backgroundColor: stat.color\n        },\n        onClick: () => GetAllTicketList(stat.Id),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: stat.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: stat.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 25\n        }, this)]\n      }, stat.label, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FeedbackTable, {\n      feedbacks: feedbacks,\n      redirectPage: /MyFeedbackDetails/\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 9\n  }, this);\n};\n_s(MyFeedback, \"ZUdYDhiCJEnPD5RvGk+/UkzzogY=\");\n_c = MyFeedback;\nexport default MyFeedback;\nvar _c;\n$RefreshReg$(_c, \"MyFeedback\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FeedbackTable", "GetSalesTicketByAgentId", "GetSalesTicketCount", "jsxDEV", "_jsxDEV", "MyFeedback", "_s", "stats", "setStats", "new", "open", "resolved", "closed", "feedbacks", "setFeedbacks", "loading", "setLoading", "objRequest", "then", "response", "length", "for<PERSON>ach", "item", "StatusID", "prev", "Count", "catch", "finally", "GetAllTicketList", "status", "request", "sortedFeedbacks", "sort", "a", "b", "Date", "CreatedOn", "statCards", "label", "count", "Id", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontWeight", "map", "stat", "backgroundColor", "onClick", "redirectPage", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport FeedbackTable from './FeedbackTable';\nimport { GetSalesTicketByAgentId, GetSalesTicketCount } from '../services/feedbackService';\nimport '../styles/MyFeedback.css';\nimport '../styles/FeedbackStats.css';\n\nconst MyFeedback = () => {\n    const [stats, setStats] = useState({\n        new: 0,\n        open: 0,\n        resolved: 0,\n        closed: 0\n    });\n\n    const [feedbacks, setFeedbacks] = useState([]);\n    const [loading, setLoading] = useState(false);\n\n    useEffect(() => {  \n        var objRequest = {\n            \"type\": 1,\n        };\n        \n        GetSalesTicketCount(objRequest)\n        .then((response) => {\n            if(response.length > 0){\n                response.forEach(item => {\n                    switch (item.StatusID) {\n                        case 1:\n                            setStats(prev => ({ ...prev, new: item.Count }));\n                            break;\n                        case 2:\n                            setStats(prev => ({ ...prev, open: item.Count }));\n                            break;\n                        case 3:\n                            setStats(prev => ({ ...prev, resolved: item.Count }));\n                            break;\n                        case 4:\n                            setStats(prev => ({ ...prev, closed: item.Count }));\n                            break;\n                        default:\n                            break;\n                    }\n                });\n            }\n            else {\n                setStats({ new: 0, open: 0, resolved: 0, closed: 0 });\n            }\n        })\n        .catch(() => {\n            setStats({ new: 0, open: 0, resolved: 0, closed: 0});\n        })\n        . finally(() => {\n            setLoading(false);\n        });\n    }, []);\n\n\n    const GetAllTicketList = (status) => {\n        const request = {\n            \"type\": 1,\n            \"status\": status\n        };\n\n        GetSalesTicketByAgentId(request)\n        .then((response) => {\n            if(response.length > 0){\n                const sortedFeedbacks = [...response].sort((a, b) => \n                    new Date(b.CreatedOn) - new Date(a.CreatedOn)\n                );\n                setFeedbacks(sortedFeedbacks);\n            }\n            else {\n                setFeedbacks([]);\n            }\n        })\n        .catch(() => {\n            setFeedbacks([]);\n        })\n    }\n\n    const statCards = [\n\t\t{ label: 'New', count: stats.new || 0, Id: 1, color: '#49b1d4' },\n\t\t{ label: 'Open', count: stats.open || 0, Id: 2, color: '#e0e02d' },\n\t\t{ label: 'Resolved', count: stats.resolved || 0 , Id: 3, color: '#53e653' },\n\t\t{ label: 'Closed', count: stats.closed || 0, Id: 4,  color: '#2e7b2e' }\n\t];\n\n    if (loading) {\n        return <div className=\"loading\">Loading...</div>;\n    }\n\n    return (\n        <div className=\"my-feedback\">\n            <p style={{ fontWeight: '600' }}>My FeedBack</p>\n            <div className=\"feedback-stats\">\n                {statCards.map((stat) => (\n                    <div\n                        key={stat.label}\n                        className=\"stat-card\"\n                        style={{ backgroundColor: stat.color }}\n                        onClick={() => GetAllTicketList(stat.Id)}\n                    >\n                        <h2>{stat.count}</h2>\n                        <p>{stat.label}</p>\n                    </div>\n                ))}\n            </div>\n            <FeedbackTable feedbacks={feedbacks} redirectPage={/MyFeedbackDetails/}/>\n        </div>\n    );\n};\n\nexport default MyFeedback; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,uBAAuB,EAAEC,mBAAmB,QAAQ,6BAA6B;AAC1F,OAAO,0BAA0B;AACjC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC;IAC/BW,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,IAAIkB,UAAU,GAAG;MACb,MAAM,EAAE;IACZ,CAAC;IAEDf,mBAAmB,CAACe,UAAU,CAAC,CAC9BC,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAGA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAC;QACnBD,QAAQ,CAACE,OAAO,CAACC,IAAI,IAAI;UACrB,QAAQA,IAAI,CAACC,QAAQ;YACjB,KAAK,CAAC;cACFf,QAAQ,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEf,GAAG,EAAEa,IAAI,CAACG;cAAM,CAAC,CAAC,CAAC;cAChD;YACJ,KAAK,CAAC;cACFjB,QAAQ,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEd,IAAI,EAAEY,IAAI,CAACG;cAAM,CAAC,CAAC,CAAC;cACjD;YACJ,KAAK,CAAC;cACFjB,QAAQ,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEb,QAAQ,EAAEW,IAAI,CAACG;cAAM,CAAC,CAAC,CAAC;cACrD;YACJ,KAAK,CAAC;cACFjB,QAAQ,CAACgB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEZ,MAAM,EAAEU,IAAI,CAACG;cAAM,CAAC,CAAC,CAAC;cACnD;YACJ;cACI;UACR;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACDjB,QAAQ,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC,CAAC;MACzD;IACJ,CAAC,CAAC,CACDc,KAAK,CAAC,MAAM;MACTlB,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CAAC,CAAC;IACxD,CAAC,CAAC,CACAe,OAAO,CAAC,MAAM;MACZX,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAGN,MAAMY,gBAAgB,GAAIC,MAAM,IAAK;IACjC,MAAMC,OAAO,GAAG;MACZ,MAAM,EAAE,CAAC;MACT,QAAQ,EAAED;IACd,CAAC;IAED5B,uBAAuB,CAAC6B,OAAO,CAAC,CAC/BZ,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAGA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAC;QACnB,MAAMW,eAAe,GAAG,CAAC,GAAGZ,QAAQ,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC5C,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAChD,CAAC;QACDtB,YAAY,CAACiB,eAAe,CAAC;MACjC,CAAC,MACI;QACDjB,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,CACDY,KAAK,CAAC,MAAM;MACTZ,YAAY,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;EACN,CAAC;EAED,MAAMuB,SAAS,GAAG,CACpB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAEhC,KAAK,CAACE,GAAG,IAAI,CAAC;IAAE+B,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChE;IAAEH,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAEhC,KAAK,CAACG,IAAI,IAAI,CAAC;IAAE8B,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClE;IAAEH,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAEhC,KAAK,CAACI,QAAQ,IAAI,CAAC;IAAG6B,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3E;IAAEH,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAEhC,KAAK,CAACK,MAAM,IAAI,CAAC;IAAE4B,EAAE,EAAE,CAAC;IAAGC,KAAK,EAAE;EAAU,CAAC,CACvE;EAEE,IAAI1B,OAAO,EAAE;IACT,oBAAOX,OAAA;MAAKsC,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpD;EAEA,oBACI3C,OAAA;IAAKsC,SAAS,EAAC,aAAa;IAAAC,QAAA,gBACxBvC,OAAA;MAAG4C,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAN,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAChD3C,OAAA;MAAKsC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC1BN,SAAS,CAACa,GAAG,CAAEC,IAAI,iBAChB/C,OAAA;QAEIsC,SAAS,EAAC,WAAW;QACrBM,KAAK,EAAE;UAAEI,eAAe,EAAED,IAAI,CAACV;QAAM,CAAE;QACvCY,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACuB,IAAI,CAACX,EAAE,CAAE;QAAAG,QAAA,gBAEzCvC,OAAA;UAAAuC,QAAA,EAAKQ,IAAI,CAACZ;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrB3C,OAAA;UAAAuC,QAAA,EAAIQ,IAAI,CAACb;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GANdI,IAAI,CAACb,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOd,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACN3C,OAAA,CAACJ,aAAa;MAACa,SAAS,EAAEA,SAAU;MAACyC,YAAY,EAAE;IAAoB;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxE,CAAC;AAEd,CAAC;AAACzC,EAAA,CAxGID,UAAU;AAAAkD,EAAA,GAAVlD,UAAU;AA0GhB,eAAeA,UAAU;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}