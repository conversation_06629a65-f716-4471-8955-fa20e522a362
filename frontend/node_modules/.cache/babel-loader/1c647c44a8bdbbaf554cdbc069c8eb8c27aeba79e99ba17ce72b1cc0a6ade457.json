{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { GetTicketDetails, UpdateTicketRemarks, UploadFile, GetProcessMaster, GetStatusMaster, GetSalesTicketProcessUser, AssignSalesTicket, ReAssignSalesTicket, GetSalesTicketLog, GetDocumentUrl } from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TicketDetails = () => {\n  _s();\n  var _ticketDetails$Assign, _ticketDetails$Assign2;\n  const {\n    ticketId\n  } = useParams();\n  const [ticketDetails, setTicketDetails] = useState(null);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [hrComments, setHrComments] = useState('');\n  const [fileAttachments, setFileAttachments] = useState([]);\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: {\n      SourceID: 0\n    },\n    Spoc: undefined\n  });\n  const [sourceList, setSourceList] = useState([]);\n  const [spocList, setSpocList] = useState([]);\n  const [statusList, setStatusList] = useState([]);\n  const [logList, setLogList] = useState([]);\n  const [activeTab, setActiveTab] = useState(1);\n  const [updateAssignmentFlag, setUpdateAssignmentFlag] = useState(0);\n  const [isSupport, setIsSupport] = useState(0);\n  const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n  useEffect(() => {\n    fetchTicketDetails();\n    fetchProcessList();\n    fetchStatusList();\n  }, [ticketId]);\n  const fetchTicketDetails = () => {\n    const req = {\n      ticketId,\n      Type: 1,\n      EmployeeID: userDetails.EMPData[0].EmployeeID,\n      UserID: userDetails.EMPData[0].EmpID\n    };\n    GetTicketDetails(req).then(res => {\n      const data = res === null || res === void 0 ? void 0 : res.GetSalesTicketDetailsByIDResult;\n      setTicketDetails(data);\n      setCommentList(data.Commentlist || []);\n      setSelected(prev => ({\n        ...prev,\n        Status: {\n          StatusID: data.StatusID\n        },\n        IssueType: {\n          ISSUEID: data.IssueID\n        },\n        Source: {\n          SourceID: data.ProcessID\n        }\n      }));\n      fetchLog();\n    });\n  };\n  const fetchStatusList = () => {\n    GetStatusMaster(userDetails.Toket).then(() => {\n      setStatusList([{\n        StatusID: 1,\n        StatusName: 'New'\n      }, {\n        StatusID: 2,\n        StatusName: 'InProgress'\n      }, {\n        StatusID: 3,\n        StatusName: 'Resolved'\n      }, {\n        StatusID: 5,\n        StatusName: 'Reopen'\n      }]);\n    });\n  };\n  const fetchProcessList = () => {\n    GetProcessMaster().then(data => {\n      data.unshift({\n        Name: 'Select',\n        SourceID: 0\n      });\n      setSourceList(data);\n    });\n  };\n  const fetchSpocList = sourceId => {\n    const req = {\n      ticketId,\n      ProcessId: sourceId,\n      AssignTo: 0,\n      UserID: userDetails.EMPData[0].EmpID,\n      Type: 1\n    };\n    GetSalesTicketProcessUser(req, userDetails.Toket).then(res => {\n      setSpocList(res.GetSalesTicketProcessUserResult || []);\n    });\n  };\n  const fetchLog = () => {\n    const req = {\n      ticketId,\n      userId: userDetails.EMPData[0].EmpID,\n      logtype: 0\n    };\n    GetSalesTicketLog(req).then(data => setLogList(data || []));\n  };\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    const readers = files.map(file => {\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve({\n            FileName: file.name,\n            AttachemntContent: btoa(reader.result),\n            AttachmentURL: '',\n            ContentType: file.type\n          });\n        };\n        reader.readAsBinaryString(file);\n      });\n    });\n    Promise.all(readers).then(data => setFileAttachments(data));\n  };\n  const updateRemarks = ReplyType => {\n    var _selected$Status;\n    const commentText = ReplyType === 2 ? ticketReply : hrComments;\n    if (!commentText || commentText.length <= 10) {\n      toast.error(\"Remark should be more than 10 characters\");\n      return;\n    }\n    const req = {\n      TicketID: ticketId,\n      Comments: commentText,\n      StatusID: (_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID,\n      CreatedBy: userDetails.EMPData[0].EmpID,\n      ReplyType,\n      FileURL: '',\n      FileName: ''\n    };\n    if (fileAttachments.length > 0) {\n      UploadFile(fileAttachments, userDetails.Toket).then(fileData => {\n        req.FileURL = fileData[0].AttachmentURL;\n        req.FileName = fileData[0].FileName;\n        UpdateTicketRemarks(req, userDetails.Toket).then(() => {\n          toast.success('Updated successfully');\n          fetchTicketDetails();\n          setTicketReply('');\n        });\n      });\n    } else {\n      UpdateTicketRemarks(req, userDetails.Toket).then(() => {\n        toast.success('Updated successfully');\n        fetchTicketDetails();\n        setTicketReply('');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-lg-6 col-md-8 col-lg-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail_links\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"javascript:void(0);\",\n                className: \"btn btn-info\",\n                onClick: () => window.history.back(),\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"demo-button\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"assign_hd\",\n                children: \"Assigned To :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tat_green\",\n                children: [(ticketDetails === null || ticketDetails === void 0 ? void 0 : (_ticketDetails$Assign = ticketDetails.AssignToDetails) === null || _ticketDetails$Assign === void 0 ? void 0 : _ticketDetails$Assign.Name) || 'Not assigned', ticketDetails !== null && ticketDetails !== void 0 && (_ticketDetails$Assign2 = ticketDetails.AssignToDetails) !== null && _ticketDetails$Assign2 !== void 0 && _ticketDetails$Assign2.EmployeeID ? `(${ticketDetails.AssignToDetails.EmployeeID})` : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row clearfix\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mail-inbox\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mail-right agent_tkt_view\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"body ticket_detailbox\",\n                children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"nav nav-tabs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"nav-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      className: `nav-link ${activeTab === 1 ? 'active show' : ''}`,\n                      onClick: () => setActiveTab(1),\n                      children: \"FeedBack Detail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 46\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"nav-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      className: `nav-link ${activeTab === 3 ? 'active show' : ''}`,\n                      onClick: () => setActiveTab(3),\n                      children: \"Log Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 46\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tab-content table_databox\",\n                  children: [activeTab === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tab-pane show active\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"table-responsive\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table m-b-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Ticket Id\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 196,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Created on\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 197,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Process\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 198,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"FeedBack\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 199,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Product\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 200,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Status\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 201,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Last Updated on\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 202,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 195,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 194,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            className: \"active_detaillist\",\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketDisplayID\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 207,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.CreatedOn\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 208,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.Process\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 209,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.IssueStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 210,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.ProductName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 211,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 212,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.UpdatedOn\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 213,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 206,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 205,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 193,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tab-pane show active\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"table-responsive\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table m-b-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"FieldName\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 226,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"OldValue\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 227,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"NewValue\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 228,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"CreatedBy\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 229,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"CreatedOn\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 230,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 225,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: logList.map((log, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: log.FieldName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 236,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: log.OldValue\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 237,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: log.NewValue\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 238,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: log.CreatedByName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 239,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: log.CreatedOn\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 240,\n                              columnNumber: 35\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 235,\n                            columnNumber: 33\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 233,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(TicketDetails, \"bhoq97Y3AGQXmaCbMbaPZLqepkQ=\", false, function () {\n  return [useParams];\n});\n_c = TicketDetails;\nexport default TicketDetails;\nvar _c;\n$RefreshReg$(_c, \"TicketDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "toast", "GetTicketDetails", "UpdateTicketRemarks", "UploadFile", "GetProcessMaster", "GetStatusMaster", "GetSalesTicketProcessUser", "AssignSalesTicket", "ReAssignSalesTicket", "GetSalesTicketLog", "GetDocumentUrl", "jsxDEV", "_jsxDEV", "TicketDetails", "_s", "_ticketDetails$Assign", "_ticketDetails$Assign2", "ticketId", "ticketDetails", "setTicketDetails", "commentList", "setCommentList", "ticketReply", "setTicketReply", "hrComments", "setHrComments", "fileAttachments", "setFileAttachments", "selected", "setSelected", "Status", "undefined", "IssueType", "SubIssueType", "Source", "SourceID", "Spoc", "sourceList", "setSourceList", "spocList", "setSpocList", "statusList", "setStatusList", "logList", "setLogList", "activeTab", "setActiveTab", "updateAssignmentFlag", "setUpdateAssignmentFlag", "isSupport", "setIsSupport", "userDetails", "JSON", "parse", "localStorage", "getItem", "fetchTicketDetails", "fetchProcessList", "fetchStatusList", "req", "Type", "EmployeeID", "EMPData", "UserID", "EmpID", "then", "res", "data", "GetSalesTicketDetailsByIDResult", "Commentlist", "prev", "StatusID", "ISSUEID", "IssueID", "ProcessID", "fetchLog", "Toket", "StatusName", "unshift", "Name", "fetchSpocList", "sourceId", "ProcessId", "Assign<PERSON><PERSON>", "GetSalesTicketProcessUserResult", "userId", "logtype", "handleFileChange", "e", "files", "Array", "from", "target", "readers", "map", "file", "Promise", "resolve", "reader", "FileReader", "onload", "FileName", "name", "AttachemntContent", "btoa", "result", "AttachmentURL", "ContentType", "type", "readAsBinaryString", "all", "updateRemarks", "ReplyType", "_selected$Status", "commentText", "length", "error", "TicketID", "Comments", "CreatedBy", "FileURL", "fileData", "success", "className", "children", "href", "onClick", "window", "history", "back", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "AssignToDetails", "TicketDisplayID", "CreatedOn", "Process", "IssueStatus", "ProductName", "TicketStatus", "UpdatedOn", "log", "index", "FieldName", "OldValue", "NewValue", "CreatedByName", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport {\n  GetTicketDetails,\n  UpdateTicketRemarks,\n  UploadFile,\n  GetProcessMaster,\n  GetStatusMaster,\n  GetSalesTicketProcessUser,\n  AssignSalesTicket,\n  ReAssignSalesTicket,\n  GetSalesTicketLog,\n  GetDocumentUrl\n} from '../services/feedbackService';\n\nconst TicketDetails = () => {\n  const { ticketId } = useParams();\n  const [ticketDetails, setTicketDetails] = useState(null);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [hrComments, setHrComments] = useState('');\n  const [fileAttachments, setFileAttachments] = useState([]);\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: { SourceID: 0 },\n    Spoc: undefined\n  });\n  const [sourceList, setSourceList] = useState([]);\n  const [spocList, setSpocList] = useState([]);\n  const [statusList, setStatusList] = useState([]);\n  const [logList, setLogList] = useState([]);\n  const [activeTab, setActiveTab] = useState(1);\n  const [updateAssignmentFlag, setUpdateAssignmentFlag] = useState(0);\n  const [isSupport, setIsSupport] = useState(0);\n  const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n\n  useEffect(() => {\n    fetchTicketDetails();\n    fetchProcessList();\n    fetchStatusList();\n  }, [ticketId]);\n\n  const fetchTicketDetails = () => {\n    const req = {\n      ticketId,\n      Type: 1,\n      EmployeeID: userDetails.EMPData[0].EmployeeID,\n      UserID: userDetails.EMPData[0].EmpID\n    };\n    GetTicketDetails(req).then(res => {\n      const data = res?.GetSalesTicketDetailsByIDResult;\n      setTicketDetails(data);\n      setCommentList(data.Commentlist || []);\n      setSelected(prev => ({\n        ...prev,\n        Status: { StatusID: data.StatusID },\n        IssueType: { ISSUEID: data.IssueID },\n        Source: { SourceID: data.ProcessID }\n      }));\n      fetchLog();\n    });\n  };\n\n  const fetchStatusList = () => {\n    GetStatusMaster(userDetails.Toket).then(() => {\n      setStatusList([\n        { StatusID: 1, StatusName: 'New' },\n        { StatusID: 2, StatusName: 'InProgress' },\n        { StatusID: 3, StatusName: 'Resolved' },\n        { StatusID: 5, StatusName: 'Reopen' }\n      ]);\n    });\n  };\n\n  const fetchProcessList = () => {\n    GetProcessMaster().then(data => {\n      data.unshift({ Name: 'Select', SourceID: 0 });\n      setSourceList(data);\n    });\n  };\n\n  const fetchSpocList = (sourceId) => {\n    const req = {\n      ticketId,\n      ProcessId: sourceId,\n      AssignTo: 0,\n      UserID: userDetails.EMPData[0].EmpID,\n      Type: 1\n    };\n    GetSalesTicketProcessUser(req, userDetails.Toket).then(res => {\n      setSpocList(res.GetSalesTicketProcessUserResult || []);\n    });\n  };\n\n  const fetchLog = () => {\n    const req = {\n      ticketId,\n      userId: userDetails.EMPData[0].EmpID,\n      logtype: 0\n    };\n    GetSalesTicketLog(req).then(data => setLogList(data || []));\n  };\n\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    const readers = files.map(file => {\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve({\n            FileName: file.name,\n            AttachemntContent: btoa(reader.result),\n            AttachmentURL: '',\n            ContentType: file.type\n          });\n        };\n        reader.readAsBinaryString(file);\n      });\n    });\n    Promise.all(readers).then(data => setFileAttachments(data));\n  };\n\n  const updateRemarks = (ReplyType) => {\n    const commentText = ReplyType === 2 ? ticketReply : hrComments;\n    if (!commentText || commentText.length <= 10) {\n      toast.error(\"Remark should be more than 10 characters\");\n      return;\n    }\n    const req = {\n      TicketID: ticketId,\n      Comments: commentText,\n      StatusID: selected.Status?.StatusID,\n      CreatedBy: userDetails.EMPData[0].EmpID,\n      ReplyType,\n      FileURL: '',\n      FileName: ''\n    };\n    if (fileAttachments.length > 0) {\n      UploadFile(fileAttachments, userDetails.Toket).then(fileData => {\n        req.FileURL = fileData[0].AttachmentURL;\n        req.FileName = fileData[0].FileName;\n        UpdateTicketRemarks(req, userDetails.Toket).then(() => {\n          toast.success('Updated successfully');\n          fetchTicketDetails();\n          setTicketReply('');\n        });\n      });\n    } else {\n      UpdateTicketRemarks(req, userDetails.Toket).then(() => {\n        toast.success('Updated successfully');\n        fetchTicketDetails();\n        setTicketReply('');\n      });\n    }\n  };\n\n  return (\n    <div className=\"container-fluid\">\n      <div className=\"block-header\">\n        <div className=\"row\">\n          <div className=\"col-lg-6 col-md-8 col-lg-12\">\n            <div className=\"detail_links\">\n              <h2>\n                <a href=\"javascript:void(0);\" className=\"btn btn-info\" onClick={() => window.history.back()}>Back</a>\n              </h2>\n              <p className=\"demo-button\">\n                <span className=\"assign_hd\">Assigned To :</span>\n                <span className=\"tat_green\">\n                  {ticketDetails?.AssignToDetails?.Name || 'Not assigned'}{ticketDetails?.AssignToDetails?.EmployeeID ? `(${ticketDetails.AssignToDetails.EmployeeID})` : ''}\n                </span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"row clearfix\">\n        <div className=\"col-lg-12\">\n          <div className=\"card\">\n            <div className=\"mail-inbox\">\n              <div className=\"mail-right agent_tkt_view\">\n                <div className=\"body ticket_detailbox\">\n                  <ul className=\"nav nav-tabs\">\n                    <li className=\"nav-item\"><a className={`nav-link ${activeTab === 1 ? 'active show' : ''}`} onClick={() => setActiveTab(1)}>FeedBack Detail</a></li>\n                    <li className=\"nav-item\"><a className={`nav-link ${activeTab === 3 ? 'active show' : ''}`} onClick={() => setActiveTab(3)}>Log Details</a></li>\n                  </ul>\n                  <div className=\"tab-content table_databox\">\n                    {activeTab === 1 && (\n                      <div className=\"tab-pane show active\">\n                        <div className=\"table-responsive\">\n                          <table className=\"table m-b-0\">\n                            <thead>\n                              <tr>\n                                <th>Ticket Id</th>\n                                <th>Created on</th>\n                                <th>Process</th>\n                                <th>FeedBack</th>\n                                <th>Product</th>\n                                <th>Status</th>\n                                <th>Last Updated on</th>\n                              </tr>\n                            </thead>\n                            <tbody>\n                              <tr className=\"active_detaillist\">\n                                <td>{ticketDetails?.TicketDisplayID}</td>\n                                <td>{ticketDetails?.CreatedOn}</td>\n                                <td>{ticketDetails?.Process}</td>\n                                <td>{ticketDetails?.IssueStatus}</td>\n                                <td>{ticketDetails?.ProductName}</td>\n                                <td>{ticketDetails?.TicketStatus}</td>\n                                <td>{ticketDetails?.UpdatedOn}</td>\n                              </tr>\n                            </tbody>\n                          </table>\n                        </div>\n                      </div>\n                    )}\n                    {activeTab === 3 && (\n                      <div className=\"tab-pane show active\">\n                        <div className=\"table-responsive\">\n                          <table className=\"table m-b-0\">\n                            <thead>\n                              <tr>\n                                <th>FieldName</th>\n                                <th>OldValue</th>\n                                <th>NewValue</th>\n                                <th>CreatedBy</th>\n                                <th>CreatedOn</th>\n                              </tr>\n                            </thead>\n                            <tbody>\n                              {logList.map((log, index) => (\n                                <tr key={index}>\n                                  <td>{log.FieldName}</td>\n                                  <td>{log.OldValue}</td>\n                                  <td>{log.NewValue}</td>\n                                  <td>{log.CreatedByName}</td>\n                                  <td>{log.CreatedOn}</td>\n                                </tr>\n                              ))}\n                            </tbody>\n                          </table>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TicketDetails;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,UAAU,EACVC,gBAAgB,EAChBC,eAAe,EACfC,yBAAyB,EACzBC,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,QACT,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAM;IAAEC;EAAS,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAChC,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IACvCiC,MAAM,EAAEC,SAAS;IACjBC,SAAS,EAAED,SAAS;IACpBE,YAAY,EAAEF,SAAS;IACvBG,MAAM,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAC;IACvBC,IAAI,EAAEL;EACR,CAAC,CAAC;EACF,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMsD,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;EAEnEzD,SAAS,CAAC,MAAM;IACd0D,kBAAkB,CAAC,CAAC;IACpBC,gBAAgB,CAAC,CAAC;IAClBC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACzC,QAAQ,CAAC,CAAC;EAEd,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMG,GAAG,GAAG;MACV1C,QAAQ;MACR2C,IAAI,EAAE,CAAC;MACPC,UAAU,EAAEV,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,CAACD,UAAU;MAC7CE,MAAM,EAAEZ,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE;IACjC,CAAC;IACD/D,gBAAgB,CAAC0D,GAAG,CAAC,CAACM,IAAI,CAACC,GAAG,IAAI;MAChC,MAAMC,IAAI,GAAGD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,+BAA+B;MACjDjD,gBAAgB,CAACgD,IAAI,CAAC;MACtB9C,cAAc,CAAC8C,IAAI,CAACE,WAAW,IAAI,EAAE,CAAC;MACtCxC,WAAW,CAACyC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPxC,MAAM,EAAE;UAAEyC,QAAQ,EAAEJ,IAAI,CAACI;QAAS,CAAC;QACnCvC,SAAS,EAAE;UAAEwC,OAAO,EAAEL,IAAI,CAACM;QAAQ,CAAC;QACpCvC,MAAM,EAAE;UAAEC,QAAQ,EAAEgC,IAAI,CAACO;QAAU;MACrC,CAAC,CAAC,CAAC;MACHC,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMjB,eAAe,GAAGA,CAAA,KAAM;IAC5BrD,eAAe,CAAC8C,WAAW,CAACyB,KAAK,CAAC,CAACX,IAAI,CAAC,MAAM;MAC5CvB,aAAa,CAAC,CACZ;QAAE6B,QAAQ,EAAE,CAAC;QAAEM,UAAU,EAAE;MAAM,CAAC,EAClC;QAAEN,QAAQ,EAAE,CAAC;QAAEM,UAAU,EAAE;MAAa,CAAC,EACzC;QAAEN,QAAQ,EAAE,CAAC;QAAEM,UAAU,EAAE;MAAW,CAAC,EACvC;QAAEN,QAAQ,EAAE,CAAC;QAAEM,UAAU,EAAE;MAAS,CAAC,CACtC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMpB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrD,gBAAgB,CAAC,CAAC,CAAC6D,IAAI,CAACE,IAAI,IAAI;MAC9BA,IAAI,CAACW,OAAO,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAE5C,QAAQ,EAAE;MAAE,CAAC,CAAC;MAC7CG,aAAa,CAAC6B,IAAI,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,aAAa,GAAIC,QAAQ,IAAK;IAClC,MAAMtB,GAAG,GAAG;MACV1C,QAAQ;MACRiE,SAAS,EAAED,QAAQ;MACnBE,QAAQ,EAAE,CAAC;MACXpB,MAAM,EAAEZ,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;MACpCJ,IAAI,EAAE;IACR,CAAC;IACDtD,yBAAyB,CAACqD,GAAG,EAAER,WAAW,CAACyB,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,IAAI;MAC5D1B,WAAW,CAAC0B,GAAG,CAACkB,+BAA+B,IAAI,EAAE,CAAC;IACxD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMT,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMhB,GAAG,GAAG;MACV1C,QAAQ;MACRoE,MAAM,EAAElC,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;MACpCsB,OAAO,EAAE;IACX,CAAC;IACD7E,iBAAiB,CAACkD,GAAG,CAAC,CAACM,IAAI,CAACE,IAAI,IAAIvB,UAAU,CAACuB,IAAI,IAAI,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,MAAMoB,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;IACxC,MAAMI,OAAO,GAAGJ,KAAK,CAACK,GAAG,CAACC,IAAI,IAAI;MAChC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;UACpBH,OAAO,CAAC;YACNI,QAAQ,EAAEN,IAAI,CAACO,IAAI;YACnBC,iBAAiB,EAAEC,IAAI,CAACN,MAAM,CAACO,MAAM,CAAC;YACtCC,aAAa,EAAE,EAAE;YACjBC,WAAW,EAAEZ,IAAI,CAACa;UACpB,CAAC,CAAC;QACJ,CAAC;QACDV,MAAM,CAACW,kBAAkB,CAACd,IAAI,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;IACFC,OAAO,CAACc,GAAG,CAACjB,OAAO,CAAC,CAAC5B,IAAI,CAACE,IAAI,IAAIxC,kBAAkB,CAACwC,IAAI,CAAC,CAAC;EAC7D,CAAC;EAED,MAAM4C,aAAa,GAAIC,SAAS,IAAK;IAAA,IAAAC,gBAAA;IACnC,MAAMC,WAAW,GAAGF,SAAS,KAAK,CAAC,GAAG1F,WAAW,GAAGE,UAAU;IAC9D,IAAI,CAAC0F,WAAW,IAAIA,WAAW,CAACC,MAAM,IAAI,EAAE,EAAE;MAC5CnH,KAAK,CAACoH,KAAK,CAAC,0CAA0C,CAAC;MACvD;IACF;IACA,MAAMzD,GAAG,GAAG;MACV0D,QAAQ,EAAEpG,QAAQ;MAClBqG,QAAQ,EAAEJ,WAAW;MACrB3C,QAAQ,GAAA0C,gBAAA,GAAErF,QAAQ,CAACE,MAAM,cAAAmF,gBAAA,uBAAfA,gBAAA,CAAiB1C,QAAQ;MACnCgD,SAAS,EAAEpE,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;MACvCgD,SAAS;MACTQ,OAAO,EAAE,EAAE;MACXnB,QAAQ,EAAE;IACZ,CAAC;IACD,IAAI3E,eAAe,CAACyF,MAAM,GAAG,CAAC,EAAE;MAC9BhH,UAAU,CAACuB,eAAe,EAAEyB,WAAW,CAACyB,KAAK,CAAC,CAACX,IAAI,CAACwD,QAAQ,IAAI;QAC9D9D,GAAG,CAAC6D,OAAO,GAAGC,QAAQ,CAAC,CAAC,CAAC,CAACf,aAAa;QACvC/C,GAAG,CAAC0C,QAAQ,GAAGoB,QAAQ,CAAC,CAAC,CAAC,CAACpB,QAAQ;QACnCnG,mBAAmB,CAACyD,GAAG,EAAER,WAAW,CAACyB,KAAK,CAAC,CAACX,IAAI,CAAC,MAAM;UACrDjE,KAAK,CAAC0H,OAAO,CAAC,sBAAsB,CAAC;UACrClE,kBAAkB,CAAC,CAAC;UACpBjC,cAAc,CAAC,EAAE,CAAC;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLrB,mBAAmB,CAACyD,GAAG,EAAER,WAAW,CAACyB,KAAK,CAAC,CAACX,IAAI,CAAC,MAAM;QACrDjE,KAAK,CAAC0H,OAAO,CAAC,sBAAsB,CAAC;QACrClE,kBAAkB,CAAC,CAAC;QACpBjC,cAAc,CAAC,EAAE,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEX,OAAA;IAAK+G,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BhH,OAAA;MAAK+G,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BhH,OAAA;QAAK+G,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBhH,OAAA;UAAK+G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ChH,OAAA;YAAK+G,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhH,OAAA;cAAAgH,QAAA,eACEhH,OAAA;gBAAGiH,IAAI,EAAC,qBAAqB;gBAACF,SAAS,EAAC,cAAc;gBAACG,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;gBAAAL,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACLzH,OAAA;cAAG+G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBhH,OAAA;gBAAM+G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDzH,OAAA;gBAAM+G,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACxB,CAAA1G,aAAa,aAAbA,aAAa,wBAAAH,qBAAA,GAAbG,aAAa,CAAEoH,eAAe,cAAAvH,qBAAA,uBAA9BA,qBAAA,CAAgCgE,IAAI,KAAI,cAAc,EAAE7D,aAAa,aAAbA,aAAa,gBAAAF,sBAAA,GAAbE,aAAa,CAAEoH,eAAe,cAAAtH,sBAAA,eAA9BA,sBAAA,CAAgC6C,UAAU,GAAG,IAAI3C,aAAa,CAACoH,eAAe,CAACzE,UAAU,GAAG,GAAG,EAAE;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNzH,OAAA;MAAK+G,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BhH,OAAA;QAAK+G,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBhH,OAAA;UAAK+G,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhH,OAAA;YAAK+G,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBhH,OAAA;cAAK+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxChH,OAAA;gBAAK+G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpChH,OAAA;kBAAI+G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC1BhH,OAAA;oBAAI+G,SAAS,EAAC,UAAU;oBAAAC,QAAA,eAAChH,OAAA;sBAAG+G,SAAS,EAAE,YAAY9E,SAAS,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE,EAAG;sBAACiF,OAAO,EAAEA,CAAA,KAAMhF,YAAY,CAAC,CAAC,CAAE;sBAAA8E,QAAA,EAAC;oBAAe;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnJzH,OAAA;oBAAI+G,SAAS,EAAC,UAAU;oBAAAC,QAAA,eAAChH,OAAA;sBAAG+G,SAAS,EAAE,YAAY9E,SAAS,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE,EAAG;sBAACiF,OAAO,EAAEA,CAAA,KAAMhF,YAAY,CAAC,CAAC,CAAE;sBAAA8E,QAAA,EAAC;oBAAW;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,eACLzH,OAAA;kBAAK+G,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GACvC/E,SAAS,KAAK,CAAC,iBACdjC,OAAA;oBAAK+G,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,eACnChH,OAAA;sBAAK+G,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BhH,OAAA;wBAAO+G,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC5BhH,OAAA;0BAAAgH,QAAA,eACEhH,OAAA;4BAAAgH,QAAA,gBACEhH,OAAA;8BAAAgH,QAAA,EAAI;4BAAS;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAU;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAO;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAQ;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACjBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAO;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAM;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACfzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAe;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACRzH,OAAA;0BAAAgH,QAAA,eACEhH,OAAA;4BAAI+G,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,gBAC/BhH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqH;4BAAe;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsH;4BAAS;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACnCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuH;4BAAO;8BAAAP,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACjCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwH;4BAAW;8BAAAR,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACrCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyH;4BAAW;8BAAAT,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACrCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0H;4BAAY;8BAAAV,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACtCzH,OAAA;8BAAAgH,QAAA,EAAK1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2H;4BAAS;8BAAAX,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EACAxF,SAAS,KAAK,CAAC,iBACdjC,OAAA;oBAAK+G,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,eACnChH,OAAA;sBAAK+G,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BhH,OAAA;wBAAO+G,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC5BhH,OAAA;0BAAAgH,QAAA,eACEhH,OAAA;4BAAAgH,QAAA,gBACEhH,OAAA;8BAAAgH,QAAA,EAAI;4BAAS;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAQ;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACjBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAQ;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACjBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAS;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBzH,OAAA;8BAAAgH,QAAA,EAAI;4BAAS;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACRzH,OAAA;0BAAAgH,QAAA,EACGjF,OAAO,CAACmD,GAAG,CAAC,CAACgD,GAAG,EAAEC,KAAK,kBACtBnI,OAAA;4BAAAgH,QAAA,gBACEhH,OAAA;8BAAAgH,QAAA,EAAKkB,GAAG,CAACE;4BAAS;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACxBzH,OAAA;8BAAAgH,QAAA,EAAKkB,GAAG,CAACG;4BAAQ;8BAAAf,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvBzH,OAAA;8BAAAgH,QAAA,EAAKkB,GAAG,CAACI;4BAAQ;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvBzH,OAAA;8BAAAgH,QAAA,EAAKkB,GAAG,CAACK;4BAAa;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAC5BzH,OAAA;8BAAAgH,QAAA,EAAKkB,GAAG,CAACN;4BAAS;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA,GALjBU,KAAK;4BAAAb,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAMV,CACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvH,EAAA,CAhPID,aAAa;EAAA,QACId,SAAS;AAAA;AAAAqJ,EAAA,GAD1BvI,aAAa;AAkPnB,eAAeA,aAAa;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}