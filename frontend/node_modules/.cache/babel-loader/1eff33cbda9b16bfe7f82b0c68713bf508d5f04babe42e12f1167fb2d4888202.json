{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport api from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const {\n    token,\n    type,\n    ticketId\n  } = useParams();\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n  const handleUserDetails = data => {\n    if (data.error) {\n      setError(data.message);\n      setTimeout(() => navigate('/login'), 3000);\n      return false;\n    } else if (!data || data.length === 0 || !data.EMPData) {\n      setError('Data not found.');\n      setTimeout(() => navigate('/login'), 3000);\n      return false;\n    }\n\n    // Store user details in localStorage\n    localStorage.removeItem('UserDetails');\n    localStorage.setItem('UserDetails', JSON.stringify({\n      \"EMPData\": data.EMPData,\n      \"Token\": data.token,\n      \"IsLocSet\": 1,\n      \"Location\": data.Location,\n      \"Issue\": {\n        \"IssueID\": 0,\n        \"SubIssueID\": 0\n      }\n    }));\n    const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n    const BU = userDetails.EMPData[0].BU;\n\n    // Redirect based on BU and type\n    if (BU === 0) {\n      navigate('/matrix/editprofile');\n    } else if (type === \"ticket\") {\n      navigate('/matrix/createFeedBack/1/4/ticket');\n    } else if (type === \"payment\") {\n      navigate(`/matrix/createFeedBack/3/41/ticket/${leadId}/${payId}/${OrderId}/${parentId}`);\n    } else if (type === \"jag\") {\n      navigate('/matrix/createFeedBack/1/33/ticket');\n    } else if (type === \"incentive\") {\n      navigate('/matrix/createFeedBack/1/4/ticket');\n    } else if (type === \"notification\") {\n      navigate(`/matrix/MyTicketDetails/${ticketId}`);\n    } else if (type === \"ticketview\") {\n      navigate(`/matrix/TicketDetails/${token}`);\n    } else if (type === \"MyFeedBack\") {\n      navigate(`/matrix/MyFeedBackDetails/${token}`);\n    } else {\n      navigate('/matrix/myFeedBack');\n    }\n    return true;\n  };\n  useEffect(() => {\n    const processLogin = async () => {\n      try {\n        setLoading(true);\n        if (type === \"matrix\") {\n          const response = await api.post('/api/matrixlogin', {\n            token,\n            userId: ticketId\n          });\n          handleUserDetails(response.data.GetUserDetailsBytokenResult);\n        } else if (type === \"ticketview\" || type === \"payment\" || type === \"MyFeedBack\") {\n          const response = await api.post('/api/validateticketlogin', {\n            token\n          });\n          handleUserDetails(response.data);\n        } else {\n          const response = await api.post('/api/saleslogin', {\n            token,\n            type\n          });\n          handleUserDetails(response.data.GetUserDetailsResult);\n        }\n      } catch (err) {\n        setError(err.message || 'Authentication failed');\n        setTimeout(() => navigate('/login'), 3000);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (token) {\n      processLogin();\n    } else {\n      navigate('/login');\n    }\n  }, [token, type, ticketId, navigate]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      minHeight: \"100vh\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        gutterBottom: true,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Redirecting to login page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    minHeight: \"100vh\",\n    children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mt: 2\n      },\n      children: \"Please wait...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 9\n  }, this);\n};\n_s(LandingPage, \"pgaUgUVPhF3nXMA6+NQsL1WxyL8=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useNavigate", "Box", "CircularProgress", "Typography", "api", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "token", "type", "ticketId", "navigate", "error", "setError", "loading", "setLoading", "handleUserDetails", "data", "message", "setTimeout", "length", "EMPData", "localStorage", "removeItem", "setItem", "JSON", "stringify", "Location", "userDetails", "parse", "getItem", "BU", "leadId", "payId", "OrderId", "parentId", "processLogin", "response", "post", "userId", "GetUserDetailsBytokenResult", "GetUserDetailsResult", "err", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "color", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport api from '../../services/api';\n\nconst LandingPage = () => {\n    const { token, type, ticketId } = useParams();\n    const navigate = useNavigate();\n    const [error, setError] = useState('');\n    const [loading, setLoading] = useState(true);\n\n    const handleUserDetails = (data) => {\n        if (data.error) {\n            setError(data.message);\n            setTimeout(() => navigate('/login'), 3000);\n            return false;\n        } else if (!data || data.length === 0 || !data.EMPData) {\n            setError('Data not found.');\n            setTimeout(() => navigate('/login'), 3000);\n            return false;\n        }\n\n        // Store user details in localStorage\n        localStorage.removeItem('UserDetails');\n        localStorage.setItem('UserDetails', \n            JSON.stringify({ \n                \"EMPData\": data.EMPData, \n                \"Token\": data.token, \n                \"IsLocSet\": 1, \n                \"Location\": data.Location, \n                \"Issue\": { \"IssueID\": 0, \"SubIssueID\": 0 } \n            })\n        );\n        \n        const userDetails = JSON.parse(localStorage.getItem('UserDetails'));\n        const BU = userDetails.EMPData[0].BU;\n\n        // Redirect based on BU and type\n        if (BU === 0) {\n            navigate('/matrix/editprofile');\n        } else if (type === \"ticket\") {\n            navigate('/matrix/createFeedBack/1/4/ticket');\n        } else if (type === \"payment\") {\n            navigate(`/matrix/createFeedBack/3/41/ticket/${leadId}/${payId}/${OrderId}/${parentId}`);\n        } else if (type === \"jag\") {\n            navigate('/matrix/createFeedBack/1/33/ticket');\n        } else if (type === \"incentive\") {\n            navigate('/matrix/createFeedBack/1/4/ticket');\n        } else if (type === \"notification\") {\n            navigate(`/matrix/MyTicketDetails/${ticketId}`);\n        } else if (type === \"ticketview\") {\n            navigate(`/matrix/TicketDetails/${token}`);\n        } else if (type === \"MyFeedBack\") {\n            navigate(`/matrix/MyFeedBackDetails/${token}`);\n        } else {\n            navigate('/matrix/myFeedBack');\n        }\n\n        return true;\n    };\n\n    useEffect(() => {\n        const processLogin = async () => {\n            try {\n                setLoading(true);\n                \n                if (type === \"matrix\") {\n                    const response = await api.post('/api/matrixlogin', {\n                        token,\n                        userId: ticketId\n                    });\n                    handleUserDetails(response.data.GetUserDetailsBytokenResult);\n                } \n                else if (type === \"ticketview\" || type === \"payment\" || type === \"MyFeedBack\") {\n                    const response = await api.post('/api/validateticketlogin', { token });\n                    handleUserDetails(response.data);\n                } \n                else {\n                    const response = await api.post('/api/saleslogin', {\n                        token,\n                        type\n                    });\n                    handleUserDetails(response.data.GetUserDetailsResult);\n                }\n            } catch (err) {\n                setError(err.message || 'Authentication failed');\n                setTimeout(() => navigate('/login'), 3000);\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        if (token) {\n            processLogin();\n        } else {\n            navigate('/login');\n        }\n    }, [token, type, ticketId, navigate]);\n\n    if (error) {\n        return (\n            <Box\n                display=\"flex\"\n                flexDirection=\"column\"\n                alignItems=\"center\"\n                justifyContent=\"center\"\n                minHeight=\"100vh\"\n            >\n                <Typography variant=\"h6\" color=\"error\" gutterBottom>\n                    {error}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                    Redirecting to login page...\n                </Typography>\n            </Box>\n        );\n    }\n\n    return (\n        <Box\n            display=\"flex\"\n            flexDirection=\"column\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            minHeight=\"100vh\"\n        >\n            <CircularProgress />\n            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                Please wait...\n            </Typography>\n        </Box>\n    );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACjE,OAAOC,GAAG,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC7C,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMmB,iBAAiB,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACL,KAAK,EAAE;MACZC,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC;MACtBC,UAAU,CAAC,MAAMR,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC1C,OAAO,KAAK;IAChB,CAAC,MAAM,IAAI,CAACM,IAAI,IAAIA,IAAI,CAACG,MAAM,KAAK,CAAC,IAAI,CAACH,IAAI,CAACI,OAAO,EAAE;MACpDR,QAAQ,CAAC,iBAAiB,CAAC;MAC3BM,UAAU,CAAC,MAAMR,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC1C,OAAO,KAAK;IAChB;;IAEA;IACAW,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC;IACtCD,YAAY,CAACE,OAAO,CAAC,aAAa,EAC9BC,IAAI,CAACC,SAAS,CAAC;MACX,SAAS,EAAET,IAAI,CAACI,OAAO;MACvB,OAAO,EAAEJ,IAAI,CAACT,KAAK;MACnB,UAAU,EAAE,CAAC;MACb,UAAU,EAAES,IAAI,CAACU,QAAQ;MACzB,OAAO,EAAE;QAAE,SAAS,EAAE,CAAC;QAAE,YAAY,EAAE;MAAE;IAC7C,CAAC,CACL,CAAC;IAED,MAAMC,WAAW,GAAGH,IAAI,CAACI,KAAK,CAACP,YAAY,CAACQ,OAAO,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMC,EAAE,GAAGH,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC,CAACU,EAAE;;IAEpC;IACA,IAAIA,EAAE,KAAK,CAAC,EAAE;MACVpB,QAAQ,CAAC,qBAAqB,CAAC;IACnC,CAAC,MAAM,IAAIF,IAAI,KAAK,QAAQ,EAAE;MAC1BE,QAAQ,CAAC,mCAAmC,CAAC;IACjD,CAAC,MAAM,IAAIF,IAAI,KAAK,SAAS,EAAE;MAC3BE,QAAQ,CAAC,sCAAsCqB,MAAM,IAAIC,KAAK,IAAIC,OAAO,IAAIC,QAAQ,EAAE,CAAC;IAC5F,CAAC,MAAM,IAAI1B,IAAI,KAAK,KAAK,EAAE;MACvBE,QAAQ,CAAC,oCAAoC,CAAC;IAClD,CAAC,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;MAC7BE,QAAQ,CAAC,mCAAmC,CAAC;IACjD,CAAC,MAAM,IAAIF,IAAI,KAAK,cAAc,EAAE;MAChCE,QAAQ,CAAC,2BAA2BD,QAAQ,EAAE,CAAC;IACnD,CAAC,MAAM,IAAID,IAAI,KAAK,YAAY,EAAE;MAC9BE,QAAQ,CAAC,yBAAyBH,KAAK,EAAE,CAAC;IAC9C,CAAC,MAAM,IAAIC,IAAI,KAAK,YAAY,EAAE;MAC9BE,QAAQ,CAAC,6BAA6BH,KAAK,EAAE,CAAC;IAClD,CAAC,MAAM;MACHG,QAAQ,CAAC,oBAAoB,CAAC;IAClC;IAEA,OAAO,IAAI;EACf,CAAC;EAEDf,SAAS,CAAC,MAAM;IACZ,MAAMwC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACArB,UAAU,CAAC,IAAI,CAAC;QAEhB,IAAIN,IAAI,KAAK,QAAQ,EAAE;UACnB,MAAM4B,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,kBAAkB,EAAE;YAChD9B,KAAK;YACL+B,MAAM,EAAE7B;UACZ,CAAC,CAAC;UACFM,iBAAiB,CAACqB,QAAQ,CAACpB,IAAI,CAACuB,2BAA2B,CAAC;QAChE,CAAC,MACI,IAAI/B,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,EAAE;UAC3E,MAAM4B,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,0BAA0B,EAAE;YAAE9B;UAAM,CAAC,CAAC;UACtEQ,iBAAiB,CAACqB,QAAQ,CAACpB,IAAI,CAAC;QACpC,CAAC,MACI;UACD,MAAMoB,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,iBAAiB,EAAE;YAC/C9B,KAAK;YACLC;UACJ,CAAC,CAAC;UACFO,iBAAiB,CAACqB,QAAQ,CAACpB,IAAI,CAACwB,oBAAoB,CAAC;QACzD;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QACV7B,QAAQ,CAAC6B,GAAG,CAACxB,OAAO,IAAI,uBAAuB,CAAC;QAChDC,UAAU,CAAC,MAAMR,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,SAAS;QACNI,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAED,IAAIP,KAAK,EAAE;MACP4B,YAAY,CAAC,CAAC;IAClB,CAAC,MAAM;MACHzB,QAAQ,CAAC,QAAQ,CAAC;IACtB;EACJ,CAAC,EAAE,CAACH,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAErC,IAAIC,KAAK,EAAE;IACP,oBACIP,OAAA,CAACL,GAAG;MACA2C,OAAO,EAAC,MAAM;MACdC,aAAa,EAAC,QAAQ;MACtBC,UAAU,EAAC,QAAQ;MACnBC,cAAc,EAAC,QAAQ;MACvBC,SAAS,EAAC,OAAO;MAAAC,QAAA,gBAEjB3C,OAAA,CAACH,UAAU;QAAC+C,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAACC,YAAY;QAAAH,QAAA,EAC9CpC;MAAK;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACblD,OAAA,CAACH,UAAU;QAAC+C,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,oBACIlD,OAAA,CAACL,GAAG;IACA2C,OAAO,EAAC,MAAM;IACdC,aAAa,EAAC,QAAQ;IACtBC,UAAU,EAAC,QAAQ;IACnBC,cAAc,EAAC,QAAQ;IACvBC,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEjB3C,OAAA,CAACJ,gBAAgB;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpBlD,OAAA,CAACH,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAACO,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAAC;IAExC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAAChD,EAAA,CA/HID,WAAW;EAAA,QACqBR,SAAS,EAC1BC,WAAW;AAAA;AAAA2D,EAAA,GAF1BpD,WAAW;AAiIjB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}