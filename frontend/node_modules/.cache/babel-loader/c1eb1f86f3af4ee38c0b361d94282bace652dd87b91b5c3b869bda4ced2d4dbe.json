{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js\";\nimport React from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-loader-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loader\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please Re-Login Matrix and close this tab.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoginPage", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet';\n\nconst LoginPage = () => {\n\n    return (\n        <>\n            <div className=\"page-loader-wrapper\">\n                <div className=\"loader\">\n                    <p>Please Re-Login Matrix and close this tab.</p>\n                </div>\n            </div>\n        </>\n    );\n};\n\nexport default LoginPage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAEpB,oBACIH,OAAA,CAAAE,SAAA;IAAAE,QAAA,eACIJ,OAAA;MAAKK,SAAS,EAAC,qBAAqB;MAAAD,QAAA,eAChCJ,OAAA;QAAKK,SAAS,EAAC,QAAQ;QAAAD,QAAA,eACnBJ,OAAA;UAAAI,QAAA,EAAG;QAA0C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBACR,CAAC;AAEX,CAAC;AAACC,EAAA,GAXIP,SAAS;AAaf,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}