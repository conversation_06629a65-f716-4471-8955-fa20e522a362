{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const {\n    token,\n    type\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    login\n  } = useAuth();\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const validateAndLogin = async () => {\n      try {\n        // Validate token and get user info\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/validate-token`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            token,\n            type\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Invalid or expired token');\n        }\n        const data = await response.json();\n\n        // Login with the validated token\n        await login({\n          token,\n          ...data\n        });\n\n        // Redirect based on type if provided, otherwise go to dashboard\n        if (type) {\n          navigate(`/matrix/${type}`);\n        } else {\n          navigate('/matrix');\n        }\n      } catch (err) {\n        setError(err.message || 'Authentication failed');\n        // Redirect to login after a delay if there's an error\n        setTimeout(() => {\n          navigate('/login');\n        }, 3000);\n      }\n    };\n    if (token) {\n      validateAndLogin();\n    } else {\n      navigate('/login');\n    }\n  }, [token, type, login, navigate]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      minHeight: \"100vh\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        gutterBottom: true,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Redirecting to login page...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    minHeight: \"100vh\",\n    children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mt: 2\n      },\n      children: \"Authenticating...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 9\n  }, this);\n};\n_s(LandingPage, \"RSLgK/KjKqaGdVZLVrYbxiOeI3M=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useNavigate", "Box", "CircularProgress", "Typography", "useAuth", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "token", "type", "navigate", "login", "error", "setError", "validateAndLogin", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "data", "json", "err", "message", "setTimeout", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "color", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst LandingPage = () => {\n    const { token, type } = useParams();\n    const navigate = useNavigate();\n    const { login } = useAuth();\n    const [error, setError] = useState('');\n\n    useEffect(() => {\n        const validateAndLogin = async () => {\n            try {\n                // Validate token and get user info\n                const response = await fetch(`${process.env.REACT_APP_API_URL}/auth/validate-token`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    },\n                    body: JSON.stringify({ token, type })\n                });\n\n                if (!response.ok) {\n                    throw new Error('Invalid or expired token');\n                }\n\n                const data = await response.json();\n                \n                // Login with the validated token\n                await login({ token, ...data });\n\n                // Redirect based on type if provided, otherwise go to dashboard\n                if (type) {\n                    navigate(`/matrix/${type}`);\n                } else {\n                    navigate('/matrix');\n                }\n            } catch (err) {\n                setError(err.message || 'Authentication failed');\n                // Redirect to login after a delay if there's an error\n                setTimeout(() => {\n                    navigate('/login');\n                }, 3000);\n            }\n        };\n\n        if (token) {\n            validateAndLogin();\n        } else {\n            navigate('/login');\n        }\n    }, [token, type, login, navigate]);\n\n    if (error) {\n        return (\n            <Box\n                display=\"flex\"\n                flexDirection=\"column\"\n                alignItems=\"center\"\n                justifyContent=\"center\"\n                minHeight=\"100vh\"\n            >\n                <Typography variant=\"h6\" color=\"error\" gutterBottom>\n                    {error}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                    Redirecting to login page...\n                </Typography>\n            </Box>\n        );\n    }\n\n    return (\n        <Box\n            display=\"flex\"\n            flexDirection=\"column\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            minHeight=\"100vh\"\n        >\n            <CircularProgress />\n            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                Authenticating...\n            </Typography>\n        </Box>\n    );\n};\n\nexport default LandingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACjE,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGX,SAAS,CAAC,CAAC;EACnC,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAM,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACZ,MAAMkB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACA;QACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,sBAAsB,EAAE;UACjFC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhB,KAAK;YAAEC;UAAK,CAAC;QACxC,CAAC,CAAC;QAEF,IAAI,CAACM,QAAQ,CAACU,EAAE,EAAE;UACd,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;QAC/C;QAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;;QAElC;QACA,MAAMjB,KAAK,CAAC;UAAEH,KAAK;UAAE,GAAGmB;QAAK,CAAC,CAAC;;QAE/B;QACA,IAAIlB,IAAI,EAAE;UACNC,QAAQ,CAAC,WAAWD,IAAI,EAAE,CAAC;QAC/B,CAAC,MAAM;UACHC,QAAQ,CAAC,SAAS,CAAC;QACvB;MACJ,CAAC,CAAC,OAAOmB,GAAG,EAAE;QACVhB,QAAQ,CAACgB,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;QAChD;QACAC,UAAU,CAAC,MAAM;UACbrB,QAAQ,CAAC,QAAQ,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC;MACZ;IACJ,CAAC;IAED,IAAIF,KAAK,EAAE;MACPM,gBAAgB,CAAC,CAAC;IACtB,CAAC,MAAM;MACHJ,QAAQ,CAAC,QAAQ,CAAC;IACtB;EACJ,CAAC,EAAE,CAACF,KAAK,EAAEC,IAAI,EAAEE,KAAK,EAAED,QAAQ,CAAC,CAAC;EAElC,IAAIE,KAAK,EAAE;IACP,oBACIP,OAAA,CAACL,GAAG;MACAgC,OAAO,EAAC,MAAM;MACdC,aAAa,EAAC,QAAQ;MACtBC,UAAU,EAAC,QAAQ;MACnBC,cAAc,EAAC,QAAQ;MACvBC,SAAS,EAAC,OAAO;MAAAC,QAAA,gBAEjBhC,OAAA,CAACH,UAAU;QAACoC,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAACC,YAAY;QAAAH,QAAA,EAC9CzB;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACbvC,OAAA,CAACH,UAAU;QAACoC,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,oBACIvC,OAAA,CAACL,GAAG;IACAgC,OAAO,EAAC,MAAM;IACdC,aAAa,EAAC,QAAQ;IACtBC,UAAU,EAAC,QAAQ;IACnBC,cAAc,EAAC,QAAQ;IACvBC,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEjBhC,OAAA,CAACJ,gBAAgB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpBvC,OAAA,CAACH,UAAU;MAACoC,OAAO,EAAC,IAAI;MAACO,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAAC;IAExC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAACrC,EAAA,CAlFID,WAAW;EAAA,QACWR,SAAS,EAChBC,WAAW,EACVI,OAAO;AAAA;AAAA4C,EAAA,GAHvBzC,WAAW;AAoFjB,eAAeA,WAAW;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}