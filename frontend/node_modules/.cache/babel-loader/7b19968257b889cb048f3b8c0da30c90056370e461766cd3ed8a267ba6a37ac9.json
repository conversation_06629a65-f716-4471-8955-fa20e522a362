{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { UpdateUserProfile } from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditProfile = () => {\n  _s();\n  const navigate = useNavigate();\n  const [selectedProduct, setSelectedProduct] = useState({\n    ProductID: 0,\n    Name: 'Select'\n  });\n  const productList = [{\n    ProductID: 0,\n    Name: 'Select'\n  }, {\n    ProductID: 115,\n    Name: 'Investment'\n  }, {\n    ProductID: 7,\n    Name: 'Term'\n  }, {\n    ProductID: 2,\n    Name: 'Health'\n  }, {\n    ProductID: 117,\n    Name: 'Motor'\n  }, {\n    ProductID: 255,\n    Name: 'Others(Non- Ops, Product, HR, Tech Etc.)'\n  }];\n  setProductList(products);\n  setSelectedProduct(products[0]);\n  const handleSubmit = () => {\n    const user = JSON.parse(localStorage.getItem('UserDetails'));\n    if (selectedProduct.ProductID === 0) {\n      alert('Please select Product');\n      return;\n    }\n    const payload = {\n      EmpID: user.EMPData[0].EmpID,\n      BU: selectedProduct.ProductID\n    };\n    UpdateUserProfile(payload, user.Toket).then(res => {\n      if (res && res.Status > 0) {\n        alert('Successfully Updated.');\n        navigate('/matrix/MyTickets'); // adjust route if needed\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-lg-6 col-md-8 col-lg-12\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"breadcrumb adv_search\",\n            children: /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"breadcrumb-item active\",\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                children: \"Update BU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 68\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row clearfix\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mail-inbox\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mail-right agent_tkt_view\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"body ticket_detailbox\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mail_compose_Section\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card shadow_none\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"body compose_box\",\n                      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row clearfix\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-lg-6 col-md-6 col-sm-12\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"form-group grp1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                                children: \"Select BU\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 66,\n                                columnNumber: 65\n                              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                                className: \"form-control\",\n                                value: selectedProduct.ProductID,\n                                onChange: e => {\n                                  const selectedId = parseInt(e.target.value);\n                                  const selected = productList.find(p => p.ProductID === selectedId);\n                                  setSelectedProduct(selected);\n                                },\n                                children: productList.map((item, idx) => /*#__PURE__*/_jsxDEV(\"option\", {\n                                  value: item.ProductID,\n                                  children: item.Name\n                                }, idx, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 77,\n                                  columnNumber: 73\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 67,\n                                columnNumber: 65\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 65,\n                              columnNumber: 61\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 64,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 63,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 62,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"m-t-30 compose_action_button\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: \"btn btn-success\",\n                          onClick: handleSubmit,\n                          children: \"Submit\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 86,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 85,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 61,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this), \" \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 9\n  }, this);\n};\n_s(EditProfile, \"pwUn41ld/661BUl8cumm+hbeqmg=\", false, function () {\n  return [useNavigate];\n});\n_c = EditProfile;\nexport default EditProfile;\nvar _c;\n$RefreshReg$(_c, \"EditProfile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "UpdateUserProfile", "jsxDEV", "_jsxDEV", "EditProfile", "_s", "navigate", "selectedProduct", "setSelectedProduct", "ProductID", "Name", "productList", "setProductList", "products", "handleSubmit", "user", "JSON", "parse", "localStorage", "getItem", "alert", "payload", "EmpID", "EMPData", "BU", "Toket", "then", "res", "Status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "selectedId", "parseInt", "target", "selected", "find", "p", "map", "item", "idx", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { UpdateUserProfile } from '../services/feedbackService';\n\nconst EditProfile = () => {\n    const navigate = useNavigate();\n    const [selectedProduct, setSelectedProduct] = useState({ ProductID: 0, Name: 'Select' });\n\n    const productList = [\n        { ProductID: 0, Name: 'Select' },\n        { ProductID: 115, Name: 'Investment' },\n        { ProductID: 7, Name: 'Term' },\n        { ProductID: 2, Name: 'Health' },\n        { ProductID: 117, Name: 'Motor' },\n        { ProductID: 255, Name: 'Others(Non- Ops, Product, HR, Tech Etc.)' }\n    ];\n    setProductList(products);\n    setSelectedProduct(products[0]);\n  \n    const handleSubmit = () => {\n        const user = JSON.parse(localStorage.getItem('UserDetails'));\n\n        if (selectedProduct.ProductID === 0) {\n            alert('Please select Product');\n            return;\n        }\n\n        const payload = {\n            EmpID: user.EMPData[0].EmpID,\n            BU: selectedProduct.ProductID\n        };\n\n        UpdateUserProfile(payload, user.Toket).then((res) => {\n            if (res && res.Status > 0) {\n                alert('Successfully Updated.');\n                navigate('/matrix/MyTickets'); // adjust route if needed\n            }\n        });\n    };\n\n    return (\n        <div className=\"container-fluid\">\n            <div className=\"block-header\">\n                <div className=\"row\">\n                    <div className=\"col-lg-6 col-md-8 col-lg-12\">\n                        <ul className=\"breadcrumb adv_search\">\n                            <li className=\"breadcrumb-item active\"><b>Update BU</b></li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"row clearfix\">\n                <div className=\"col-lg-12\">\n                    <div className=\"card\">\n                        <div className=\"mail-inbox\">\n                            <div className=\"mail-right agent_tkt_view\">\n                                <div className=\"body ticket_detailbox\">\n                                    <div className=\"mail_compose_Section\">\n                                        <div className=\"card shadow_none\">\n                                            <div className=\"body compose_box\">\n                                                <form>\n                                                    <div className=\"row clearfix\">\n                                                        <div className=\"col-lg-6 col-md-6 col-sm-12\">\n                                                            <div className=\"form-group grp1\">\n                                                                <label>Select BU</label>\n                                                                <select\n                                                                    className=\"form-control\"\n                                                                    value={selectedProduct.ProductID}\n                                                                    onChange={(e) => {\n                                                                        const selectedId = parseInt(e.target.value);\n                                                                        const selected = productList.find(p => p.ProductID === selectedId);\n                                                                        setSelectedProduct(selected);\n                                                                    }}\n                                                                >\n                                                                    {productList.map((item, idx) => (\n                                                                        <option key={idx} value={item.ProductID}>{item.Name}</option>\n                                                                    ))}\n                                                                </select>\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </form>\n\n                                                <div className=\"m-t-30 compose_action_button\">\n                                                    <button type=\"button\" className=\"btn btn-success\" onClick={handleSubmit}>Submit</button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div> {/* row clearfix */}\n        </div>\n    );\n};\n\nexport default EditProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,eAAe,EAAEC,kBAAkB,CAAC,GAAGV,QAAQ,CAAC;IAAEW,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,CAAC;EAExF,MAAMC,WAAW,GAAG,CAChB;IAAEF,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,EAChC;IAAED,SAAS,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAa,CAAC,EACtC;IAAED,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC9B;IAAED,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,EAChC;IAAED,SAAS,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACjC;IAAED,SAAS,EAAE,GAAG;IAAEC,IAAI,EAAE;EAA2C,CAAC,CACvE;EACDE,cAAc,CAACC,QAAQ,CAAC;EACxBL,kBAAkB,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE/B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;IAE5D,IAAIZ,eAAe,CAACE,SAAS,KAAK,CAAC,EAAE;MACjCW,KAAK,CAAC,uBAAuB,CAAC;MAC9B;IACJ;IAEA,MAAMC,OAAO,GAAG;MACZC,KAAK,EAAEP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;MAC5BE,EAAE,EAAEjB,eAAe,CAACE;IACxB,CAAC;IAEDR,iBAAiB,CAACoB,OAAO,EAAEN,IAAI,CAACU,KAAK,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAK;MACjD,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;QACvBR,KAAK,CAAC,uBAAuB,CAAC;QAC9Bd,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN,CAAC;EAED,oBACIH,OAAA;IAAK0B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5B3B,OAAA;MAAK0B,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzB3B,OAAA;QAAK0B,SAAS,EAAC,KAAK;QAAAC,QAAA,eAChB3B,OAAA;UAAK0B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eACxC3B,OAAA;YAAI0B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACjC3B,OAAA;cAAI0B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eAAC3B,OAAA;gBAAA2B,QAAA,EAAG;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN/B,OAAA;MAAK0B,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzB3B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB3B,OAAA;UAAK0B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACjB3B,OAAA;YAAK0B,SAAS,EAAC,YAAY;YAAAC,QAAA,eACvB3B,OAAA;cAAK0B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACtC3B,OAAA;gBAAK0B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClC3B,OAAA;kBAAK0B,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eACjC3B,OAAA;oBAAK0B,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC7B3B,OAAA;sBAAK0B,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC7B3B,OAAA;wBAAA2B,QAAA,eACI3B,OAAA;0BAAK0B,SAAS,EAAC,cAAc;0BAAAC,QAAA,eACzB3B,OAAA;4BAAK0B,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,eACxC3B,OAAA;8BAAK0B,SAAS,EAAC,iBAAiB;8BAAAC,QAAA,gBAC5B3B,OAAA;gCAAA2B,QAAA,EAAO;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACxB/B,OAAA;gCACI0B,SAAS,EAAC,cAAc;gCACxBM,KAAK,EAAE5B,eAAe,CAACE,SAAU;gCACjC2B,QAAQ,EAAGC,CAAC,IAAK;kCACb,MAAMC,UAAU,GAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;kCAC3C,MAAMM,QAAQ,GAAG9B,WAAW,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClC,SAAS,KAAK6B,UAAU,CAAC;kCAClE9B,kBAAkB,CAACiC,QAAQ,CAAC;gCAChC,CAAE;gCAAAX,QAAA,EAEDnB,WAAW,CAACiC,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACvB3C,OAAA;kCAAkBgC,KAAK,EAAEU,IAAI,CAACpC,SAAU;kCAAAqB,QAAA,EAAEe,IAAI,CAACnC;gCAAI,GAAtCoC,GAAG;kCAAAf,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAA4C,CAC/D;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAEP/B,OAAA;wBAAK0B,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,eACzC3B,OAAA;0BAAQ4C,IAAI,EAAC,QAAQ;0BAAClB,SAAS,EAAC,iBAAiB;0BAACmB,OAAO,EAAElC,YAAa;0BAAAgB,QAAA,EAAC;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,KAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAC7B,EAAA,CA9FID,WAAW;EAAA,QACIJ,WAAW;AAAA;AAAAiD,EAAA,GAD1B7C,WAAW;AAgGjB,eAAeA,WAAW;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}