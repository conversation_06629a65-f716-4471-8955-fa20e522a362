{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js\",\n  _s = $RefreshSig$();\n/* eslint-disable eqeqeq */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { formatDate, getUserId } from '../services/CommonHelper';\nimport { GetDocumentUrl, GetTicketDetails, UpdateTicketRemarks, UploadFile } from '../services/feedbackService';\nimport '../styles/MyTicketDetails.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyTicketDetails = () => {\n  _s();\n  var _ticketDetails$Assign, _ticketDetails$Assign2, _selected$Status;\n  const {\n    ticketId\n  } = useParams();\n  const [ticketDetails, setTicketDetails] = useState([]);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [fileAttachments, setFileAttachments] = useState([]);\n  const [isSatisfied, setIsSatisfied] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: {\n      SourceID: null\n    }\n  });\n  const statusList = [{\n    StatusID: 3,\n    StatusName: \"Resolved\"\n  }];\n  const isEmpty = str => {\n    return typeof str === 'string' && !str.trim() || typeof str === 'undefined' || str === null;\n  };\n  const getTicketDetailsService = () => {\n    const reqData = {\n      ticketId: ticketId\n    };\n    GetTicketDetails(reqData).then(response => {\n      if (response) {\n        setTicketDetails(response);\n        setCommentList(response.Commentlist || []);\n        if (response.StatusID == 3) {\n          statusList.push({\n            StatusID: 4,\n            StatusName: \"Closed\"\n          });\n        }\n        setSelected(prev => ({\n          ...prev,\n          Status: {\n            StatusID: response.StatusID\n          },\n          IssueType: {\n            ISSUEID: response.IssueID\n          }\n        }));\n      } else {\n        setTicketDetails([]);\n        setCommentList([]);\n        setSelected({\n          Status: undefined,\n          IssueType: undefined,\n          SubIssueType: undefined,\n          Source: {\n            SourceID: null\n          }\n        });\n      }\n    }).catch(() => {\n      setTicketDetails([]);\n      setCommentList([]);\n      setSelected({\n        Status: undefined,\n        IssueType: undefined,\n        SubIssueType: undefined,\n        Source: {\n          SourceID: null\n        }\n      });\n    }).finally(() => {\n      setIsLoading(false);\n    });\n  };\n  useEffect(() => {\n    getTicketDetailsService();\n  }, [ticketId]);\n  const handleFileChange = event => {\n    const files = Array.from(event.target.files);\n    const fileData = [];\n    files.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        const binaryStr = e.target.result;\n        fileData.push({\n          FileName: file.name,\n          AttachemntContent: btoa(binaryStr),\n          AttachmentURL: \"\",\n          ContentType: file.type\n        });\n        if (fileData.length === files.length) {\n          setFileAttachments(fileData);\n        }\n      };\n      reader.readAsBinaryString(file);\n    });\n  };\n  const updateTicketRemarks = replyType => {\n    if (!isSatisfied && (isEmpty(ticketReply) || ticketReply.length <= 10)) {\n      toast.error(isEmpty(ticketReply) ? 'Remark should not be blank' : 'Query should be more than 10 char');\n      return;\n    }\n    let ticketStatusId = 0;\n    if (selected.Status.StatusID === 3) {\n      ticketStatusId = 5;\n    }\n    if (isSatisfied === 1) {\n      ticketStatusId = 4;\n    }\n    const requestData = {\n      TicketID: ticketId,\n      Comments: `Comments : ${ticketReply}`,\n      CreatedBy: getUserId(),\n      StatusID: ticketStatusId,\n      ReplyType: replyType,\n      FileURL: \"\",\n      FileName: \"\",\n      IsSatisfied: isSatisfied,\n      LeadID: ticketDetails.LeadID,\n      ParentID: ticketDetails.ParentID,\n      PayID: ticketDetails.PayID,\n      OrderID: ticketDetails.OrderID,\n      IsStatusChanged: ticketDetails.StatusID !== selected.Status.StatusID\n    };\n    if (fileAttachments.length > 0) {\n      UploadFile(fileAttachments).then(response => {\n        const FileAttachments = response;\n        requestData.FileURL = FileAttachments[0].AttachmentURL;\n        requestData.FileName = FileAttachments[0].FileName;\n        requestData.RefId = FileAttachments[0].RefId;\n        UpdateTicketRemarksService(requestData);\n      }).catch(() => {\n        return;\n      });\n    } else {\n      UpdateTicketRemarksService(requestData);\n    }\n  };\n  const UpdateTicketRemarksService = requestData => {\n    UpdateTicketRemarks(requestData).then(response => {\n      if (response) {\n        toast.success('Updated successfully');\n        setFileAttachments([]);\n        setTicketReply('');\n        getTicketDetailsService();\n      } else {\n        toast.error('Error updating ticket');\n      }\n    }).catch(() => {});\n  };\n  const getDocumentUrl = (docId, refId) => {\n    const requestData = {\n      \"docId\": docId,\n      \"RefId\": refId\n    };\n    GetDocumentUrl(requestData).then(response => {\n      const data = response.data;\n      if (data !== null && data !== void 0 && data.ttlDocUrl) {\n        window.open(data.ttlDocUrl, '_blank');\n      }\n    }).catch(() => {});\n  };\n  const openInNewTab = url => {\n    if (url) {\n      window.open(url, '_blank');\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid ticketdetails\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"block-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"col-lg-6 col-md-8 col-lg-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"detail_links\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              class: \"demo-button\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"assign_hd\",\n                children: \"Assigned To :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                class: \"btn btn-outline-primary\",\n                children: [(ticketDetails === null || ticketDetails === void 0 ? void 0 : (_ticketDetails$Assign = ticketDetails.AssignToDetails) === null || _ticketDetails$Assign === void 0 ? void 0 : _ticketDetails$Assign.Name) || 'Not assigned', (ticketDetails === null || ticketDetails === void 0 ? void 0 : (_ticketDetails$Assign2 = ticketDetails.AssignToDetails) === null || _ticketDetails$Assign2 === void 0 ? void 0 : _ticketDetails$Assign2.EmployeeID) && `(${ticketDetails.AssignToDetails.EmployeeID})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"row clearfix\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"col-lg-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"mail-inbox\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"mail-right agent_tkt_view\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"body ticket_detailbox\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"tab-content table_databox\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"tab-pane show active\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"table-responsive\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table m-b-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"FeedbackID\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 232,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"CreatedOn\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 233,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Process\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 234,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Feedback\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 235,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Status\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 236,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Last Updated on\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 237,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 231,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 230,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            class: \"active_detaillist\",\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketDisplayID\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 242,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: formatDate(ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.CreatedOn)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 243,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.Process\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 244,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.IssueStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 245,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 246,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: formatDate(ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.UpdatedOn)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 247,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 241,\n                            columnNumber: 57\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: commentList && commentList.length > 0 && /*#__PURE__*/_jsxDEV(\"td\", {\n                              colspan: 7,\n                              className: \"tkt_detailbox\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"card detialbox\",\n                                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"body emailer_body\",\n                                  children: [commentList.map((comment, index) => {\n                                    var _comment$User, _comment$User2;\n                                    return (comment.ReplyType == 1 || comment.ReplyType == 2) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: `timeline-item detail_data ${comment.ReplyType === 1 ? 'green' : comment.ReplyType === 2 ? 'blue' : ''}`,\n                                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                        style: {\n                                          margin: '0 0 14px 0'\n                                        },\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"date\",\n                                          children: [\"From : \", /*#__PURE__*/_jsxDEV(\"a\", {\n                                            children: [(_comment$User = comment.User) === null || _comment$User === void 0 ? void 0 : _comment$User.UserName, \"(\", (_comment$User2 = comment.User) === null || _comment$User2 === void 0 ? void 0 : _comment$User2.EmployeeId, \")\"]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 260,\n                                            columnNumber: 104\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 259,\n                                          columnNumber: 93\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"right_section\",\n                                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                            className: \"sl-date\",\n                                            children: formatDate(comment.CreatedOn)\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 263,\n                                            columnNumber: 97\n                                          }, this)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 262,\n                                          columnNumber: 93\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          style: {\n                                            width: '100%',\n                                            display: 'block'\n                                          },\n                                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                                            children: comment.Comment\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 266,\n                                            columnNumber: 97\n                                          }, this), comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') === -1 && /*#__PURE__*/_jsxDEV(\"a\", {\n                                            style: {\n                                              cursor: 'pointer',\n                                              textDecoration: 'underline',\n                                              color: '#007bff'\n                                            },\n                                            onClick: () => getDocumentUrl(comment.FileURL, comment.RefId),\n                                            children: comment.FileName\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 268,\n                                            columnNumber: 101\n                                          }, this), comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') !== -1 && /*#__PURE__*/_jsxDEV(\"a\", {\n                                            style: {\n                                              cursor: 'pointer',\n                                              textDecoration: 'underline',\n                                              color: '#007bff'\n                                            },\n                                            onClick: () => openInNewTab(comment.FileURL),\n                                            children: ticketDetails.RefTicketID\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 275,\n                                            columnNumber: 101\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 265,\n                                          columnNumber: 93\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 258,\n                                        columnNumber: 89\n                                      }, this)\n                                    }, index, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 257,\n                                      columnNumber: 85\n                                    }, this);\n                                  }), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"mail_compose_Section\",\n                                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"card shadow_none\",\n                                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: \"body compose_box\",\n                                        children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 290,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"header sub_hd\",\n                                          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                                            children: \"Add comment\"\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 292,\n                                            columnNumber: 93\n                                          }, this)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 291,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                                          className: \"form-control mb-3\",\n                                          style: {\n                                            width: '100%'\n                                          },\n                                          rows: \"10\",\n                                          value: ticketReply,\n                                          onChange: e => setTicketReply(e.target.value),\n                                          placeholder: \"Enter your comment...\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 294,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"m-t-30 compose_action_button\",\n                                          children: [selected.Status.StatusID === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                            style: {\n                                              fontWeight: 'bold',\n                                              margin: '4px'\n                                            },\n                                            children: [\"This feedback has been marked as resolved, if satisfied please mark 'Yes' to close else 'No' to re-open the same.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 306,\n                                              columnNumber: 131\n                                            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 306,\n                                              columnNumber: 137\n                                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"form-check form-check-inline\",\n                                              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                                type: \"radio\",\n                                                className: \"form-check-input\",\n                                                id: \"satisfiedYes\",\n                                                name: \"satisfied\",\n                                                value: \"1\",\n                                                checked: isSatisfied === 1,\n                                                onChange: e => setIsSatisfied(Number(e.target.value))\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 308,\n                                                columnNumber: 105\n                                              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                                className: \"form-check-label\",\n                                                htmlFor: \"satisfiedYes\",\n                                                children: \"Yes\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 317,\n                                                columnNumber: 105\n                                              }, this)]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 307,\n                                              columnNumber: 101\n                                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"form-check form-check-inline\",\n                                              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                                type: \"radio\",\n                                                className: \"form-check-input\",\n                                                id: \"satisfiedNo\",\n                                                name: \"satisfied\",\n                                                value: \"2\",\n                                                checked: isSatisfied === 2,\n                                                onChange: e => setIsSatisfied(Number(e.target.value))\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 320,\n                                                columnNumber: 105\n                                              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                                className: \"form-check-label\",\n                                                htmlFor: \"satisfiedNo\",\n                                                children: \"No\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 329,\n                                                columnNumber: 105\n                                              }, this)]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 319,\n                                              columnNumber: 101\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 304,\n                                            columnNumber: 97\n                                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                            style: {\n                                              display: 'none',\n                                              width: '100%',\n                                              padding: '0 0 10px 0'\n                                            },\n                                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                                              children: \"Choose Status\"\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 334,\n                                              columnNumber: 97\n                                            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                                              className: \"form-control\",\n                                              value: ((_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID) || '',\n                                              onChange: e => setSelected(prev => ({\n                                                ...prev,\n                                                Status: {\n                                                  StatusID: parseInt(e.target.value)\n                                                }\n                                              })),\n                                              disabled: false // Replace with your inActive condition\n                                              ,\n                                              style: {\n                                                display: 'inline-block',\n                                                width: 'auto',\n                                                margin: '0 5px 0 0'\n                                              },\n                                              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Status\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 345,\n                                                columnNumber: 101\n                                              }, this), statusList.filter((status, index, self) => index === self.findIndex(s => s.StatusID === status.StatusID)).sort((a, b) => (a.Sequence || 0) - (b.Sequence || 0)).map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                                                value: status.StatusID,\n                                                children: status.StatusName\n                                              }, status.StatusID, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 351,\n                                                columnNumber: 109\n                                              }, this))]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 335,\n                                              columnNumber: 97\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 333,\n                                            columnNumber: 93\n                                          }, this), selected.Status.StatusID != 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                            className: \"upload_box ng-scope\",\n                                            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                              type: \"file\",\n                                              id: \"file-3\",\n                                              className: \"inputfile inputfile-3 d-none\",\n                                              onChange: handleFileChange,\n                                              multiple: true,\n                                              accept: \".jpg,.pdf,.xlsx\"\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 360,\n                                              columnNumber: 101\n                                            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                              htmlFor: \"file-3\",\n                                              className: \"upload_docs\",\n                                              style: {\n                                                display: 'inline'\n                                              },\n                                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"20\",\n                                                height: \"17\",\n                                                viewBox: \"0 0 20 17\",\n                                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                                  d: \"M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z\"\n                                                }, void 0, false, {\n                                                  fileName: _jsxFileName,\n                                                  lineNumber: 370,\n                                                  columnNumber: 109\n                                                }, this)\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 369,\n                                                columnNumber: 105\n                                              }, this)\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 368,\n                                              columnNumber: 101\n                                            }, this), fileAttachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"mt-2\",\n                                              children: fileAttachments.map((file, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                                                className: \"attachment_files\",\n                                                children: [file.FileName, /*#__PURE__*/_jsxDEV(\"em\", {\n                                                  className: \"close_btn\",\n                                                  children: \"X\"\n                                                }, void 0, false, {\n                                                  fileName: _jsxFileName,\n                                                  lineNumber: 379,\n                                                  columnNumber: 117\n                                                }, this)]\n                                              }, index, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 376,\n                                                columnNumber: 113\n                                              }, this))\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 374,\n                                              columnNumber: 105\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 359,\n                                            columnNumber: 97\n                                          }, this), selected.Status.StatusID != 4 && /*#__PURE__*/_jsxDEV(\"button\", {\n                                            type: \"button\",\n                                            className: \"btn btn-success\",\n                                            onClick: () => updateTicketRemarks(1),\n                                            children: \"Submit\"\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 387,\n                                            columnNumber: 97\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 302,\n                                          columnNumber: 89\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 289,\n                                        columnNumber: 85\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 288,\n                                      columnNumber: 81\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 287,\n                                    columnNumber: 77\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 253,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 252,\n                                columnNumber: 69\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 251,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 249,\n                            columnNumber: 57\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 240,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 9\n  }, this);\n};\n_s(MyTicketDetails, \"e6A8WVtwsdeeVF6pEATZqQk8WG0=\", false, function () {\n  return [useParams];\n});\n_c = MyTicketDetails;\nexport default MyTicketDetails;\nvar _c;\n$RefreshReg$(_c, \"MyTicketDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "toast", "formatDate", "getUserId", "GetDocumentUrl", "GetTicketDetails", "UpdateTicketRemarks", "UploadFile", "jsxDEV", "_jsxDEV", "MyTicketDetails", "_s", "_ticketDetails$Assign", "_ticketDetails$Assign2", "_selected$Status", "ticketId", "ticketDetails", "setTicketDetails", "commentList", "setCommentList", "ticketReply", "setTicketReply", "fileAttachments", "setFileAttachments", "isSatisfied", "setIsSatisfied", "isLoading", "setIsLoading", "selected", "setSelected", "Status", "undefined", "IssueType", "SubIssueType", "Source", "SourceID", "statusList", "StatusID", "StatusName", "isEmpty", "str", "trim", "getTicketDetailsService", "reqData", "then", "response", "Commentlist", "push", "prev", "ISSUEID", "IssueID", "catch", "finally", "handleFileChange", "event", "files", "Array", "from", "target", "fileData", "for<PERSON>ach", "file", "reader", "FileReader", "onload", "e", "binaryStr", "result", "FileName", "name", "AttachemntContent", "btoa", "AttachmentURL", "ContentType", "type", "length", "readAsBinaryString", "updateTicketRemarks", "replyType", "error", "ticketStatusId", "requestData", "TicketID", "Comments", "CreatedBy", "ReplyType", "FileURL", "IsSatisfied", "LeadID", "ParentID", "PayID", "OrderID", "IsStatusChanged", "FileAttachments", "RefId", "UpdateTicketRemarksService", "success", "getDocumentUrl", "docId", "refId", "data", "ttlDocUrl", "window", "open", "openInNewTab", "url", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "class", "AssignToDetails", "Name", "EmployeeID", "TicketDisplayID", "CreatedOn", "Process", "IssueStatus", "TicketStatus", "UpdatedOn", "colspan", "map", "comment", "index", "_comment$User", "_comment$User2", "style", "margin", "User", "UserName", "EmployeeId", "width", "display", "Comment", "indexOf", "cursor", "textDecoration", "color", "onClick", "RefTicketID", "rows", "value", "onChange", "placeholder", "fontWeight", "id", "checked", "Number", "htmlFor", "padding", "parseInt", "disabled", "filter", "status", "self", "findIndex", "s", "sort", "a", "b", "Sequence", "multiple", "accept", "xmlns", "height", "viewBox", "d", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js"], "sourcesContent": ["/* eslint-disable eqeqeq */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { formatDate, getUserId } from '../services/CommonHelper';\nimport { GetDocumentUrl, GetTicketDetails, UpdateTicketRemarks, UploadFile } from '../services/feedbackService';\nimport '../styles/MyTicketDetails.css';\n\nconst MyTicketDetails = () => {\n    const { ticketId } = useParams();\n    const [ticketDetails, setTicketDetails] = useState([]);\n    const [commentList, setCommentList] = useState([]);\n    const [ticketReply, setTicketReply] = useState('');\n    const [fileAttachments, setFileAttachments] = useState([]);\n    const [isSatisfied, setIsSatisfied] = useState(0);\n    const [isLoading, setIsLoading] = useState(true);\n    const [selected, setSelected] = useState({\n        Status: undefined,\n        IssueType: undefined,\n        SubIssueType: undefined,\n        Source: { SourceID: null }\n    });\n\n    const statusList = [\n        { StatusID: 3, StatusName: \"Resolved\" }\n    ];\n\n    const isEmpty = (str) => {\n        return typeof str === 'string' && !str.trim() || typeof str === 'undefined' || str === null;\n    };\n\n    const getTicketDetailsService = () => {\n        const reqData = {\n            ticketId: ticketId,\n        }\n\n        GetTicketDetails(reqData)\n            .then((response) => {\n                if (response) {\n                    setTicketDetails(response);\n                    setCommentList(response.Commentlist || []);\n\n                    if (response.StatusID == 3) {\n                        statusList.push({ StatusID: 4, StatusName: \"Closed\" });\n                    }\n\n                    setSelected(prev => ({\n                        ...prev,\n                        Status: { StatusID: response.StatusID },\n                        IssueType: { ISSUEID: response.IssueID }\n                    }));\n                } else {\n                    setTicketDetails([]);\n                    setCommentList([]);\n                    setSelected({\n                        Status: undefined,\n                        IssueType: undefined,\n                        SubIssueType: undefined,\n                        Source: { SourceID: null }\n                    });\n                }\n            })\n            .catch(() => {\n                setTicketDetails([]);\n                setCommentList([]);\n                setSelected({\n                    Status: undefined,\n                    IssueType: undefined,\n                    SubIssueType: undefined,\n                    Source: { SourceID: null }\n                });\n            })\n            .finally(() => {\n                setIsLoading(false);\n            });\n    };\n\n    useEffect(() => {\n        getTicketDetailsService();\n    }, [ticketId]);\n\n\n    const handleFileChange = (event) => {\n        const files = Array.from(event.target.files);\n        const fileData = [];\n\n        files.forEach(file => {\n            const reader = new FileReader();\n            reader.onload = (e) => {\n                const binaryStr = e.target.result;\n                fileData.push({\n                    FileName: file.name,\n                    AttachemntContent: btoa(binaryStr),\n                    AttachmentURL: \"\",\n                    ContentType: file.type\n                });\n\n                if (fileData.length === files.length) {\n                    setFileAttachments(fileData);\n                }\n            };\n            reader.readAsBinaryString(file);\n        });\n    };\n\n    const updateTicketRemarks = (replyType) => {\n        if (!isSatisfied && (isEmpty(ticketReply) || ticketReply.length <= 10)) {\n            toast.error(isEmpty(ticketReply) ? 'Remark should not be blank' : 'Query should be more than 10 char');\n            return;\n        }\n\n        let ticketStatusId = 0;\n        if (selected.Status.StatusID === 3) {\n            ticketStatusId = 5;\n        }\n        if (isSatisfied === 1) {\n            ticketStatusId = 4;\n        }\n\n        const requestData = {\n            TicketID: ticketId,\n            Comments: `Comments : ${ticketReply}`,\n            CreatedBy: getUserId(),\n            StatusID: ticketStatusId,\n            ReplyType: replyType,\n            FileURL: \"\",\n            FileName: \"\",\n            IsSatisfied: isSatisfied,\n            LeadID: ticketDetails.LeadID,\n            ParentID: ticketDetails.ParentID,\n            PayID: ticketDetails.PayID,\n            OrderID: ticketDetails.OrderID,\n            IsStatusChanged: ticketDetails.StatusID !== selected.Status.StatusID\n        };\n\n        if (fileAttachments.length > 0) {\n            \n            UploadFile(fileAttachments)\n            .then((response) => {\n                const FileAttachments = response;\n                requestData.FileURL = FileAttachments[0].AttachmentURL;\n                requestData.FileName = FileAttachments[0].FileName;\n                requestData.RefId = FileAttachments[0].RefId;\n\n                UpdateTicketRemarksService(requestData);\n            })\n            .catch(() => {\n                return;\n            })\n        } else {\n            UpdateTicketRemarksService(requestData);\n        }\n    };\n\n    const UpdateTicketRemarksService = (requestData) => {\n        UpdateTicketRemarks(requestData)\n        .then ((response) => {\n            if(response) {\n                toast.success('Updated successfully');\n                setFileAttachments([]);\n                setTicketReply('');\n                getTicketDetailsService();\n            }\n            else {\n                toast.error('Error updating ticket');\n            }\n        })\n        .catch(() => {\n\n        })\n    }\n \n    const getDocumentUrl = (docId, refId) => {\n        const requestData = {\n            \"docId\": docId,\n            \"RefId\": refId\n        }\n\n        GetDocumentUrl(requestData)\n        .then((response) => {\n            const data = response.data;\n            if (data?.ttlDocUrl) {\n                window.open(data.ttlDocUrl, '_blank');\n            }\n        })\n        .catch(() => {\n\n        })   \n    };\n\n    const openInNewTab = (url) => {\n        if (url) {\n            window.open(url, '_blank');\n        }\n    };\n\n    if (isLoading) {\n        return <div>Loading...</div>;\n    }\n\n    return (\n        <div className=\"container-fluid ticketdetails\">\n            <div class=\"block-header\">\n                <div class=\"row\">\n                    <div class=\"col-lg-6 col-md-8 col-lg-12\">\n                        <div class=\"detail_links\">\n                            <p class=\"demo-button\">\n                                <span class=\"assign_hd\">Assigned To :</span>\n                                <button type=\"button\" class=\"btn btn-outline-primary\">{ticketDetails?.AssignToDetails?.Name || 'Not assigned'}\n                                    {ticketDetails?.AssignToDetails?.EmployeeID &&\n                                        `(${ticketDetails.AssignToDetails.EmployeeID})`}\n                                </button>\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"row clearfix\">\n                <div class=\"col-lg-12\">\n                    <div class=\"card\">\n                        <div class=\"mail-inbox\">\n                            <div class=\"mail-right agent_tkt_view\">\n                                <div class=\"body ticket_detailbox\">\n                                    <div class=\"tab-content table_databox\">\n                                        <div class=\"tab-pane show active\">\n                                            <div className=\"table-responsive\">\n                                                <table className=\"table m-b-0\">\n                                                    <thead>\n                                                        <tr>\n                                                            <th>FeedbackID</th>\n                                                            <th>CreatedOn</th>\n                                                            <th>Process</th>\n                                                            <th>Feedback</th>\n                                                            <th>Status</th>\n                                                            <th>Last Updated on</th>\n                                                        </tr>\n                                                    </thead>\n                                                    <tbody>\n                                                        <tr class=\"active_detaillist\">\n                                                            <td>{ticketDetails?.TicketDisplayID}</td>\n                                                            <td>{formatDate(ticketDetails?.CreatedOn)}</td>\n                                                            <td>{ticketDetails?.Process}</td>\n                                                            <td>{ticketDetails?.IssueStatus}</td>\n                                                            <td>{ticketDetails?.TicketStatus}</td>\n                                                            <td>{formatDate(ticketDetails?.UpdatedOn)}</td>\n                                                        </tr>\n                                                        <tr>\n                                                            {commentList && commentList.length > 0 && (\n                                                                <td colspan={7} className=\"tkt_detailbox\">\n                                                                    <div className=\"card detialbox\">\n                                                                        <div className=\"body emailer_body\">\n                                                                            {commentList.map((comment, index) => (\n                                                                                (comment.ReplyType == 1 || comment.ReplyType == 2)\n                                                                                && (\n                                                                                    <div key={index} className={`timeline-item detail_data ${comment.ReplyType === 1 ? 'green' : comment.ReplyType === 2 ? 'blue' : ''}`}>\n                                                                                        <div style={{ margin: '0 0 14px 0' }}>\n                                                                                            <span className=\"date\">\n                                                                                                From : <a>{comment.User?.UserName}({comment.User?.EmployeeId})</a>\n                                                                                            </span>\n                                                                                            <div className=\"right_section\">\n                                                                                                <span className=\"sl-date\">{formatDate(comment.CreatedOn)}</span>\n                                                                                            </div>\n                                                                                            <div style={{ width: '100%', display: 'block' }}>\n                                                                                                <p>{comment.Comment}</p>\n                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') === -1 && (\n                                                                                                    <a\n                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}\n                                                                                                        onClick={() => getDocumentUrl(comment.FileURL, comment.RefId)}                                                                                                    >\n                                                                                                        {comment.FileName}\n                                                                                                    </a>)\n                                                                                                }\n                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') !== -1 && (\n                                                                                                    <a\n                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}\n                                                                                                        onClick={() => openInNewTab(comment.FileURL)}\n                                                                                                    >\n                                                                                                        {ticketDetails.RefTicketID}\n                                                                                                    </a>)\n                                                                                                }\n                                                                                            </div>\n                                                                                        </div>\n                                                                                    </div>\n                                                                                )\n                                                                            ))}\n                                                                            <div className=\"mail_compose_Section\">\n                                                                                <div className=\"card shadow_none\">\n                                                                                    <div className=\"body compose_box\">\n                                                                                        <hr />\n                                                                                        <div className=\"header sub_hd\">\n                                                                                            <h2>Add comment</h2>\n                                                                                        </div>  \n                                                                                        <textarea\n                                                                                            className=\"form-control mb-3\"\n                                                                                            style={{ width: '100%' }}\n                                                                                            rows=\"10\"\n                                                                                            value={ticketReply}\n                                                                                            onChange={(e) => setTicketReply(e.target.value)}\n                                                                                            placeholder=\"Enter your comment...\"\n                                                                                        />\n                                                                                        <div className=\"m-t-30 compose_action_button\">\n                                                                                            {selected.Status.StatusID === 3 && (\n                                                                                                <div style={{ fontWeight: 'bold', margin: '4px' }}>\n                                                                                                    This feedback has been marked as resolved, if satisfied please mark 'Yes' to close\n                                                                                                    else 'No' to re-open the same.<br /><br />\n                                                                                                    <div className=\"form-check form-check-inline\">\n                                                                                                        <input\n                                                                                                            type=\"radio\"\n                                                                                                            className=\"form-check-input\"\n                                                                                                            id=\"satisfiedYes\"\n                                                                                                            name=\"satisfied\"\n                                                                                                            value=\"1\"\n                                                                                                            checked={isSatisfied === 1}\n                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}\n                                                                                                        />\n                                                                                                        <label className=\"form-check-label\" htmlFor=\"satisfiedYes\">Yes</label>\n                                                                                                    </div>\n                                                                                                    <div className=\"form-check form-check-inline\">\n                                                                                                        <input\n                                                                                                            type=\"radio\"\n                                                                                                            className=\"form-check-input\"\n                                                                                                            id=\"satisfiedNo\"\n                                                                                                            name=\"satisfied\"\n                                                                                                            value=\"2\"\n                                                                                                            checked={isSatisfied === 2}\n                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}\n                                                                                                        />\n                                                                                                        <label className=\"form-check-label\" htmlFor=\"satisfiedNo\">No</label>\n                                                                                                    </div>\n                                                                                                </div>\n                                                                                            )}\n                                                                                            <div style={{ display: 'none', width: '100%', padding: '0 0 10px 0' }}>\n                                                                                                <label>Choose Status</label>\n                                                                                                <select \n                                                                                                    className=\"form-control\" \n                                                                                                    value={selected.Status?.StatusID || ''} \n                                                                                                    onChange={(e) => setSelected(prev => ({\n                                                                                                        ...prev,\n                                                                                                        Status: { StatusID: parseInt(e.target.value) }\n                                                                                                    }))}\n                                                                                                    disabled={false} // Replace with your inActive condition\n                                                                                                    style={{ display: 'inline-block', width: 'auto', margin: '0 5px 0 0' }}\n                                                                                                >\n                                                                                                    <option value=\"\">Select Status</option>\n                                                                                                    {statusList\n                                                                                                        .filter((status, index, self) => \n                                                                                                            index === self.findIndex(s => s.StatusID === status.StatusID))\n                                                                                                        .sort((a, b) => (a.Sequence || 0) - (b.Sequence || 0))\n                                                                                                        .map(status => (\n                                                                                                            <option key={status.StatusID} value={status.StatusID}>\n                                                                                                                {status.StatusName}\n                                                                                                            </option>\n                                                                                                        ))\n                                                                                                    }\n                                                                                                </select>\n                                                                                            </div>\n                                                                                            {selected.Status.StatusID != 4 && (\n                                                                                                <div className=\"upload_box ng-scope\">\n                                                                                                    <input\n                                                                                                        type=\"file\"\n                                                                                                        id=\"file-3\"\n                                                                                                        className=\"inputfile inputfile-3 d-none\"\n                                                                                                        onChange={handleFileChange}\n                                                                                                        multiple\n                                                                                                        accept=\".jpg,.pdf,.xlsx\"\n                                                                                                    />\n                                                                                                    <label htmlFor=\"file-3\" className=\"upload_docs\" style={{ display: 'inline' }}>\n                                                                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"17\" viewBox=\"0 0 20 17\">\n                                                                                                            <path d=\"M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z\" />\n                                                                                                        </svg>\n                                                                                                    </label>\n                                                                                                    {fileAttachments.length > 0 && (\n                                                                                                        <div className=\"mt-2\">\n                                                                                                            {fileAttachments.map((file, index) => (\n                                                                                                                <span key={index} \n                                                                                                                    className=\"attachment_files\">\n                                                                                                                    {file.FileName} \n                                                                                                                    <em className=\"close_btn\">X</em>\n                                                                                                                </span>\n                                                                                                            ))}\n                                                                                                        </div>\n                                                                                                    )}\n                                                                                                </div>\n                                                                                            )}\n                                                                                            {selected.Status.StatusID != 4 &&\n                                                                                                <button\n                                                                                                    type=\"button\"\n                                                                                                    className=\"btn btn-success\"\n                                                                                                    onClick={() => updateTicketRemarks(1)}\n                                                                                                >\n                                                                                                    Submit\n                                                                                                </button>\n                                                                                            }\n                                                                                        </div>\n                                                                                    </div>\n                                                                                </div>\n                                                                            </div>\n                                                                        </div>\n                                                                    </div>\n                                                                </td>\n                                                            )}\n                                                        </tr>\n                                                    </tbody>\n                                                </table>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default MyTicketDetails;"], "mappings": ";;AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,UAAU,EAAEC,SAAS,QAAQ,0BAA0B;AAChE,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,6BAA6B;AAC/G,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,gBAAA;EAC1B,MAAM;IAAEC;EAAS,CAAC,GAAGf,SAAS,CAAC,CAAC;EAChC,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IACrCgC,MAAM,EAAEC,SAAS;IACjBC,SAAS,EAAED,SAAS;IACpBE,YAAY,EAAEF,SAAS;IACvBG,MAAM,EAAE;MAAEC,QAAQ,EAAE;IAAK;EAC7B,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACf;IAAEC,QAAQ,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAW,CAAC,CAC1C;EAED,MAAMC,OAAO,GAAIC,GAAG,IAAK;IACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACA,GAAG,CAACC,IAAI,CAAC,CAAC,IAAI,OAAOD,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI;EAC/F,CAAC;EAED,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG;MACZ5B,QAAQ,EAAEA;IACd,CAAC;IAEDV,gBAAgB,CAACsC,OAAO,CAAC,CACpBC,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAIA,QAAQ,EAAE;QACV5B,gBAAgB,CAAC4B,QAAQ,CAAC;QAC1B1B,cAAc,CAAC0B,QAAQ,CAACC,WAAW,IAAI,EAAE,CAAC;QAE1C,IAAID,QAAQ,CAACR,QAAQ,IAAI,CAAC,EAAE;UACxBD,UAAU,CAACW,IAAI,CAAC;YAAEV,QAAQ,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAS,CAAC,CAAC;QAC1D;QAEAT,WAAW,CAACmB,IAAI,KAAK;UACjB,GAAGA,IAAI;UACPlB,MAAM,EAAE;YAAEO,QAAQ,EAAEQ,QAAQ,CAACR;UAAS,CAAC;UACvCL,SAAS,EAAE;YAAEiB,OAAO,EAAEJ,QAAQ,CAACK;UAAQ;QAC3C,CAAC,CAAC,CAAC;MACP,CAAC,MAAM;QACHjC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,cAAc,CAAC,EAAE,CAAC;QAClBU,WAAW,CAAC;UACRC,MAAM,EAAEC,SAAS;UACjBC,SAAS,EAAED,SAAS;UACpBE,YAAY,EAAEF,SAAS;UACvBG,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAK;QAC7B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDgB,KAAK,CAAC,MAAM;MACTlC,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBU,WAAW,CAAC;QACRC,MAAM,EAAEC,SAAS;QACjBC,SAAS,EAAED,SAAS;QACpBE,YAAY,EAAEF,SAAS;QACvBG,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAK;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC,CACDiB,OAAO,CAAC,MAAM;MACXzB,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;EACV,CAAC;EAED5B,SAAS,CAAC,MAAM;IACZ2C,uBAAuB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAAC3B,QAAQ,CAAC,CAAC;EAGd,MAAMsC,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACH,KAAK,CAAC;IAC5C,MAAMI,QAAQ,GAAG,EAAE;IAEnBJ,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;MAClB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACnB,MAAMC,SAAS,GAAGD,CAAC,CAACP,MAAM,CAACS,MAAM;QACjCR,QAAQ,CAACZ,IAAI,CAAC;UACVqB,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,iBAAiB,EAAEC,IAAI,CAACL,SAAS,CAAC;UAClCM,aAAa,EAAE,EAAE;UACjBC,WAAW,EAAEZ,IAAI,CAACa;QACtB,CAAC,CAAC;QAEF,IAAIf,QAAQ,CAACgB,MAAM,KAAKpB,KAAK,CAACoB,MAAM,EAAE;UAClCpD,kBAAkB,CAACoC,QAAQ,CAAC;QAChC;MACJ,CAAC;MACDG,MAAM,CAACc,kBAAkB,CAACf,IAAI,CAAC;IACnC,CAAC,CAAC;EACN,CAAC;EAED,MAAMgB,mBAAmB,GAAIC,SAAS,IAAK;IACvC,IAAI,CAACtD,WAAW,KAAKe,OAAO,CAACnB,WAAW,CAAC,IAAIA,WAAW,CAACuD,MAAM,IAAI,EAAE,CAAC,EAAE;MACpE1E,KAAK,CAAC8E,KAAK,CAACxC,OAAO,CAACnB,WAAW,CAAC,GAAG,4BAA4B,GAAG,mCAAmC,CAAC;MACtG;IACJ;IAEA,IAAI4D,cAAc,GAAG,CAAC;IACtB,IAAIpD,QAAQ,CAACE,MAAM,CAACO,QAAQ,KAAK,CAAC,EAAE;MAChC2C,cAAc,GAAG,CAAC;IACtB;IACA,IAAIxD,WAAW,KAAK,CAAC,EAAE;MACnBwD,cAAc,GAAG,CAAC;IACtB;IAEA,MAAMC,WAAW,GAAG;MAChBC,QAAQ,EAAEnE,QAAQ;MAClBoE,QAAQ,EAAE,cAAc/D,WAAW,EAAE;MACrCgE,SAAS,EAAEjF,SAAS,CAAC,CAAC;MACtBkC,QAAQ,EAAE2C,cAAc;MACxBK,SAAS,EAAEP,SAAS;MACpBQ,OAAO,EAAE,EAAE;MACXlB,QAAQ,EAAE,EAAE;MACZmB,WAAW,EAAE/D,WAAW;MACxBgE,MAAM,EAAExE,aAAa,CAACwE,MAAM;MAC5BC,QAAQ,EAAEzE,aAAa,CAACyE,QAAQ;MAChCC,KAAK,EAAE1E,aAAa,CAAC0E,KAAK;MAC1BC,OAAO,EAAE3E,aAAa,CAAC2E,OAAO;MAC9BC,eAAe,EAAE5E,aAAa,CAACqB,QAAQ,KAAKT,QAAQ,CAACE,MAAM,CAACO;IAChE,CAAC;IAED,IAAIf,eAAe,CAACqD,MAAM,GAAG,CAAC,EAAE;MAE5BpE,UAAU,CAACe,eAAe,CAAC,CAC1BsB,IAAI,CAAEC,QAAQ,IAAK;QAChB,MAAMgD,eAAe,GAAGhD,QAAQ;QAChCoC,WAAW,CAACK,OAAO,GAAGO,eAAe,CAAC,CAAC,CAAC,CAACrB,aAAa;QACtDS,WAAW,CAACb,QAAQ,GAAGyB,eAAe,CAAC,CAAC,CAAC,CAACzB,QAAQ;QAClDa,WAAW,CAACa,KAAK,GAAGD,eAAe,CAAC,CAAC,CAAC,CAACC,KAAK;QAE5CC,0BAA0B,CAACd,WAAW,CAAC;MAC3C,CAAC,CAAC,CACD9B,KAAK,CAAC,MAAM;QACT;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH4C,0BAA0B,CAACd,WAAW,CAAC;IAC3C;EACJ,CAAC;EAED,MAAMc,0BAA0B,GAAId,WAAW,IAAK;IAChD3E,mBAAmB,CAAC2E,WAAW,CAAC,CAC/BrC,IAAI,CAAGC,QAAQ,IAAK;MACjB,IAAGA,QAAQ,EAAE;QACT5C,KAAK,CAAC+F,OAAO,CAAC,sBAAsB,CAAC;QACrCzE,kBAAkB,CAAC,EAAE,CAAC;QACtBF,cAAc,CAAC,EAAE,CAAC;QAClBqB,uBAAuB,CAAC,CAAC;MAC7B,CAAC,MACI;QACDzC,KAAK,CAAC8E,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACJ,CAAC,CAAC,CACD5B,KAAK,CAAC,MAAM,CAEb,CAAC,CAAC;EACN,CAAC;EAED,MAAM8C,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrC,MAAMlB,WAAW,GAAG;MAChB,OAAO,EAAEiB,KAAK;MACd,OAAO,EAAEC;IACb,CAAC;IAED/F,cAAc,CAAC6E,WAAW,CAAC,CAC1BrC,IAAI,CAAEC,QAAQ,IAAK;MAChB,MAAMuD,IAAI,GAAGvD,QAAQ,CAACuD,IAAI;MAC1B,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,SAAS,EAAE;QACjBC,MAAM,CAACC,IAAI,CAACH,IAAI,CAACC,SAAS,EAAE,QAAQ,CAAC;MACzC;IACJ,CAAC,CAAC,CACDlD,KAAK,CAAC,MAAM,CAEb,CAAC,CAAC;EACN,CAAC;EAED,MAAMqD,YAAY,GAAIC,GAAG,IAAK;IAC1B,IAAIA,GAAG,EAAE;MACLH,MAAM,CAACC,IAAI,CAACE,GAAG,EAAE,QAAQ,CAAC;IAC9B;EACJ,CAAC;EAED,IAAI/E,SAAS,EAAE;IACX,oBAAOjB,OAAA;MAAAiG,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChC;EAEA,oBACIrG,OAAA;IAAKsG,SAAS,EAAC,+BAA+B;IAAAL,QAAA,gBAC1CjG,OAAA;MAAKuG,KAAK,EAAC,cAAc;MAAAN,QAAA,eACrBjG,OAAA;QAAKuG,KAAK,EAAC,KAAK;QAAAN,QAAA,eACZjG,OAAA;UAAKuG,KAAK,EAAC,6BAA6B;UAAAN,QAAA,eACpCjG,OAAA;YAAKuG,KAAK,EAAC,cAAc;YAAAN,QAAA,eACrBjG,OAAA;cAAGuG,KAAK,EAAC,aAAa;cAAAN,QAAA,gBAClBjG,OAAA;gBAAMuG,KAAK,EAAC,WAAW;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CrG,OAAA;gBAAQiE,IAAI,EAAC,QAAQ;gBAACsC,KAAK,EAAC,yBAAyB;gBAAAN,QAAA,GAAE,CAAA1F,aAAa,aAAbA,aAAa,wBAAAJ,qBAAA,GAAbI,aAAa,CAAEiG,eAAe,cAAArG,qBAAA,uBAA9BA,qBAAA,CAAgCsG,IAAI,KAAI,cAAc,EACxG,CAAAlG,aAAa,aAAbA,aAAa,wBAAAH,sBAAA,GAAbG,aAAa,CAAEiG,eAAe,cAAApG,sBAAA,uBAA9BA,sBAAA,CAAgCsG,UAAU,KACvC,IAAInG,aAAa,CAACiG,eAAe,CAACE,UAAU,GAAG;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENrG,OAAA;MAAKuG,KAAK,EAAC,cAAc;MAAAN,QAAA,eACrBjG,OAAA;QAAKuG,KAAK,EAAC,WAAW;QAAAN,QAAA,eAClBjG,OAAA;UAAKuG,KAAK,EAAC,MAAM;UAAAN,QAAA,eACbjG,OAAA;YAAKuG,KAAK,EAAC,YAAY;YAAAN,QAAA,eACnBjG,OAAA;cAAKuG,KAAK,EAAC,2BAA2B;cAAAN,QAAA,eAClCjG,OAAA;gBAAKuG,KAAK,EAAC,uBAAuB;gBAAAN,QAAA,eAC9BjG,OAAA;kBAAKuG,KAAK,EAAC,2BAA2B;kBAAAN,QAAA,eAClCjG,OAAA;oBAAKuG,KAAK,EAAC,sBAAsB;oBAAAN,QAAA,eAC7BjG,OAAA;sBAAKsG,SAAS,EAAC,kBAAkB;sBAAAL,QAAA,eAC7BjG,OAAA;wBAAOsG,SAAS,EAAC,aAAa;wBAAAL,QAAA,gBAC1BjG,OAAA;0BAAAiG,QAAA,eACIjG,OAAA;4BAAAiG,QAAA,gBACIjG,OAAA;8BAAAiG,QAAA,EAAI;4BAAU;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnBrG,OAAA;8BAAAiG,QAAA,EAAI;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBrG,OAAA;8BAAAiG,QAAA,EAAI;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChBrG,OAAA;8BAAAiG,QAAA,EAAI;4BAAQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACjBrG,OAAA;8BAAAiG,QAAA,EAAI;4BAAM;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACfrG,OAAA;8BAAAiG,QAAA,EAAI;4BAAe;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACRrG,OAAA;0BAAAiG,QAAA,gBACIjG,OAAA;4BAAIuG,KAAK,EAAC,mBAAmB;4BAAAN,QAAA,gBACzBjG,OAAA;8BAAAiG,QAAA,EAAK1F,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoG;4BAAe;8BAAAT,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzCrG,OAAA;8BAAAiG,QAAA,EAAKxG,UAAU,CAACc,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqG,SAAS;4BAAC;8BAAAV,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAC/CrG,OAAA;8BAAAiG,QAAA,EAAK1F,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsG;4BAAO;8BAAAX,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACjCrG,OAAA;8BAAAiG,QAAA,EAAK1F,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuG;4BAAW;8BAAAZ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACrCrG,OAAA;8BAAAiG,QAAA,EAAK1F,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwG;4BAAY;8BAAAb,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACtCrG,OAAA;8BAAAiG,QAAA,EAAKxG,UAAU,CAACc,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyG,SAAS;4BAAC;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACLrG,OAAA;4BAAAiG,QAAA,EACKxF,WAAW,IAAIA,WAAW,CAACyD,MAAM,GAAG,CAAC,iBAClClE,OAAA;8BAAIiH,OAAO,EAAE,CAAE;8BAACX,SAAS,EAAC,eAAe;8BAAAL,QAAA,eACrCjG,OAAA;gCAAKsG,SAAS,EAAC,gBAAgB;gCAAAL,QAAA,eAC3BjG,OAAA;kCAAKsG,SAAS,EAAC,mBAAmB;kCAAAL,QAAA,GAC7BxF,WAAW,CAACyG,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;oCAAA,IAAAC,aAAA,EAAAC,cAAA;oCAAA,OAC5B,CAACH,OAAO,CAACvC,SAAS,IAAI,CAAC,IAAIuC,OAAO,CAACvC,SAAS,IAAI,CAAC,kBAE7C5E,OAAA;sCAAiBsG,SAAS,EAAE,6BAA6Ba,OAAO,CAACvC,SAAS,KAAK,CAAC,GAAG,OAAO,GAAGuC,OAAO,CAACvC,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,EAAG;sCAAAqB,QAAA,eACjIjG,OAAA;wCAAKuH,KAAK,EAAE;0CAAEC,MAAM,EAAE;wCAAa,CAAE;wCAAAvB,QAAA,gBACjCjG,OAAA;0CAAMsG,SAAS,EAAC,MAAM;0CAAAL,QAAA,GAAC,SACZ,eAAAjG,OAAA;4CAAAiG,QAAA,IAAAoB,aAAA,GAAIF,OAAO,CAACM,IAAI,cAAAJ,aAAA,uBAAZA,aAAA,CAAcK,QAAQ,EAAC,GAAC,GAAAJ,cAAA,GAACH,OAAO,CAACM,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAcK,UAAU,EAAC,GAAC;0CAAA;4CAAAzB,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAG,CAAC;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAChE,CAAC,eACPrG,OAAA;0CAAKsG,SAAS,EAAC,eAAe;0CAAAL,QAAA,eAC1BjG,OAAA;4CAAMsG,SAAS,EAAC,SAAS;4CAAAL,QAAA,EAAExG,UAAU,CAAC0H,OAAO,CAACP,SAAS;0CAAC;4CAAAV,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAO;wCAAC;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAC/D,CAAC,eACNrG,OAAA;0CAAKuH,KAAK,EAAE;4CAAEK,KAAK,EAAE,MAAM;4CAAEC,OAAO,EAAE;0CAAQ,CAAE;0CAAA5B,QAAA,gBAC5CjG,OAAA;4CAAAiG,QAAA,EAAIkB,OAAO,CAACW;0CAAO;4CAAA5B,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAI,CAAC,EACvBc,OAAO,CAACtC,OAAO,IAAIsC,OAAO,CAACtC,OAAO,KAAK,CAAC,CAAC,IAAIsC,OAAO,CAACtC,OAAO,CAACkD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,iBACjF/H,OAAA;4CACIuH,KAAK,EAAE;8CAACS,MAAM,EAAE,SAAS;8CAAEC,cAAc,EAAE,WAAW;8CAAEC,KAAK,EAAE;4CAAS,CAAE;4CAC1EC,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC2B,OAAO,CAACtC,OAAO,EAAEsC,OAAO,CAAC9B,KAAK,CAAE;4CAAAY,QAAA,EAC7DkB,OAAO,CAACxD;0CAAQ;4CAAAuC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAClB,CAAE,EAERc,OAAO,CAACtC,OAAO,IAAIsC,OAAO,CAACtC,OAAO,KAAK,CAAC,CAAC,IAAIsC,OAAO,CAACtC,OAAO,CAACkD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,iBACjF/H,OAAA;4CACIuH,KAAK,EAAE;8CAACS,MAAM,EAAE,SAAS;8CAAEC,cAAc,EAAE,WAAW;8CAAEC,KAAK,EAAE;4CAAS,CAAE;4CAC1EC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACoB,OAAO,CAACtC,OAAO,CAAE;4CAAAoB,QAAA,EAE5C1F,aAAa,CAAC6H;0CAAW;4CAAAlC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAC3B,CAAE;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAER,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACL;oCAAC,GA1BAe,KAAK;sCAAAlB,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OA2BV,CACR;kCAAA,CACJ,CAAC,eACFrG,OAAA;oCAAKsG,SAAS,EAAC,sBAAsB;oCAAAL,QAAA,eACjCjG,OAAA;sCAAKsG,SAAS,EAAC,kBAAkB;sCAAAL,QAAA,eAC7BjG,OAAA;wCAAKsG,SAAS,EAAC,kBAAkB;wCAAAL,QAAA,gBAC7BjG,OAAA;0CAAAkG,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAK,CAAC,eACNrG,OAAA;0CAAKsG,SAAS,EAAC,eAAe;0CAAAL,QAAA,eAC1BjG,OAAA;4CAAAiG,QAAA,EAAI;0CAAW;4CAAAC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAI;wCAAC;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OACnB,CAAC,eACNrG,OAAA;0CACIsG,SAAS,EAAC,mBAAmB;0CAC7BiB,KAAK,EAAE;4CAAEK,KAAK,EAAE;0CAAO,CAAE;0CACzBS,IAAI,EAAC,IAAI;0CACTC,KAAK,EAAE3H,WAAY;0CACnB4H,QAAQ,EAAG/E,CAAC,IAAK5C,cAAc,CAAC4C,CAAC,CAACP,MAAM,CAACqF,KAAK,CAAE;0CAChDE,WAAW,EAAC;wCAAuB;0CAAAtC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OACtC,CAAC,eACFrG,OAAA;0CAAKsG,SAAS,EAAC,8BAA8B;0CAAAL,QAAA,GACxC9E,QAAQ,CAACE,MAAM,CAACO,QAAQ,KAAK,CAAC,iBAC3B5B,OAAA;4CAAKuH,KAAK,EAAE;8CAAEkB,UAAU,EAAE,MAAM;8CAAEjB,MAAM,EAAE;4CAAM,CAAE;4CAAAvB,QAAA,GAAC,mHAEjB,eAAAjG,OAAA;8CAAAkG,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAK,CAAC,eAAArG,OAAA;8CAAAkG,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAK,CAAC,eAC1CrG,OAAA;8CAAKsG,SAAS,EAAC,8BAA8B;8CAAAL,QAAA,gBACzCjG,OAAA;gDACIiE,IAAI,EAAC,OAAO;gDACZqC,SAAS,EAAC,kBAAkB;gDAC5BoC,EAAE,EAAC,cAAc;gDACjB9E,IAAI,EAAC,WAAW;gDAChB0E,KAAK,EAAC,GAAG;gDACTK,OAAO,EAAE5H,WAAW,KAAK,CAAE;gDAC3BwH,QAAQ,EAAG/E,CAAC,IAAKxC,cAAc,CAAC4H,MAAM,CAACpF,CAAC,CAACP,MAAM,CAACqF,KAAK,CAAC;8CAAE;gDAAApC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAC3D,CAAC,eACFrG,OAAA;gDAAOsG,SAAS,EAAC,kBAAkB;gDAACuC,OAAO,EAAC,cAAc;gDAAA5C,QAAA,EAAC;8CAAG;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAO,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACrE,CAAC,eACNrG,OAAA;8CAAKsG,SAAS,EAAC,8BAA8B;8CAAAL,QAAA,gBACzCjG,OAAA;gDACIiE,IAAI,EAAC,OAAO;gDACZqC,SAAS,EAAC,kBAAkB;gDAC5BoC,EAAE,EAAC,aAAa;gDAChB9E,IAAI,EAAC,WAAW;gDAChB0E,KAAK,EAAC,GAAG;gDACTK,OAAO,EAAE5H,WAAW,KAAK,CAAE;gDAC3BwH,QAAQ,EAAG/E,CAAC,IAAKxC,cAAc,CAAC4H,MAAM,CAACpF,CAAC,CAACP,MAAM,CAACqF,KAAK,CAAC;8CAAE;gDAAApC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAC3D,CAAC,eACFrG,OAAA;gDAAOsG,SAAS,EAAC,kBAAkB;gDAACuC,OAAO,EAAC,aAAa;gDAAA5C,QAAA,EAAC;8CAAE;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAO,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACnE,CAAC;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACL,CACR,eACDrG,OAAA;4CAAKuH,KAAK,EAAE;8CAAEM,OAAO,EAAE,MAAM;8CAAED,KAAK,EAAE,MAAM;8CAAEkB,OAAO,EAAE;4CAAa,CAAE;4CAAA7C,QAAA,gBAClEjG,OAAA;8CAAAiG,QAAA,EAAO;4CAAa;8CAAAC,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAO,CAAC,eAC5BrG,OAAA;8CACIsG,SAAS,EAAC,cAAc;8CACxBgC,KAAK,EAAE,EAAAjI,gBAAA,GAAAc,QAAQ,CAACE,MAAM,cAAAhB,gBAAA,uBAAfA,gBAAA,CAAiBuB,QAAQ,KAAI,EAAG;8CACvC2G,QAAQ,EAAG/E,CAAC,IAAKpC,WAAW,CAACmB,IAAI,KAAK;gDAClC,GAAGA,IAAI;gDACPlB,MAAM,EAAE;kDAAEO,QAAQ,EAAEmH,QAAQ,CAACvF,CAAC,CAACP,MAAM,CAACqF,KAAK;gDAAE;8CACjD,CAAC,CAAC,CAAE;8CACJU,QAAQ,EAAE,KAAM,CAAC;8CAAA;8CACjBzB,KAAK,EAAE;gDAAEM,OAAO,EAAE,cAAc;gDAAED,KAAK,EAAE,MAAM;gDAAEJ,MAAM,EAAE;8CAAY,CAAE;8CAAAvB,QAAA,gBAEvEjG,OAAA;gDAAQsI,KAAK,EAAC,EAAE;gDAAArC,QAAA,EAAC;8CAAa;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAQ,CAAC,EACtC1E,UAAU,CACNsH,MAAM,CAAC,CAACC,MAAM,EAAE9B,KAAK,EAAE+B,IAAI,KACxB/B,KAAK,KAAK+B,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACzH,QAAQ,KAAKsH,MAAM,CAACtH,QAAQ,CAAC,CAAC,CACjE0H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,QAAQ,IAAI,CAAC,KAAKD,CAAC,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC,CACrDvC,GAAG,CAACgC,MAAM,iBACPlJ,OAAA;gDAA8BsI,KAAK,EAAEY,MAAM,CAACtH,QAAS;gDAAAqE,QAAA,EAChDiD,MAAM,CAACrH;8CAAU,GADTqH,MAAM,CAACtH,QAAQ;gDAAAsE,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAEpB,CACX,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAEF,CAAC;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACR,CAAC,EACLlF,QAAQ,CAACE,MAAM,CAACO,QAAQ,IAAI,CAAC,iBAC1B5B,OAAA;4CAAKsG,SAAS,EAAC,qBAAqB;4CAAAL,QAAA,gBAChCjG,OAAA;8CACIiE,IAAI,EAAC,MAAM;8CACXyE,EAAE,EAAC,QAAQ;8CACXpC,SAAS,EAAC,8BAA8B;8CACxCiC,QAAQ,EAAE3F,gBAAiB;8CAC3B8G,QAAQ;8CACRC,MAAM,EAAC;4CAAiB;8CAAAzD,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAC3B,CAAC,eACFrG,OAAA;8CAAO6I,OAAO,EAAC,QAAQ;8CAACvC,SAAS,EAAC,aAAa;8CAACiB,KAAK,EAAE;gDAAEM,OAAO,EAAE;8CAAS,CAAE;8CAAA5B,QAAA,eACzEjG,OAAA;gDAAK4J,KAAK,EAAC,4BAA4B;gDAAChC,KAAK,EAAC,IAAI;gDAACiC,MAAM,EAAC,IAAI;gDAACC,OAAO,EAAC,WAAW;gDAAA7D,QAAA,eAC9EjG,OAAA;kDAAM+J,CAAC,EAAC;gDAA4P;kDAAA7D,QAAA,EAAAC,YAAA;kDAAAC,UAAA;kDAAAC,YAAA;gDAAA,OAAE;8CAAC;gDAAAH,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OACtQ;4CAAC;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACH,CAAC,EACPxF,eAAe,CAACqD,MAAM,GAAG,CAAC,iBACvBlE,OAAA;8CAAKsG,SAAS,EAAC,MAAM;8CAAAL,QAAA,EAChBpF,eAAe,CAACqG,GAAG,CAAC,CAAC9D,IAAI,EAAEgE,KAAK,kBAC7BpH,OAAA;gDACIsG,SAAS,EAAC,kBAAkB;gDAAAL,QAAA,GAC3B7C,IAAI,CAACO,QAAQ,eACd3D,OAAA;kDAAIsG,SAAS,EAAC,WAAW;kDAAAL,QAAA,EAAC;gDAAC;kDAAAC,QAAA,EAAAC,YAAA;kDAAAC,UAAA;kDAAAC,YAAA;gDAAA,OAAI,CAAC;8CAAA,GAHzBe,KAAK;gDAAAlB,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAIV,CACT;4CAAC;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACD,CACR;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACA,CACR,EACAlF,QAAQ,CAACE,MAAM,CAACO,QAAQ,IAAI,CAAC,iBAC1B5B,OAAA;4CACIiE,IAAI,EAAC,QAAQ;4CACbqC,SAAS,EAAC,iBAAiB;4CAC3B6B,OAAO,EAAEA,CAAA,KAAM/D,mBAAmB,CAAC,CAAC,CAAE;4CAAA6B,QAAA,EACzC;0CAED;4CAAAC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAQ,CAAC;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAEZ,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACL;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACL;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACL;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BACP;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnG,EAAA,CAvZID,eAAe;EAAA,QACIV,SAAS;AAAA;AAAAyK,EAAA,GAD5B/J,eAAe;AAyZrB,eAAeA,eAAe;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}