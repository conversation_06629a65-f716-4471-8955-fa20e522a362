{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TicketDetails = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Ticket Details\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = TicketDetails;\nexport default TicketDetails;\nvar _c;\n$RefreshReg$(_c, \"TicketDetails\");", "map": {"version": 3, "names": ["TicketDetails", "_jsxDEV", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js"], "sourcesContent": ["const TicketDetails = () => {\n    return (\n        <div>\n            <h1>Ticket Details</h1>\n        </div>\n    );\n};\n\nexport default TicketDetails;"], "mappings": ";;AAAA,MAAMA,aAAa,GAAGA,CAAA,KAAM;EACxB,oBACIC,OAAA;IAAAC,QAAA,eACID,OAAA;MAAAC,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEd,CAAC;AAACC,EAAA,GANIP,aAAa;AAQnB,eAAeA,aAAa;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}