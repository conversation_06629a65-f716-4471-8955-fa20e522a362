[{"/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js": "1", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/reportWebVitals.js": "2", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/App.js": "3", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/layouts/MainLayout.js": "4", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FAQ/FAQ.js": "5", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Dashboard/Dashboard.js": "6", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/common/ErrorBoundary.js": "7", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/hooks/useAuth.js": "8", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.js": "9", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js": "10", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/CreateFeedback.js": "11", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackStats.js": "12", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js": "13", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/feedbackService.js": "14", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.service.js": "15", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/CommonHelper.js": "16", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js": "17", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyAssignedTickets.js": "18", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanTickets.js": "19", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanCreatedTicket.js": "20", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/AllTickets.js": "21", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js": "22", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js": "23", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js": "24", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js": "25"}, {"size": 911, "mtime": 1749557795086, "results": "26", "hashOfConfig": "27"}, {"size": 362, "mtime": 1748940968525, "results": "28", "hashOfConfig": "27"}, {"size": 4757, "mtime": 1749814305144, "results": "29", "hashOfConfig": "27"}, {"size": 2509, "mtime": 1749207917672, "results": "30", "hashOfConfig": "27"}, {"size": 3323, "mtime": 1748949300044, "results": "31", "hashOfConfig": "27"}, {"size": 2403, "mtime": 1748949769270, "results": "32", "hashOfConfig": "27"}, {"size": 2707, "mtime": 1748949071173, "results": "33", "hashOfConfig": "27"}, {"size": 76, "mtime": 1748949342828, "results": "34", "hashOfConfig": "27"}, {"size": 2373, "mtime": 1748945253809, "results": "35", "hashOfConfig": "27"}, {"size": 3585, "mtime": 1749729956557, "results": "36", "hashOfConfig": "27"}, {"size": 15011, "mtime": 1749637880599, "results": "37", "hashOfConfig": "27"}, {"size": 788, "mtime": 1749455834634, "results": "38", "hashOfConfig": "27"}, {"size": 4399, "mtime": 1749729998949, "results": "39", "hashOfConfig": "27"}, {"size": 3651, "mtime": 1749813667049, "results": "40", "hashOfConfig": "27"}, {"size": 1974, "mtime": 1749548381486, "results": "41", "hashOfConfig": "27"}, {"size": 2120, "mtime": 1749707662454, "results": "42", "hashOfConfig": "27"}, {"size": 29695, "mtime": 1749796739302, "results": "43", "hashOfConfig": "27"}, {"size": 16932, "mtime": 1749801152968, "results": "44", "hashOfConfig": "27"}, {"size": 18653, "mtime": 1749805681631, "results": "45", "hashOfConfig": "27"}, {"size": 20724, "mtime": 1749805687966, "results": "46", "hashOfConfig": "27"}, {"size": 18605, "mtime": 1749805728105, "results": "47", "hashOfConfig": "27"}, {"size": 38696, "mtime": 1749799335958, "results": "48", "hashOfConfig": "27"}, {"size": 2303, "mtime": 1749814333576, "results": "49", "hashOfConfig": "27"}, {"size": 4851, "mtime": 1749813927947, "results": "50", "hashOfConfig": "27"}, {"size": 895, "mtime": 1749814297692, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qogglo", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/reportWebVitals.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/App.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/layouts/MainLayout.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FAQ/FAQ.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Dashboard/Dashboard.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/common/ErrorBoundary.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/hooks/useAuth.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/CreateFeedback.js", ["127", "128"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackStats.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/feedbackService.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.service.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/CommonHelper.js", ["129", "130"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js", ["131", "132", "133"], ["134", "135", "136", "137", "138", "139", "140", "141"], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyAssignedTickets.js", ["142"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanTickets.js", ["143"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanCreatedTicket.js", ["144", "145", "146", "147", "148", "149", "150", "151"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/AllTickets.js", ["152"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js", ["153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], ["165", "166", "167", "168", "169"], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js", ["170"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js", [], [], {"ruleId": "171", "severity": 1, "message": "172", "line": 31, "column": 8, "nodeType": "173", "endLine": 31, "endColumn": 10, "suggestions": "174"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 122, "column": 27, "nodeType": "177", "messageId": "178", "endLine": 122, "endColumn": 29}, {"ruleId": "175", "severity": 1, "message": "179", "line": 10, "column": 32, "nodeType": "177", "messageId": "178", "endLine": 10, "endColumn": 34}, {"ruleId": "175", "severity": 1, "message": "179", "line": 11, "column": 35, "nodeType": "177", "messageId": "178", "endLine": 11, "endColumn": 37}, {"ruleId": "180", "severity": 1, "message": "181", "line": 30, "column": 40, "nodeType": "182", "messageId": "183", "endLine": 30, "endColumn": 42}, {"ruleId": "180", "severity": 1, "message": "181", "line": 30, "column": 55, "nodeType": "182", "messageId": "183", "endLine": 30, "endColumn": 57}, {"ruleId": "171", "severity": 1, "message": "184", "line": 81, "column": 8, "nodeType": "173", "endLine": 81, "endColumn": 18, "suggestions": "185"}, {"ruleId": "175", "severity": 1, "message": "179", "line": 44, "column": 43, "nodeType": "177", "messageId": "178", "endLine": 44, "endColumn": 45, "suppressions": "186"}, {"ruleId": "175", "severity": 1, "message": "179", "line": 255, "column": 100, "nodeType": "177", "messageId": "178", "endLine": 255, "endColumn": 102, "suppressions": "187"}, {"ruleId": "175", "severity": 1, "message": "179", "line": 255, "column": 126, "nodeType": "177", "messageId": "178", "endLine": 255, "endColumn": 128, "suppressions": "188"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 260, "column": 104, "nodeType": "191", "endLine": 260, "endColumn": 107, "suppressions": "192"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 268, "column": 101, "nodeType": "191", "endLine": 270, "endColumn": 268, "suppressions": "193"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 275, "column": 101, "nodeType": "191", "endLine": 278, "endColumn": 102, "suppressions": "194"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 358, "column": 119, "nodeType": "177", "messageId": "178", "endLine": 358, "endColumn": 121, "suppressions": "195"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 390, "column": 119, "nodeType": "177", "messageId": "178", "endLine": 390, "endColumn": 121, "suppressions": "196"}, {"ruleId": "171", "severity": 1, "message": "172", "line": 42, "column": 8, "nodeType": "173", "endLine": 42, "endColumn": 10, "suggestions": "197"}, {"ruleId": "171", "severity": 1, "message": "172", "line": 50, "column": 8, "nodeType": "173", "endLine": 50, "endColumn": 10, "suggestions": "198"}, {"ruleId": "171", "severity": 1, "message": "172", "line": 51, "column": 8, "nodeType": "173", "endLine": 51, "endColumn": 10, "suggestions": "199"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 157, "column": 46, "nodeType": "177", "messageId": "178", "endLine": 157, "endColumn": 48}, {"ruleId": "175", "severity": 1, "message": "179", "line": 181, "column": 44, "nodeType": "177", "messageId": "178", "endLine": 181, "endColumn": 46}, {"ruleId": "175", "severity": 1, "message": "179", "line": 189, "column": 41, "nodeType": "177", "messageId": "178", "endLine": 189, "endColumn": 43}, {"ruleId": "175", "severity": 1, "message": "179", "line": 196, "column": 59, "nodeType": "177", "messageId": "178", "endLine": 196, "endColumn": 61}, {"ruleId": "175", "severity": 1, "message": "176", "line": 200, "column": 30, "nodeType": "177", "messageId": "178", "endLine": 200, "endColumn": 32}, {"ruleId": "175", "severity": 1, "message": "176", "line": 200, "column": 62, "nodeType": "177", "messageId": "178", "endLine": 200, "endColumn": 64}, {"ruleId": "175", "severity": 1, "message": "179", "line": 202, "column": 62, "nodeType": "177", "messageId": "178", "endLine": 202, "endColumn": 64}, {"ruleId": "171", "severity": 1, "message": "172", "line": 50, "column": 8, "nodeType": "173", "endLine": 50, "endColumn": 10, "suggestions": "200"}, {"ruleId": "171", "severity": 1, "message": "184", "line": 37, "column": 8, "nodeType": "173", "endLine": 37, "endColumn": 18, "suggestions": "201"}, {"ruleId": "175", "severity": 1, "message": "179", "line": 82, "column": 36, "nodeType": "177", "messageId": "178", "endLine": 82, "endColumn": 38}, {"ruleId": "175", "severity": 1, "message": "179", "line": 91, "column": 35, "nodeType": "177", "messageId": "178", "endLine": 91, "endColumn": 37}, {"ruleId": "175", "severity": 1, "message": "179", "line": 91, "column": 57, "nodeType": "177", "messageId": "178", "endLine": 91, "endColumn": 59}, {"ruleId": "175", "severity": 1, "message": "179", "line": 160, "column": 19, "nodeType": "177", "messageId": "178", "endLine": 160, "endColumn": 21}, {"ruleId": "175", "severity": 1, "message": "179", "line": 182, "column": 25, "nodeType": "177", "messageId": "178", "endLine": 182, "endColumn": 27}, {"ruleId": "175", "severity": 1, "message": "179", "line": 197, "column": 25, "nodeType": "177", "messageId": "178", "endLine": 197, "endColumn": 27}, {"ruleId": "175", "severity": 1, "message": "176", "line": 199, "column": 47, "nodeType": "177", "messageId": "178", "endLine": 199, "endColumn": 49}, {"ruleId": "175", "severity": 1, "message": "176", "line": 199, "column": 79, "nodeType": "177", "messageId": "178", "endLine": 199, "endColumn": 81}, {"ruleId": "175", "severity": 1, "message": "179", "line": 218, "column": 24, "nodeType": "177", "messageId": "178", "endLine": 218, "endColumn": 26}, {"ruleId": "175", "severity": 1, "message": "179", "line": 251, "column": 22, "nodeType": "177", "messageId": "178", "endLine": 251, "endColumn": 24}, {"ruleId": "175", "severity": 1, "message": "179", "line": 254, "column": 42, "nodeType": "177", "messageId": "178", "endLine": 254, "endColumn": 44}, {"ruleId": "189", "severity": 1, "message": "190", "line": 441, "column": 66, "nodeType": "191", "endLine": 441, "endColumn": 164, "suppressions": "202"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 442, "column": 66, "nodeType": "191", "endLine": 442, "endColumn": 164, "suppressions": "203"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 477, "column": 118, "nodeType": "191", "endLine": 477, "endColumn": 121, "suppressions": "204"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 484, "column": 101, "nodeType": "191", "endLine": 486, "endColumn": 256, "suppressions": "205"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 491, "column": 101, "nodeType": "191", "endLine": 494, "endColumn": 102, "suppressions": "206"}, {"ruleId": "171", "severity": 1, "message": "207", "line": 63, "column": 8, "nodeType": "173", "endLine": 63, "endColumn": 35, "suggestions": "208"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", "ArrayExpression", ["209"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", ["210"], ["211"], ["212"], ["213"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["214"], ["215"], ["216"], ["217"], ["218"], ["219"], ["220"], ["221"], ["222"], ["223"], ["224"], ["225"], ["226"], ["227"], ["228"], "React Hook useEffect has a missing dependency: 'handleUserDetails'. Either include it or remove the dependency array.", ["229"], {"desc": "230", "fix": "231"}, {"desc": "232", "fix": "233"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"desc": "230", "fix": "236"}, {"desc": "230", "fix": "237"}, {"desc": "230", "fix": "238"}, {"desc": "230", "fix": "239"}, {"desc": "232", "fix": "240"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"kind": "234", "justification": "235"}, {"desc": "241", "fix": "242"}, "Update the dependencies array to be: [GetAllProcess]", {"range": "243", "text": "244"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "245", "text": "246"}, "directive", "", {"range": "247", "text": "244"}, {"range": "248", "text": "244"}, {"range": "249", "text": "244"}, {"range": "250", "text": "244"}, {"range": "251", "text": "246"}, "Update the dependencies array to be: [type, ticketId, navigate, handleUserDetails]", {"range": "252", "text": "253"}, [1271, 1273], "[GetAllProcess]", [2839, 2849], "[getTicketDetailsService, ticketId]", [1540, 1542], [1833, 1835], [1883, 1885], [1830, 1832], [1751, 1761], [1881, 1908], "[type, ticketId, navigate, handleUserDetails]"]