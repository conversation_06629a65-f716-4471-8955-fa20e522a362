[{"/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js": "1", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/App.js": "2", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FAQ/FAQ.js": "3", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js": "4", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/CreateFeedback.js": "5", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackStats.js": "6", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js": "7", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/feedbackService.js": "8", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.service.js": "9", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/CommonHelper.js": "10", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js": "11", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyAssignedTickets.js": "12", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanTickets.js": "13", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanCreatedTicket.js": "14", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/AllTickets.js": "15", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js": "16", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js": "17", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js": "18", "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js": "19"}, {"size": 630, "mtime": 1750059448087, "results": "20", "hashOfConfig": "21"}, {"size": 4757, "mtime": 1749816025168, "results": "22", "hashOfConfig": "21"}, {"size": 3323, "mtime": 1748949300044, "results": "23", "hashOfConfig": "21"}, {"size": 3585, "mtime": 1749729956557, "results": "24", "hashOfConfig": "21"}, {"size": 15011, "mtime": 1749637880599, "results": "25", "hashOfConfig": "21"}, {"size": 788, "mtime": 1749455834634, "results": "26", "hashOfConfig": "21"}, {"size": 4399, "mtime": 1749729998949, "results": "27", "hashOfConfig": "21"}, {"size": 3651, "mtime": 1749813667049, "results": "28", "hashOfConfig": "21"}, {"size": 1974, "mtime": 1749816025168, "results": "29", "hashOfConfig": "21"}, {"size": 2120, "mtime": 1749707662454, "results": "30", "hashOfConfig": "21"}, {"size": 29695, "mtime": 1749796739302, "results": "31", "hashOfConfig": "21"}, {"size": 16932, "mtime": 1749801152968, "results": "32", "hashOfConfig": "21"}, {"size": 18653, "mtime": 1749805681631, "results": "33", "hashOfConfig": "21"}, {"size": 20724, "mtime": 1749805687966, "results": "34", "hashOfConfig": "21"}, {"size": 18605, "mtime": 1749805728105, "results": "35", "hashOfConfig": "21"}, {"size": 38696, "mtime": 1749799335958, "results": "36", "hashOfConfig": "21"}, {"size": 2303, "mtime": 1749814333576, "results": "37", "hashOfConfig": "21"}, {"size": 4851, "mtime": 1749813927947, "results": "38", "hashOfConfig": "21"}, {"size": 330, "mtime": 1750059410857, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qogglo", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Documents/Project/feedbackProject/frontend/src/index.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/App.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FAQ/FAQ.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyFeedback.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/CreateFeedback.js", ["97", "98"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackStats.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/FeedbackTable.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/feedbackService.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/api.service.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/services/CommonHelper.js", ["99", "100"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyTicketDetails.js", ["101", "102", "103"], ["104", "105", "106", "107", "108", "109", "110", "111"], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MyAssignedTickets.js", ["112"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanTickets.js", ["113"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/MySpanCreatedTicket.js", ["114", "115", "116", "117", "118", "119", "120", "121"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/AllTickets.js", ["122"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/TicketDetails.js", ["123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134"], ["135", "136", "137", "138", "139"], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/LandingPage.js", ["140"], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/EditProfile.js", [], [], "/home/<USER>/Documents/Project/feedbackProject/frontend/src/components/Login.js", [], [], {"ruleId": "141", "severity": 1, "message": "142", "line": 31, "column": 8, "nodeType": "143", "endLine": 31, "endColumn": 10, "suggestions": "144"}, {"ruleId": "145", "severity": 1, "message": "146", "line": 122, "column": 27, "nodeType": "147", "messageId": "148", "endLine": 122, "endColumn": 29}, {"ruleId": "145", "severity": 1, "message": "149", "line": 10, "column": 32, "nodeType": "147", "messageId": "148", "endLine": 10, "endColumn": 34}, {"ruleId": "145", "severity": 1, "message": "149", "line": 11, "column": 35, "nodeType": "147", "messageId": "148", "endLine": 11, "endColumn": 37}, {"ruleId": "150", "severity": 1, "message": "151", "line": 30, "column": 40, "nodeType": "152", "messageId": "153", "endLine": 30, "endColumn": 42}, {"ruleId": "150", "severity": 1, "message": "151", "line": 30, "column": 55, "nodeType": "152", "messageId": "153", "endLine": 30, "endColumn": 57}, {"ruleId": "141", "severity": 1, "message": "154", "line": 81, "column": 8, "nodeType": "143", "endLine": 81, "endColumn": 18, "suggestions": "155"}, {"ruleId": "145", "severity": 1, "message": "149", "line": 44, "column": 43, "nodeType": "147", "messageId": "148", "endLine": 44, "endColumn": 45, "suppressions": "156"}, {"ruleId": "145", "severity": 1, "message": "149", "line": 255, "column": 100, "nodeType": "147", "messageId": "148", "endLine": 255, "endColumn": 102, "suppressions": "157"}, {"ruleId": "145", "severity": 1, "message": "149", "line": 255, "column": 126, "nodeType": "147", "messageId": "148", "endLine": 255, "endColumn": 128, "suppressions": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 260, "column": 104, "nodeType": "161", "endLine": 260, "endColumn": 107, "suppressions": "162"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 268, "column": 101, "nodeType": "161", "endLine": 270, "endColumn": 268, "suppressions": "163"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 275, "column": 101, "nodeType": "161", "endLine": 278, "endColumn": 102, "suppressions": "164"}, {"ruleId": "145", "severity": 1, "message": "146", "line": 358, "column": 119, "nodeType": "147", "messageId": "148", "endLine": 358, "endColumn": 121, "suppressions": "165"}, {"ruleId": "145", "severity": 1, "message": "146", "line": 390, "column": 119, "nodeType": "147", "messageId": "148", "endLine": 390, "endColumn": 121, "suppressions": "166"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 42, "column": 8, "nodeType": "143", "endLine": 42, "endColumn": 10, "suggestions": "167"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 50, "column": 8, "nodeType": "143", "endLine": 50, "endColumn": 10, "suggestions": "168"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 51, "column": 8, "nodeType": "143", "endLine": 51, "endColumn": 10, "suggestions": "169"}, {"ruleId": "145", "severity": 1, "message": "146", "line": 157, "column": 46, "nodeType": "147", "messageId": "148", "endLine": 157, "endColumn": 48}, {"ruleId": "145", "severity": 1, "message": "149", "line": 181, "column": 44, "nodeType": "147", "messageId": "148", "endLine": 181, "endColumn": 46}, {"ruleId": "145", "severity": 1, "message": "149", "line": 189, "column": 41, "nodeType": "147", "messageId": "148", "endLine": 189, "endColumn": 43}, {"ruleId": "145", "severity": 1, "message": "149", "line": 196, "column": 59, "nodeType": "147", "messageId": "148", "endLine": 196, "endColumn": 61}, {"ruleId": "145", "severity": 1, "message": "146", "line": 200, "column": 30, "nodeType": "147", "messageId": "148", "endLine": 200, "endColumn": 32}, {"ruleId": "145", "severity": 1, "message": "146", "line": 200, "column": 62, "nodeType": "147", "messageId": "148", "endLine": 200, "endColumn": 64}, {"ruleId": "145", "severity": 1, "message": "149", "line": 202, "column": 62, "nodeType": "147", "messageId": "148", "endLine": 202, "endColumn": 64}, {"ruleId": "141", "severity": 1, "message": "142", "line": 50, "column": 8, "nodeType": "143", "endLine": 50, "endColumn": 10, "suggestions": "170"}, {"ruleId": "141", "severity": 1, "message": "154", "line": 37, "column": 8, "nodeType": "143", "endLine": 37, "endColumn": 18, "suggestions": "171"}, {"ruleId": "145", "severity": 1, "message": "149", "line": 82, "column": 36, "nodeType": "147", "messageId": "148", "endLine": 82, "endColumn": 38}, {"ruleId": "145", "severity": 1, "message": "149", "line": 91, "column": 35, "nodeType": "147", "messageId": "148", "endLine": 91, "endColumn": 37}, {"ruleId": "145", "severity": 1, "message": "149", "line": 91, "column": 57, "nodeType": "147", "messageId": "148", "endLine": 91, "endColumn": 59}, {"ruleId": "145", "severity": 1, "message": "149", "line": 160, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 160, "endColumn": 21}, {"ruleId": "145", "severity": 1, "message": "149", "line": 182, "column": 25, "nodeType": "147", "messageId": "148", "endLine": 182, "endColumn": 27}, {"ruleId": "145", "severity": 1, "message": "149", "line": 197, "column": 25, "nodeType": "147", "messageId": "148", "endLine": 197, "endColumn": 27}, {"ruleId": "145", "severity": 1, "message": "146", "line": 199, "column": 47, "nodeType": "147", "messageId": "148", "endLine": 199, "endColumn": 49}, {"ruleId": "145", "severity": 1, "message": "146", "line": 199, "column": 79, "nodeType": "147", "messageId": "148", "endLine": 199, "endColumn": 81}, {"ruleId": "145", "severity": 1, "message": "149", "line": 218, "column": 24, "nodeType": "147", "messageId": "148", "endLine": 218, "endColumn": 26}, {"ruleId": "145", "severity": 1, "message": "149", "line": 251, "column": 22, "nodeType": "147", "messageId": "148", "endLine": 251, "endColumn": 24}, {"ruleId": "145", "severity": 1, "message": "149", "line": 254, "column": 42, "nodeType": "147", "messageId": "148", "endLine": 254, "endColumn": 44}, {"ruleId": "159", "severity": 1, "message": "160", "line": 441, "column": 66, "nodeType": "161", "endLine": 441, "endColumn": 164, "suppressions": "172"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 442, "column": 66, "nodeType": "161", "endLine": 442, "endColumn": 164, "suppressions": "173"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 477, "column": 118, "nodeType": "161", "endLine": 477, "endColumn": 121, "suppressions": "174"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 484, "column": 101, "nodeType": "161", "endLine": 486, "endColumn": 256, "suppressions": "175"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 491, "column": 101, "nodeType": "161", "endLine": 494, "endColumn": 102, "suppressions": "176"}, {"ruleId": "141", "severity": 1, "message": "177", "line": 63, "column": 8, "nodeType": "143", "endLine": 63, "endColumn": 35, "suggestions": "178"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", "ArrayExpression", ["179"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", ["180"], ["181"], ["182"], ["183"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["184"], ["185"], ["186"], ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], ["194"], ["195"], ["196"], ["197"], ["198"], "React Hook useEffect has a missing dependency: 'handleUserDetails'. Either include it or remove the dependency array.", ["199"], {"desc": "200", "fix": "201"}, {"desc": "202", "fix": "203"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"desc": "200", "fix": "206"}, {"desc": "200", "fix": "207"}, {"desc": "200", "fix": "208"}, {"desc": "200", "fix": "209"}, {"desc": "202", "fix": "210"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"desc": "211", "fix": "212"}, "Update the dependencies array to be: [GetAllProcess]", {"range": "213", "text": "214"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "215", "text": "216"}, "directive", "", {"range": "217", "text": "214"}, {"range": "218", "text": "214"}, {"range": "219", "text": "214"}, {"range": "220", "text": "214"}, {"range": "221", "text": "216"}, "Update the dependencies array to be: [type, ticketId, navigate, handleUserDetails]", {"range": "222", "text": "223"}, [1271, 1273], "[GetAllProcess]", [2839, 2849], "[getTicketDetailsService, ticketId]", [1540, 1542], [1833, 1835], [1883, 1885], [1830, 1832], [1751, 1761], [1881, 1908], "[type, ticketId, navigate, handleUserDetails]"]