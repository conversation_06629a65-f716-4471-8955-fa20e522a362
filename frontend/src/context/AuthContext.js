import React, { createContext, useState, useEffect } from 'react';
import api from '../services/api';

export const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(localStorage.getItem('token'));

  useEffect(() => {
    if (token) {
      fetchUserDetails();
    } else {
      setLoading(false);
    }
  }, [token]);

  const fetchUserDetails = async () => {
    try {
      const response = await api.get(`/api/GetUserDetails/${localStorage.getItem('employeeId')}`);
      setUser(response.data.EMPData[0]);
    } catch (error) {
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      const response = await api.post('/api/login', credentials);
      const { token, employeeId } = response.data;
      
      localStorage.setItem('token', token);
      localStorage.setItem('employeeId', employeeId);
      
      setToken(token);
      await fetchUserDetails();
      
      return true;
    } catch (error) {
      throw new Error('Login failed');
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('employeeId');
    setToken(null);
    setUser(null);
  };

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!token
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthProvider; 