import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import MyFeedback from './components/MyFeedback';
import CreateFeedback from './components/CreateFeedback';
import 'bootstrap/dist/css/bootstrap.min.css';
import '@fortawesome/fontawesome-free/css/all.min.css';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import MyTicketDetails from './components/MyTicketDetails';
import MyAssignedTickets from './components/MyAssignedTickets';
import MySpanTickets from './components/MySpanTickets';
import MySpanCreatedTicket from './components/MySpanCreatedTicket';
import AllTickets from './components/AllTickets';
import TicketDetails from './components/TicketDetails';
import LandingPage from './components/LandingPage';
import EditProfile from './components/EditProfile';
import LoginPage from './components/Login';

function App() {
	const [activeTab, setActiveTab] = useState(1);

	return (
		<Router>
			<div className="app">
				<div className="sidebar-scroll">
					<div className="user-account">
						<div className="dropdown">
							<span>Welcome,</span>
							<strong className="agent_name">shikhaagrawal</strong>
						</div>
						<hr />
					</div>

					<ul className="nav nav-tabs">
						<li className="nav-item">
							<Link
								className={`nav-link ${activeTab === 1 ? 'active show' : ''}`}
								to="/myFeedBack"
								onClick={() => setActiveTab(1)}
							>
								My FeedBack
							</Link>
						</li>
						{/* RoleId check remaining */}
						<li className="nav-item">
							<Link
								className={`nav-link ${activeTab === 2 ? 'active show' : ''}`}
								to="/AssignedFeedBack"
								onClick={() => setActiveTab(2)}
							>
								My Account
							</Link>
						</li>
					</ul>

					<div className="tab-content p-l-0 p-r-0">
						<div className={`tab-pane animated fadeIn ${activeTab === 1 ? 'show active' : ''}`}>
							<nav className="sidebar-nav">
								<ul className="main-menu metismenu">
									<li>
										<Link to="/createFeedBack">
											<i className="fa fa-plus-circle"></i>
											<span>Create FeedBack</span>
										</Link>
									</li>
									<li>
										<Link to="/myFeedBack">
											<i className="fa fa-ticket"></i>
											<span>My FeedBack</span>
										</Link>
									</li>
								</ul>
							</nav>
						</div>
						{/* RoleId check remaining */}
						<div className={`tab-pane animated fadeIn ${activeTab === 2 ? 'show active' : ''}`}>
							<nav className="sidebar-nav">
								<ul className="main-menu metismenu">
									<li>
										<Link to="/AssignedFeedBack">
											<i className="fa fa-check"></i>
											<span>Assigned FeedBack</span>
										</Link>
									</li>
									<li>
										<Link to="/MySpanFeedBack">
											<i className="fa-solid fa-envelope"></i>
											<span>My Process Feedback</span>
										</Link>
									</li>
									<li>
										<Link to="/MySpanCreatedFeedback">
											<i className="fa-solid fa-envelope"></i>
											<span>My Span Feedback</span>
										</Link>
									</li>
									<li>
										<Link to="/AllFeedBack">
											<i className="fa fa-ticket"></i>
											<span>All FeedBack</span>
										</Link>
									</li>
								</ul>
							</nav>
						</div>
					</div>
				</div>
				

				<div className="main-content">
					<Routes>
						<Route path="/" element={<Navigate to="/LandingPage" replace />} />
						<Route path="/LandingPage" element={<LandingPage />} />
						<Route path="/LandingPage/:type" element={<LandingPage />} />
						<Route path="/LandingPage/:type/:ticketId" element={<LandingPage />} />
						<Route path="/editprofile" element={<EditProfile />} />
						<Route path="/login" element={<LoginPage />} />
						<Route path="/myFeedBack" element={<MyFeedback />} />
						<Route path="/createFeedBack" element={<CreateFeedback />} />
						<Route path="/MyFeedbackDetails/:ticketId" element={<MyTicketDetails />} />
						<Route path="/MyTicketDetails/:ticketId" element={<MyTicketDetails />} />
						<Route path="/AssignedFeedBack" element={<MyAssignedTickets />} />
						<Route path="/MySpanFeedBack" element={<MySpanTickets />} />
						<Route path="/MySpanCreatedFeedback" element={<MySpanCreatedTicket />} />
						<Route path="/AllFeedBack" element={<AllTickets />} />
						<Route path="/TicketDetails/:ticketId" element={<TicketDetails />} />
						<Route path="/LandingPage/:ticketId/:type" element={<LandingPage />} />
					</Routes>
				</div>
			</div>
			<ToastContainer position="top-right" autoClose={3000} />
		</Router>
	);
}

export default App; 