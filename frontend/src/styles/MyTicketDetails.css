.ticketdetails {
    .detail_links {
        width: 100%;
    }

    .detail_links>p {
        padding: 0;
        float: right;
        clear: both;
        margin: 0;
        vertical-align: top;
        display: inline-block;
    }

    .assign_hd {
        font-size: 16px;
        color: #40a8fe;
        padding: 0 13px 0 0;
    }

    .btn {
        font-size: 14px;
    }

    .btn-outline-primary {
        color: #007bff;
        background-color: transparent;
        background-image: none;
        border: 1px solid #007bff;
    }

    .card .header h2 {
        font-size: 16px;
        color: #444;
        position: relative;
    }

    .mail-inbox {
        display: flex
    }

    .mail-inbox .mail-right {
        width: calc(100% - 280px);
        position: relative;
    }

    .mail-inbox .mail-right.agent_tkt_view {
        width: 100%;
    }

    .card .body {
        color: #444;
        padding: 20px;
        font-weight: 400;
        position: relative;
    }

    .card .body.ticket_detailbox {
        padding-left: 10px;
        padding-right: 10px;
    }

    .table_databox {
        padding: 10px 0 0 0;
    }

    .tab-content>.active {
        display: block;
    }

    .table {
        border: 1px solid #ddd !important;
    }

    .table-responsive {
        position: relative;
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    .active_detaillist {
        background: #ddd;
    }

    .detail_data {
        padding-top: 0 !important;
        padding-right: 0 !important;
    }

    .card.detialbox {
        box-shadow: none !important;
    }

    .emailer_body {
        padding-left: 10px !important;
    }

    .card .body.emailer_body {
        padding-right: 0;
    }

    .timeline-item {
        padding: 3em 2em 2em;
        position: relative;
        border-left: 1px solid;
    }

    .timeline-item.green {
        border-color: #86c541;
    }

    .timeline-item .date {
        margin-top: -30px;
        display: inline-block;
    }

    .timeline-item.green .date {
        color: #86c541;
    }

    .right_section {
        display: inline-block;
        float: right;
    }

    .mail_compose_Section {
        width: 100%;
        display: block;
    }

    .shadow_none {
        box-shadow: none;
        margin: 0;
    }

    .compose_box {
        padding-left: 0 !important;
        padding-right: 10px !important;
        width: auto;
        box-sizing: border-box;
        padding-bottom: 0 !important;
    }

    textarea {
        overflow: auto;
        resize: vertical;
    }

    .compose_action_button {
        margin-top: 10px;
        width: 100%;
        display: block;
        float: left;
    }

    .upload_box {
        display: inline-block;
        padding: 0;
        margin: 3px 0 0 0;
        text-align: left;
        left: 0;
    }

    .inputfile {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
    }

    .upload_docs {
        background: #007bff !important;
        color: #fff;
        border: none !important;
        border-radius: 4px !important;
        top: -1px !important;
        padding: 6px 4px 6px 9px !important;
    }

    .inputfile+label {
        max-width: 80%;
        font-size: 1.25rem;
        font-weight: 700;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        display: inline-block;
        overflow: hidden;
        padding: 0.625rem 1.25rem;
    }

    .timeline-item.green:after {
        border-color: #86c541;
    }

    .timeline-item:after {
        width: 10px;
        height: 15px;
        top: 1em;
        position: absolute;
        left: -6px;
        border-radius: 10px;
        content: '';
        background: #fff;
        border: 2px solid;
    }

    .mail-inbox {
        display: flex;

        .mail-left {
            width: 280px;
            padding: 15px;
            border-right: 1px solid #eaeaea;
            display: block;

            .mail-side {
                .nav {
                    flex-direction: column;

                    li {
                        padding: 0 10px;
                        margin-bottom: 5px;

                        label {
                            width: 40%;
                            font-size: 12px;
                            font-weight: normal;
                        }

                        a {
                            color: #666666;
                            display: flex;
                            align-items: center;

                            i {
                                font-size: 17px;
                                width: 35px;
                                -webkit-transition: font-size 0.2s;
                                -moz-transition: font-size 0.2s;
                                transition: font-size 0.2s;

                            }

                            .badge {
                                margin-left: auto;
                                margin-right: 0;
                            }
                        }

                        &:hover {
                            background-color: #f4f7f6;

                            a {
                                i {
                                    font-size: 20px;
                                }
                            }
                        }

                        &.active {
                            background-color: #f4f7f6;

                            a {
                                color: #17191c;
                            }
                        }
                    }
                }

                h3 {
                    font-size: 15px;
                    font-weight: 500;
                    margin-bottom: 15px;
                    margin-top: 30px;
                    line-height: 20px;
                }
            }
        }

        .mail-right {
            width: calc(100% - 280px);
            position: relative;

            .header {
                padding: 15px;
            }

            h2 {
                line-height: 35px;
            }

            .mail-action {
                padding: 15px;

                .fancy-checkbox {
                    label {
                        margin-bottom: 0;
                    }

                    input[type="checkbox"] {
                        +span {
                            &:before {
                                bottom: 0;
                            }
                        }
                    }
                }

                .pagination-email {
                    p {
                        line-height: 30px;
                        margin-bottom: 0;
                    }
                }
            }

            .mail-list {
                padding: 15px 0;

                ul {
                    li {
                        padding: 17px 15px 15px;
                        border-top: 1px solid #eaeaea;
                        width: 100%;
                        position: relative;

                        &:last-child {
                            border-bottom: 1px solid #eaeaea;
                        }

                        .hover-action {
                            position: absolute;
                            opacity: 0;
                            top: 0;
                            right: 0;
                            padding: 23px 16px 23px;
                            background: #ffffff;
                            transition: all 0.5s ease-in-out;
                        }

                        &:hover {
                            .hover-action {
                                opacity: 1;
                            }

                            .mail-detail-expand {
                                color: #007bff;
                            }
                        }

                        &.unread {
                            background-color: #f1f1f1;

                            .hover-action {
                                background-color: #f1f1f1;
                            }
                        }

                        .mail-detail-left,
                        .mail-detail-right {
                            float: left;
                        }

                        .mail-detail-left {
                            max-width: 60px;
                            min-width: 60px;
                            width: 60px;
                            position: relative;

                            .mail-star {
                                position: absolute;
                                right: 13px;
                                top: 0;
                                color: #6c757d;

                                &.active {
                                    color: #ffc107;
                                }
                            }
                        }

                        .mail-detail-right {
                            position: relative;
                            padding-right: 70px;
                            width: calc(100% - 70px);

                            span.time {
                                position: absolute;
                                top: 0;
                                right: 0;
                            }

                            h6,
                            p {
                                width: 100%;
                                display: block;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }

                            h6 {

                                a {
                                    color: #5a5a5a;
                                }
                            }

                            p {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }

            .mail-detail-full {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                background: #ffffff;

                .mail-action {
                    padding: 15px;
                }

                .detail-header {
                    padding: 20px 15px 0;
                    border-top: 1px solid #eaeaea;
                    border-bottom: 1px solid #eaeaea;

                    .float-left {
                        img {
                            max-width: 63px;
                            border-radius: 5px;
                        }
                    }
                }

                .media-body {
                    p {
                        a {
                            color: #6c757d;
                        }
                    }
                }

                .mail-cnt {
                    padding: 20px 15px;
                }
            }
        }
    }

    .mobile-left {
        display: none;
    }
    

    @media screen and (max-width: 991px) {
        .mail-inbox {
            .mail-left {
                width: 230px;
            }

            .mail-right {
                width: calc(100% - 230px);

                .mail-action {
                    .btn {
                        padding-left: 10px;
                        padding-right: 10px;
                    }

                    .pagination-email {
                        p {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    

    @media screen and (max-width: 767px) {
        .mobile-left {
            display: block;
            position: fixed;
            z-index: 9999;
            right: 10px;
            bottom: 10px;
        }

        .mail-inbox .mail-left {
            position: absolute;
            left: 0;
            background-color: #ffffff;
            z-index: 99;
        }

        .mail-inbox {
            .mail-left.collapse {
                &:not(.show) {
                    display: none;

                    + {
                        .mail-right {
                            width: 100%;
                        }
                    }
                }
            }

            .mail-right {
                width: 100%;

                .header {
                    flex-direction: column;

                    .ml-auto {
                        margin-left: 0 !important;
                        margin-top: 15px;
                    }
                }

                .mail-list ul li .mail-detail-right h6 {
                    .badge {
                        display: none;
                    }
                }
            }
        }

        .mail-detail-full {

            a.mail-back {
                top: -60px;
            }
        }

        .media-body {
            p {

                span,
                small {
                    display: none;
                }
            }
        }

    }
}