import { CALL_API } from "./api.service";

export const GetSalesTicketCount = (requestData) => {
	const input = {
        url: `api/SalesTicket/GetSalesTicketCount/${requestData.type}`,
        method: 'GET'
    }

	return CALL_API(input);
}											

export const GetSalesTicketByAgentId = (reqData) => {
	const input = {
        url: `api/SalesTicket/GetSalesTicketByAgentId/${reqData.type}?status=${reqData.status}`,
        method: 'GET',
    }

	return CALL_API(input);
}

export const GetTicketDetails = ( requestData) => {
    const input = {
        url: `api/SalesTicket/GetSalesTicketDetailsByID/${requestData.ticketId}`,
        method: 'GET',
    }

	return CALL_API(input);
}

export const UploadFile = (requestData) => {
    const input = {
        url: `api/SalesTicket/UploadFile`,
        method: 'POST',
        requestData
    }

	return CALL_API(input);
}

export const UpdateTicketRemarks = (requestData) => {
    const input = {
        url: `api/SalesTicket/UpdateSalesTicketByID`,
        method: 'POST',
        requestData
    }

	return CALL_API(input);
}

export const GetDocumentUrl = (requestData) => {
    const input = {
        url: `api/SalesTicket/GetDocumentUrl`,
        method: 'POST',
        requestData
    }

	return CALL_API(input);
}

export const GetProcessMasterByAPI = () => {
    const input = {
        url: `api/SalesTicket/GetProcessMaster`,
        method: 'GET'
    }

	return CALL_API(input);
}

export const GetAllIssueSubIssue = () => {
    const input = {
        url: `api/SalesTicket/GetAllIssueSubIssue`,
        method: 'GET'
    }

	return CALL_API(input);
}

export const CreateNewTicket = (requestData) => {
    const input = {
        url: `api/SalesTicket/CreateTicket`,
        method: 'POST',
        requestData
    }

	return CALL_API(input);
}

export const GetAdminTicketList = (requestData) => {
    const input = {
        url: 'api/SalesTicket/SalesTicketDashboardSearch',
        method: 'POST',
        requestData
    }
    return CALL_API(input);
};

export const getStatusMaster = () => {
    const input = {
        url: 'api/SalesTicket/GetSalesTicketStatusMaster',
        method: 'GET'
    }
    return CALL_API(input);
};

export const GetSpanCreatedTickets = (requestData) => {
    const input = {
        url: `api/SalesTicket/GetSpanCreatedTickets/${requestData.type}`,
        method: 'GET'
    }

	return CALL_API(input);
}

export const GetSalesTicketProcessUser = (requestData) => {
    const input = {
        url: `api/SalesTicket/GetSalesTicketProcessUser/${requestData.ticketId}/${requestData.ProcessId}`,
        method: 'GET'    
    }

	return CALL_API(input);
}

export const AssignSalesTicket = (requestData) => {
    const input = {
        url: `api/SalesTicket/AssignSalesTicket`,
        method: 'POST',
        requestData
    }

    return CALL_API(input);
}

export const ReAssignSalesTicket = (requestData) => {
    const input = {
        url: `api/SalesTicket/ReAssignSalesTicket`,
        method: 'POST',
        requestData
    }

	return CALL_API(input);
}

export const GetSalesTicketLog = (requestData) => {
    const input = {
        url: `api/SalesTicket/GetSalesTicketLog/${requestData.ticketId}/${requestData.logtype}`,
        method: 'GET'
    }

	return CALL_API(input);
}

export const GetSalesTicketUserDetails = () => {
    const input = {
        url: `api/SalesTicket/GetSalesTicketUserDetails`,
        method: 'GET'
    }

	return CALL_API(input);
}

export const updateUserProfile = () => {
    const input = {
        url: `api/SalesTicket/UpdateUserProfile`,
        method: 'POST'
    }

	return CALL_API(input);
}