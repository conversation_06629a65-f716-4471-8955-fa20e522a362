import axios from 'axios';

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to handle auth
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Feedback API endpoints
export const feedbackApi = {
  // Process related
  getProcesses: () => api.get('/GetProcessMaster'),
  getIssues: () => api.get('/GetAllIssueSubIssue'),
  getProducts: (processId) => api.get(`/GetProducts/${processId}`),

  // Ticket related
  createTicket: (data) => api.post('/CreateTicket', data),
  getTicketById: (ticketId) => api.post('/GetSalesTicketDetailsByID', { ticketId }),
  getTicketsByStatus: (status) => api.post('/GetSalesTicketByAgentId', {
    userId: localStorage.getItem('userId'),
    status,
    type: 1
  }),
  getTicketCounts: () => api.post('/GetSalesTicketCount', {
    userId: localStorage.getItem('userId'),
    status: 0,
    type: 1
  }),
  updateTicket: (data) => api.post('/UpdateSalesTicketByID', data),
  reassignTicket: (data) => api.post('/ReAssignSalesTicket', data),

  // Comments and logs
  getTicketLogs: (ticketId) => api.post('/GetSalesTicketLog', {
    ticketId,
    logtype: 1,
    userId: localStorage.getItem('userId')
  }),

  // Status
  getStatusMaster: () => api.get('/GetSalesTicketStatusMaster'),

  // Users
  getProcessUsers: (ticketId, processId) => api.post('/GetSalesTicketProcessUser', {
    ticketId,
    processId
  }),

  // File upload
  uploadFiles: (files) => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    return api.post('/UploadFile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

export default api; 