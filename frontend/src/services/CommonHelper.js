import { format, parseISO } from "date-fns";

export const getCookie = (name) => {
    if (document) {
        name = name || 'token';
        let nameEQ = name + "=";
        let ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
}

export const getUserId = () => {
    let MatrixToken = getCookie('MatrixToken');
    if (MatrixToken) {
        try {
            MatrixToken = JSON.parse(atob(MatrixToken));
            return MatrixToken.UserId;
        } catch (e) {
            return getCookie('AgentId');
        }
    }
    else {
        return getCookie('AgentId');
    }
}

export const getAsteriskToken = () => {
    let MatrixToken = getCookie('MatrixToken');
    if (MatrixToken) {
        try {
            MatrixToken = JSON.parse(atob(MatrixToken));
            return MatrixToken.AsteriskToken;
        } catch (e) {
            return getCookie('AsteriskToken');
        }
    }
    else {
        return getCookie('AsteriskToken');
    }
}

export const getEmployeeID = () => {
    let MatrixToken = getCookie('MatrixToken');
    if (MatrixToken) {
        try {
            MatrixToken = JSON.parse(atob(MatrixToken));
            return MatrixToken.EmployeeId;
        } catch (e) {
            return '';
        }
    }
    else {
        return '';
    }
}

export const formatDate = (dateString) => {
    try {
        if (!dateString) return 'N/A';
        const date = parseISO(dateString);
        return format(date, 'MMM d, yyyy h:mm:ss a');
    } catch (error) {
        console.error('Date formatting error:', error);
        return dateString || 'N/A';
    }
};

export const convertDotNetDate = (dotNetDate) => {
    // Extract timestamp using regex
    const timestamp = parseInt(dotNetDate.match(/\d+/)[0], 10);

    // Convert to JavaScript Date object
    const date = new Date(timestamp);

    return date;  // Returns a Date object
}
