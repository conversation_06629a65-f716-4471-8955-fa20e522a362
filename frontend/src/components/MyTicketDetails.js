/* eslint-disable eqeqeq */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { formatDate, getUserId } from '../services/CommonHelper';
import { GetDocumentUrl, GetTicketDetails, UpdateTicketRemarks, UploadFile } from '../services/feedbackService';
import '../styles/MyTicketDetails.css';

const MyTicketDetails = () => {
    const { ticketId } = useParams();
    const [ticketDetails, setTicketDetails] = useState([]);
    const [commentList, setCommentList] = useState([]);
    const [ticketReply, setTicketReply] = useState('');
    const [fileAttachments, setFileAttachments] = useState([]);
    const [isSatisfied, setIsSatisfied] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [selected, setSelected] = useState({
        Status: undefined,
        IssueType: undefined,
        SubIssueType: undefined,
        Source: { SourceID: null }
    });

    const statusList = [
        { StatusID: 3, StatusName: "Resolved" }
    ];

    const isEmpty = (str) => {
        return typeof str === 'string' && !str.trim() || typeof str === 'undefined' || str === null;
    };

    const getTicketDetailsService = () => {
        const reqData = {
            ticketId: ticketId,
        }

        GetTicketDetails(reqData)
            .then((response) => {
                if (response) {
                    setTicketDetails(response);
                    setCommentList(response.Commentlist || []);

                    if (response.StatusID == 3) {
                        statusList.push({ StatusID: 4, StatusName: "Closed" });
                    }

                    setSelected(prev => ({
                        ...prev,
                        Status: { StatusID: response.StatusID },
                        IssueType: { ISSUEID: response.IssueID }
                    }));
                } else {
                    setTicketDetails([]);
                    setCommentList([]);
                    setSelected({
                        Status: undefined,
                        IssueType: undefined,
                        SubIssueType: undefined,
                        Source: { SourceID: null }
                    });
                }
            })
            .catch(() => {
                setTicketDetails([]);
                setCommentList([]);
                setSelected({
                    Status: undefined,
                    IssueType: undefined,
                    SubIssueType: undefined,
                    Source: { SourceID: null }
                });
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    useEffect(() => {
        getTicketDetailsService();
    }, [ticketId]);


    const handleFileChange = (event) => {
        const files = Array.from(event.target.files);
        const fileData = [];

        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const binaryStr = e.target.result;
                fileData.push({
                    FileName: file.name,
                    AttachemntContent: btoa(binaryStr),
                    AttachmentURL: "",
                    ContentType: file.type
                });

                if (fileData.length === files.length) {
                    setFileAttachments(fileData);
                }
            };
            reader.readAsBinaryString(file);
        });
    };

    const updateTicketRemarks = (replyType) => {
        if (!isSatisfied && (isEmpty(ticketReply) || ticketReply.length <= 10)) {
            toast.error(isEmpty(ticketReply) ? 'Remark should not be blank' : 'Query should be more than 10 char');
            return;
        }

        let ticketStatusId = 0;
        if (selected.Status.StatusID === 3) {
            ticketStatusId = 5;
        }
        if (isSatisfied === 1) {
            ticketStatusId = 4;
        }

        const requestData = {
            TicketID: ticketId,
            Comments: `Comments : ${ticketReply}`,
            CreatedBy: getUserId(),
            StatusID: ticketStatusId,
            ReplyType: replyType,
            FileURL: "",
            FileName: "",
            IsSatisfied: isSatisfied,
            LeadID: ticketDetails.LeadID,
            ParentID: ticketDetails.ParentID,
            PayID: ticketDetails.PayID,
            OrderID: ticketDetails.OrderID,
            IsStatusChanged: ticketDetails.StatusID !== selected.Status.StatusID
        };

        if (fileAttachments.length > 0) {
            
            UploadFile(fileAttachments)
            .then((response) => {
                const FileAttachments = response;
                requestData.FileURL = FileAttachments[0].AttachmentURL;
                requestData.FileName = FileAttachments[0].FileName;
                requestData.RefId = FileAttachments[0].RefId;

                UpdateTicketRemarksService(requestData);
            })
            .catch(() => {
                return;
            })
        } else {
            UpdateTicketRemarksService(requestData);
        }
    };

    const UpdateTicketRemarksService = (requestData) => {
        UpdateTicketRemarks(requestData)
        .then ((response) => {
            if(response) {
                toast.success('Updated successfully');
                setFileAttachments([]);
                setTicketReply('');
                getTicketDetailsService();
            }
            else {
                toast.error('Error updating ticket');
            }
        })
        .catch(() => {

        })
    }
 
    const getDocumentUrl = (docId, refId) => {
        const requestData = {
            "docId": docId,
            "RefId": refId
        }

        GetDocumentUrl(requestData)
        .then((response) => {
            const data = response.data;
            if (data?.ttlDocUrl) {
                window.open(data.ttlDocUrl, '_blank');
            }
        })
        .catch(() => {

        })   
    };

    const openInNewTab = (url) => {
        if (url) {
            window.open(url, '_blank');
        }
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    return (
        <div className="container-fluid ticketdetails">
            <div class="block-header">
                <div class="row">
                    <div class="col-lg-6 col-md-8 col-lg-12">
                        <div class="detail_links">
                            <p class="demo-button">
                                <span class="assign_hd">Assigned To :</span>
                                <button type="button" class="btn btn-outline-primary">{ticketDetails?.AssignToDetails?.Name || 'Not assigned'}
                                    {ticketDetails?.AssignToDetails?.EmployeeID &&
                                        `(${ticketDetails.AssignToDetails.EmployeeID})`}
                                </button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row clearfix">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="mail-inbox">
                            <div class="mail-right agent_tkt_view">
                                <div class="body ticket_detailbox">
                                    <div class="tab-content table_databox">
                                        <div class="tab-pane show active">
                                            <div className="table-responsive">
                                                <table className="table m-b-0">
                                                    <thead>
                                                        <tr>
                                                            <th>FeedbackID</th>
                                                            <th>CreatedOn</th>
                                                            <th>Process</th>
                                                            <th>Feedback</th>
                                                            <th>Status</th>
                                                            <th>Last Updated on</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr class="active_detaillist">
                                                            <td>{ticketDetails?.TicketDisplayID}</td>
                                                            <td>{formatDate(ticketDetails?.CreatedOn)}</td>
                                                            <td>{ticketDetails?.Process}</td>
                                                            <td>{ticketDetails?.IssueStatus}</td>
                                                            <td>{ticketDetails?.TicketStatus}</td>
                                                            <td>{formatDate(ticketDetails?.UpdatedOn)}</td>
                                                        </tr>
                                                        <tr>
                                                            {commentList && commentList.length > 0 && (
                                                                <td colspan={7} className="tkt_detailbox">
                                                                    <div className="card detialbox">
                                                                        <div className="body emailer_body">
                                                                            {commentList.map((comment, index) => (
                                                                                (comment.ReplyType == 1 || comment.ReplyType == 2)
                                                                                && (
                                                                                    <div key={index} className={`timeline-item detail_data ${comment.ReplyType === 1 ? 'green' : comment.ReplyType === 2 ? 'blue' : ''}`}>
                                                                                        <div style={{ margin: '0 0 14px 0' }}>
                                                                                            <span className="date">
                                                                                                From : <a>{comment.User?.UserName}({comment.User?.EmployeeId})</a>
                                                                                            </span>
                                                                                            <div className="right_section">
                                                                                                <span className="sl-date">{formatDate(comment.CreatedOn)}</span>
                                                                                            </div>
                                                                                            <div style={{ width: '100%', display: 'block' }}>
                                                                                                <p>{comment.Comment}</p>
                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') === -1 && (
                                                                                                    <a
                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}
                                                                                                        onClick={() => getDocumentUrl(comment.FileURL, comment.RefId)}                                                                                                    >
                                                                                                        {comment.FileName}
                                                                                                    </a>)
                                                                                                }
                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') !== -1 && (
                                                                                                    <a
                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}
                                                                                                        onClick={() => openInNewTab(comment.FileURL)}
                                                                                                    >
                                                                                                        {ticketDetails.RefTicketID}
                                                                                                    </a>)
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                )
                                                                            ))}
                                                                            <div className="mail_compose_Section">
                                                                                <div className="card shadow_none">
                                                                                    <div className="body compose_box">
                                                                                        <hr />
                                                                                        <div className="header sub_hd">
                                                                                            <h2>Add comment</h2>
                                                                                        </div>  
                                                                                        <textarea
                                                                                            className="form-control mb-3"
                                                                                            style={{ width: '100%' }}
                                                                                            rows="10"
                                                                                            value={ticketReply}
                                                                                            onChange={(e) => setTicketReply(e.target.value)}
                                                                                            placeholder="Enter your comment..."
                                                                                        />
                                                                                        <div className="m-t-30 compose_action_button">
                                                                                            {selected.Status.StatusID === 3 && (
                                                                                                <div style={{ fontWeight: 'bold', margin: '4px' }}>
                                                                                                    This feedback has been marked as resolved, if satisfied please mark 'Yes' to close
                                                                                                    else 'No' to re-open the same.<br /><br />
                                                                                                    <div className="form-check form-check-inline">
                                                                                                        <input
                                                                                                            type="radio"
                                                                                                            className="form-check-input"
                                                                                                            id="satisfiedYes"
                                                                                                            name="satisfied"
                                                                                                            value="1"
                                                                                                            checked={isSatisfied === 1}
                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}
                                                                                                        />
                                                                                                        <label className="form-check-label" htmlFor="satisfiedYes">Yes</label>
                                                                                                    </div>
                                                                                                    <div className="form-check form-check-inline">
                                                                                                        <input
                                                                                                            type="radio"
                                                                                                            className="form-check-input"
                                                                                                            id="satisfiedNo"
                                                                                                            name="satisfied"
                                                                                                            value="2"
                                                                                                            checked={isSatisfied === 2}
                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}
                                                                                                        />
                                                                                                        <label className="form-check-label" htmlFor="satisfiedNo">No</label>
                                                                                                    </div>
                                                                                                </div>
                                                                                            )}
                                                                                            <div style={{ display: 'none', width: '100%', padding: '0 0 10px 0' }}>
                                                                                                <label>Choose Status</label>
                                                                                                <select 
                                                                                                    className="form-control" 
                                                                                                    value={selected.Status?.StatusID || ''} 
                                                                                                    onChange={(e) => setSelected(prev => ({
                                                                                                        ...prev,
                                                                                                        Status: { StatusID: parseInt(e.target.value) }
                                                                                                    }))}
                                                                                                    disabled={false} // Replace with your inActive condition
                                                                                                    style={{ display: 'inline-block', width: 'auto', margin: '0 5px 0 0' }}
                                                                                                >
                                                                                                    <option value="">Select Status</option>
                                                                                                    {statusList
                                                                                                        .filter((status, index, self) => 
                                                                                                            index === self.findIndex(s => s.StatusID === status.StatusID))
                                                                                                        .sort((a, b) => (a.Sequence || 0) - (b.Sequence || 0))
                                                                                                        .map(status => (
                                                                                                            <option key={status.StatusID} value={status.StatusID}>
                                                                                                                {status.StatusName}
                                                                                                            </option>
                                                                                                        ))
                                                                                                    }
                                                                                                </select>
                                                                                            </div>
                                                                                            {selected.Status.StatusID != 4 && (
                                                                                                <div className="upload_box ng-scope">
                                                                                                    <input
                                                                                                        type="file"
                                                                                                        id="file-3"
                                                                                                        className="inputfile inputfile-3 d-none"
                                                                                                        onChange={handleFileChange}
                                                                                                        multiple
                                                                                                        accept=".jpg,.pdf,.xlsx"
                                                                                                    />
                                                                                                    <label htmlFor="file-3" className="upload_docs" style={{ display: 'inline' }}>
                                                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17">
                                                                                                            <path d="M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z" />
                                                                                                        </svg>
                                                                                                    </label>
                                                                                                    {fileAttachments.length > 0 && (
                                                                                                        <div className="mt-2">
                                                                                                            {fileAttachments.map((file, index) => (
                                                                                                                <span key={index} 
                                                                                                                    className="attachment_files">
                                                                                                                    {file.FileName} 
                                                                                                                    <em onClick={() => {
                                                                                                                        const updated = [...fileAttachments];
                                                                                                                        updated.splice(index, 1);
                                                                                                                        setFileAttachments(updated);
                                                                                                                    }}>X</em>
                                                                                                                </span>
                                                                                                            ))}
                                                                                                        </div>
                                                                                                    )}
                                                                                                </div>
                                                                                            )}
                                                                                            {selected.Status.StatusID != 4 &&
                                                                                                <button
                                                                                                    type="button"
                                                                                                    className="btn btn-success"
                                                                                                    onClick={() => updateTicketRemarks(1)}
                                                                                                >
                                                                                                    Submit
                                                                                                </button>
                                                                                            }
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            )}
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MyTicketDetails;