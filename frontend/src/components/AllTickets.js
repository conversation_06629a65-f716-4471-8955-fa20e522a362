import React, { useState, useEffect } from 'react';
import FeedbackTable from './FeedbackTable';
import { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import '../styles/MyFeedback.css';
import '../styles/FeedbackStats.css';
import { getUserId } from '../services/CommonHelper';
import alasql from 'alasql';

const AllTickets = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(4);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData?.[0]?.ProcessID > 0) {
                        setSelected(prev => ({
                            ...prev,
                            Source: { SourceID: userDetails.EMPData[0].ProcessID }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSalesTicketCount(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    data.forEach(item => {
                        switch (item.StatusID) {
                            case 1:
                                setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                break;
                            case 2:
                                setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                break;
                            case 3:
                                setStats(prev => ({ ...prev, Resolved: item.Count }));
                                break;
                            case 4:
                                setStats(prev => ({ ...prev, Closed: item.Count }));
                                break;
                            case 5:
                                setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                break;
                            default:
                                break;
                        }
                    });
                }
            })
            .catch(() => {
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var fromDateStr = formatDateForRequest(fromDate,3);
        var toDateStr = formatDateForRequest(toDate,0);

        if(status === 8){
            fromDateStr = formatDateForRequest(fromDate,0);
            toDateStr = formatDateForRequest(toDate,0);
        } 

        const obj = {
            EmpID: parseInt(getUserId() || 0),
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: selected.Source?.SourceID || 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            ProductID: selected.Product ? selected.Product.ProductID : 0,
            FeedBackTypeID: 4
        };

        GetAdminTicketList(obj)
            .then((data) => {
                if (data && data.length > 0) {
                    const sortedFeedbacks = [...data].sort((a, b) => 
                        new Date(b.CreatedOn) - new Date(a.CreatedOn)
                    );
                    setFeedbacks(sortedFeedbacks);
                } else {
                    setFeedbacks([]);
                }
            })
            .catch(() => {
                setFeedbacks([]);
            });
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {
        alasql.fn.datetime = function (dateStr) {
            var date = new Date(parseInt(dateStr.substr(6)));
            return date.toLocaleString();
        };
        
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,'
            + 'CreatedByDetails -> EmployeeID as EmpID,'
            + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + Date.now() + '.xlsx", { headers: true }) FROM ? ', [feedbacks]
        );
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1, color: '#49b1d4' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2, color: '#e0e02d' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5, color: '#ff3131' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3, color: '#53e653' },
        { label: 'Closed', count: stats.Closed || 0, id: 4, color: '#2e7b2e' }
    ];

    return (
        <div className="container-fluid">
            <div className="block-header">
                <div className="row">
                    <div className="col-lg-6 col-md-8 col-lg-12">
                        <ul className="breadcrumb adv_search">
                            <li className="breadcrumb-item active"><b>All Feedback</b></li>
                        </ul>
                        <div className="col-lg-6 hidden-sm text-right switch_btns">
                            <button className="btn btn-sm btn-outline-info" onClick={() => setActiveSearchType(1)}>Search</button>
                            <button 
                                className="btn btn-sm btn-outline-secondary" 
                                onClick={() => {
                                    setActiveSearchType(2)
                                    setSelected({
                                        Source: { SourceID: 0, Name: 'Select' },
                                        IssueType: undefined,
                                        Status: undefined
                                    });
                                    setTicketId('');
                                }} 
                            >Dashboard</button>
                        </div>
                    </div>
                </div>
            </div>

            {activeSearchType === 2 && (
                <div className="feedback-stats">
                    {statCards.map((stat) => (
                        <div
                            key={stat.label}
                            className="stat-card"
                            style={{ backgroundColor: stat.color }}
                            onClick={() => GetAgentTicketList(stat.id)}
                        >
                            <h2>{stat.count}</h2>
                            <p>{stat.label}</p>
                        </div>
                    ))}
                </div>
            )}

            {activeSearchType === 1 && (
                <div className="row clearfix">
                    <div className="col-md-12">
                        <div className="card">
                            <div className="body">
                                <div className="row clearfix">
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>From</label>
                                            <DatePicker
                                                selected={fromDate}
                                                onChange={date => setFromDate(date)}
                                                className="form-control"
                                                dateFormat="dd-MM-yyyy"
                                            />
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>To</label>
                                            <DatePicker
                                                selected={toDate}
                                                onChange={date => setToDate(date)}
                                                className="form-control"
                                                dateFormat="dd-MM-yyyy"
                                            />
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Process</label>
                                            <select 
                                                className="form-control"
                                                value={selected.Source?.SourceID || 0}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    Source: { SourceID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                {source.map(s => (
                                                    <option key={s.SourceID} value={s.SourceID}>{s.Name}</option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                    {selected.Source?.SourceID && [2, 4, 5, 8].includes(selected.Source?.SourceID) && (
                                        <div className="col-lg-3 col-md-6 col-sm-12">
                                            <div className="form-group">
                                                <label>Product</label>
                                                <select 
                                                    className="form-control"
                                                    value={selected.Source?.SourceID || 0}
                                                    onChange={(e) => setSelected(prev => ({
                                                        ...prev,
                                                        Product: parseInt(e.target.value)
                                                    }))}
                                                >
                                                    {ProductOptions.map(p => (
                                                        <option key={p.ProductID} value={p.ProductID}>{p.Name}</option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>
                                    )}
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Feedback</label>
                                            <select
                                                className="form-control"
                                                value={selected.IssueType?.IssueID || ''}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    IssueType: { IssueID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                <option value="">Select Feedback</option>
                                                {issueSubIssue
                                                    .filter(item => item.SourceID === selected.Source?.SourceID)
                                                    .map(issue => (
                                                        <option key={issue.IssueID} value={issue.IssueID}>
                                                            {issue.ISSUENAME}
                                                        </option>
                                                    ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Status</label>
                                            <select
                                                className="form-control"
                                                value={selected.Status?.StatusID || ''}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    Status: { StatusID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                <option value="">Select Status</option>
                                                {statusList.map(status => (
                                                    <option key={status.StatusID} value={status.StatusID}>
                                                        {status.StatusName}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Ticket ID</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                value={ticketId}
                                                onChange={(e) => setTicketId(e.target.value)}
                                            />
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="m-t-15 advance_search_btn">
                                            <button className="btn btn-primary" onClick={() => GetAgentTicketList(8)}>
                                                Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="row clearfix">
                <div className="col-md-12">
                    {feedbacks.length > 0 && (
                        <button className="btn btn-info" onClick={exportData}>Export Data</button>
                    )}
                    <div className="card">
                        <div className="body">
                            <FeedbackTable feedbacks={feedbacks} type={5} redirectPage={'/TicketDetails/'}/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AllTickets;