import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, LinearProgress, Typography } from '@mui/material';
import { GetSalesTicketUserDetails } from '../services/feedbackService';

const LandingPage = () => {
    const { type, ticketId } = useParams();
    const navigate = useNavigate();

    const handleUserDetails = (data) => {
        if (data.error) {
            navigate('/login');
            return false;
        } 
        else if (!data || data.length === 0 || !data.EMPData) {
            navigate('/login');
            return false;
        }

        localStorage.setItem('UserDetails', 
            JSON.stringify({ 
                "EMPData": data.EMPData, 
                "Token": '', 
                "IsLocSet": 1, 
                "Location": data.Location, 
                "Issue": { "IssueID": 0, "SubIssueID": 0 } 
            })
        );
        
        const userDetails = JSON.parse(localStorage.getItem('UserDetails'));
        const BU = userDetails.EMPData[0].BU;
        
        if (BU === 0) {
            navigate('/editprofile');
        }
        else if (["ticket","jag","incentive"].includes(type)) {
            navigate('/createFeedBack');
        }
        else if (type === "notification") {
            navigate(`/MyTicketDetails/${ticketId}`);
        }
        else if (type === "ticketview") {
            navigate(`/TicketDetails/${ticketId}`);
        }
        else if (type === "MyFeedBack") {
            navigate(`/MyFeedBackDetails/${ticketId}`);
        }
        else {
            navigate('/myFeedBack');
        }

        return true;
    };

    useEffect(() => {
        GetSalesTicketUserDetails()
        .then((response) => {
            handleUserDetails(response);
        })
        .catch(() => {
            navigate('/login');
        })
    }, [ type, ticketId, navigate]);


    return (
        <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            minHeight="100vh"
        >
            <LinearProgress />
            <Typography variant="h6" sx={{ mt: 2 }}>
                Please wait...
            </Typography>
        </Box>
    );
};

export default LandingPage;