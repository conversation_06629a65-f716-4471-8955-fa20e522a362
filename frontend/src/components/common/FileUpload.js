import React, { useState } from 'react';
import {
    Box,
    Button,
    LinearProgress,
    Typography,
    IconButton,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction
} from '@mui/material';
import { CloudUpload, Delete } from '@mui/icons-material';
import axios from 'axios';

const FileUpload = ({ onUploadComplete, maxFiles = 5, acceptedTypes = '*' }) => {
    const [files, setFiles] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState('');

    const handleFileSelect = (event) => {
        const selectedFiles = Array.from(event.target.files);
        if (files.length + selectedFiles.length > maxFiles) {
            setError(`You can only upload up to ${maxFiles} files`);
            return;
        }
        setFiles(prevFiles => [...prevFiles, ...selectedFiles]);
        setError('');
    };

    const handleFileRemove = (index) => {
        setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
    };

    const handleUpload = async () => {
        if (files.length === 0) return;

        setUploading(true);
        setProgress(0);
        setError('');

        const formData = new FormData();
        files.forEach(file => {
            formData.append('files', file);
        });

        try {
            const response = await axios.post(
                `${process.env.REACT_APP_API_URL}/api/upload`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${localStorage.getItem('token')}`
                    },
                    onUploadProgress: (progressEvent) => {
                        const percentCompleted = Math.round(
                            (progressEvent.loaded * 100) / progressEvent.total
                        );
                        setProgress(percentCompleted);
                    }
                }
            );

            onUploadComplete(response.data);
            setFiles([]);
            setProgress(0);
        } catch (err) {
            setError(err.message || 'Failed to upload files');
        } finally {
            setUploading(false);
        }
    };

    return (
        <Box>
            <input
                type="file"
                multiple
                accept={acceptedTypes}
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                id="file-upload-input"
                disabled={uploading}
            />
            <label htmlFor="file-upload-input">
                <Button
                    variant="outlined"
                    component="span"
                    startIcon={<CloudUpload />}
                    disabled={uploading || files.length >= maxFiles}
                >
                    Select Files
                </Button>
            </label>

            {error && (
                <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {error}
                </Typography>
            )}

            {files.length > 0 && (
                <>
                    <List>
                        {files.map((file, index) => (
                            <ListItem key={index}>
                                <ListItemText
                                    primary={file.name}
                                    secondary={`${(file.size / 1024).toFixed(2)} KB`}
                                />
                                <ListItemSecondaryAction>
                                    <IconButton
                                        edge="end"
                                        onClick={() => handleFileRemove(index)}
                                        disabled={uploading}
                                    >
                                        <Delete />
                                    </IconButton>
                                </ListItemSecondaryAction>
                            </ListItem>
                        ))}
                    </List>

                    <Button
                        variant="contained"
                        onClick={handleUpload}
                        disabled={uploading || files.length === 0}
                        sx={{ mt: 2 }}
                    >
                        Upload
                    </Button>

                    {uploading && (
                        <Box sx={{ mt: 2 }}>
                            <LinearProgress variant="determinate" value={progress} />
                            <Typography variant="body2" color="text.secondary" align="center">
                                {progress}%
                            </Typography>
                        </Box>
                    )}
                </>
            )}
        </Box>
    );
};

export default FileUpload; 