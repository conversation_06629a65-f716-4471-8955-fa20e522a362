import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { updateUserProfile } from '../services/feedbackService';

const EditProfile = () => {
    const navigate = useNavigate();
    const [selectedProduct, setSelectedProduct] = useState({ ProductID: 0, Name: 'Select' });

    const productList = [
        { ProductID: 0, Name: 'Select' },
        { ProductID: 115, Name: 'Investment' },
        { ProductID: 7, Name: 'Term' },
        { ProductID: 2, Name: 'Health' },
        { ProductID: 117, Name: 'Motor' },
        { ProductID: 255, Name: 'Others(Non- Ops, Product, HR, Tech Etc.)' }
    ];
  
    const handleSubmit = () => {
        const user = JSON.parse(localStorage.getItem('UserDetails'));

        if (selectedProduct.ProductID === 0) {
            alert('Please select Product');
            return;
        }

        const payload = {
            EmpID: user.EMPData[0].EmpID,
            BU: selectedProduct.ProductID
        };

        updateUserProfile(payload)
        .then((res) => {
            if (res && res.Status > 0) {
                alert('Successfully Updated.');
                navigate('/myFeedBack');
            }
        })
        .catch(() => {
            navigate('/login');
        });
    };

    return (
        <div className="container-fluid">
            <div className="block-header">
                <div className="row">
                    <div className="col-lg-6 col-md-8 col-lg-12">
                        <ul className="breadcrumb adv_search">
                            <li className="breadcrumb-item active"><b>Update BU</b></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="row clearfix">
                <div className="col-lg-12">
                    <div className="card">
                        <div className="mail-inbox">
                            <div className="mail-right agent_tkt_view">
                                <div className="body ticket_detailbox">
                                    <div className="mail_compose_Section">
                                        <div className="card shadow_none">
                                            <div className="body compose_box">
                                                <form>
                                                    <div className="row clearfix">
                                                        <div className="col-lg-6 col-md-6 col-sm-12">
                                                            <div className="form-group grp1">
                                                                <label>Select BU</label>
                                                                <select
                                                                    className="form-control"
                                                                    value={selectedProduct.ProductID}
                                                                    onChange={(e) => {
                                                                        const selectedId = parseInt(e.target.value);
                                                                        const selected = productList.find(p => p.ProductID === selectedId);
                                                                        setSelectedProduct(selected);
                                                                    }}
                                                                >
                                                                    {productList.map((item, idx) => (
                                                                        <option key={idx} value={item.ProductID}>{item.Name}</option>
                                                                    ))}
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>

                                                <div className="m-t-30 compose_action_button">
                                                    <button type="button" className="btn btn-success" onClick={handleSubmit}>Submit</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EditProfile;
