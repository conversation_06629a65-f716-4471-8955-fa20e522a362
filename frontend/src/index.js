import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Import Bootstrap and its dependencies
import 'jquery/dist/jquery.min.js';
import 'popper.js/dist/umd/popper.min.js';
import 'bootstrap/dist/js/bootstrap.min.js';
import 'bootstrap/dist/css/bootstrap.min.css';

// Import Font Awesome
import '@fortawesome/fontawesome-free/css/all.min.css';

// Import Google Fonts
import 'typeface-open-sans';

// Import custom styles
import './styles/tickets.css';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
	<React.StrictMode>
		<App />
	</React.StrictMode>
);
