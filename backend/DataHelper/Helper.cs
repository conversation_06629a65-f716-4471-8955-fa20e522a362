using System;
using System.Text;
using System.Data;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http;

namespace Helper
{
    public static class CoreCommonMethods
    {
        private static readonly DateTime UnixEpoch = new DateTime(1970, 1, 1, 5, 30, 0);
        private static IHttpContextAccessor _httpContextAccessor;

        public static void Configure(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public static long ToSafeLong(this string Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (long)i;
            }
            else
            {
                long i = 0;
                long.TryParse(Input, out i);
                return i;
            }
        }
        public static string[] Split2(this String Input, string split)
        {
            return Input.Split(split).Where(t => !string.IsNullOrEmpty(t)).ToArray();
        }
        public static int ToSafeInt(this String Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (int)i;
            }
            else
            {
                int i = 0;
                int.TryParse(Input, out i);
                return i;
            }
        }
        public static bool ToSafeBool(this String Input)
        {
            if (string.IsNullOrEmpty(Input))
            {
                return false;
            }
            else
            {
                bool result;
                if (bool.TryParse(Input, out result))
                {
                    return result;
                }
                else
                {
                    return false;
                }
            }
        }
        public static string NullSafeReplace(this string input, char replace, char replaceWith)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }
            else
            {
                return input.Replace(replace, replaceWith);
            }
        }

        public static bool IsValidString(string input)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(input) && input != "undefined" &&  input != "null")
                result = true;
            return result;
        }
        public static string Base64Decode(string base64EncodedData)
        {
            var base64EncodedBytes = Convert.FromBase64String(base64EncodedData);
            return Encoding.UTF8.GetString(base64EncodedBytes);
        }
        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }
        public static string GetEnvironmentVar()
        {
            string Enviornment = string.Empty;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            
            var environmentValue = con.GetSection("Environment")?.Value;
            Enviornment = environmentValue != null ? environmentValue.ToString().ToUpper() : string.Empty;
            return Enviornment;
        }

        public static string ReadCookies(string reqCookies)
        {
            string request = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(reqCookies))
                {

                    byte[] data = Convert.FromBase64String(reqCookies);
                    string decodedString = Encoding.UTF8.GetString(data);

                    if (decodedString != null)
                        request = decodedString;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in ReadCookies: " + ex.ToString());
            }

            return request;
        }

        public static DateTime UnixTimeToDateTime(long unixtime)
        {
            var dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dtDateTime = dtDateTime.AddMilliseconds(unixtime).ToLocalTime();
            return dtDateTime;
        }
    }

}
