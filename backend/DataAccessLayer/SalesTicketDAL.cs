using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Helper;
using PropertyLayers;

namespace DataAccessLayer
{
    public class SalesTicketDAL
    {
        public static Result CreateTicket(SalesTicketModel obj)
        {
            Result objNewTicketResponse = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.LivesqlConnection());
                SqlCommand cmd = new("CARE.CreateSalesTicket", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@Title", SqlDbType.VarChar).Value = obj.Title;
                cmd.Parameters.Add("@sourceId", SqlDbType.SmallInt).Value = obj.SourceID;
                cmd.Parameters.Add("@IssueId", SqlDbType.SmallInt).Value = obj.IssueID;
                cmd.Parameters.Add("@ProductID", SqlDbType.SmallInt).Value = obj.ProductID;
                cmd.Parameters.Add("@LeadID", SqlDbType.BigInt).Value = Convert.ToInt64(obj.LeadID);
                cmd.Parameters.Add("@ParentID", SqlDbType.BigInt).Value = Convert.ToInt64(obj.ParentID);
                cmd.Parameters.Add("@createdBy", SqlDbType.Int).Value = obj.CreatedBy;
                cmd.Parameters.Add("@Status", SqlDbType.SmallInt).Value = 1;
                cmd.Parameters.Add("@comments", SqlDbType.VarChar).Value = obj.Comments;
                cmd.Parameters.Add("@fileurl", SqlDbType.VarChar).Value = obj.FileURL ?? "";
                cmd.Parameters.Add("@fileName", SqlDbType.VarChar).Value = obj.FileName ?? "";
                cmd.Parameters.Add("@ReplyType", SqlDbType.SmallInt).Value = obj.ReplyType;
                cmd.Parameters.Add("@PayID", SqlDbType.VarChar).Value = obj.PayID ?? "";
                cmd.Parameters.Add("@OrderID", SqlDbType.VarChar).Value = obj.OrderID ?? "";
                cmd.Parameters.Add("@RefTicketID", SqlDbType.VarChar).Value = obj.RefTicketID ?? "";

                if (!string.IsNullOrEmpty(obj.Source))
                {
                    cmd.Parameters.Add("@source", SqlDbType.VarChar).Value = obj.Source;
                }

                SqlParameter outputStatus = new("@result", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputStatus);

                SqlParameter outputTicketId = new("@ticketdisplayId", SqlDbType.VarChar, 200)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputTicketId);

                cmd.ExecuteNonQuery();

                objNewTicketResponse.Status = Convert.ToInt32(outputStatus.Value);
                objNewTicketResponse.TicketDisplayId = Convert.ToString(outputTicketId.Value) ?? string.Empty;
            }
            catch (Exception)
            {

            }

            return objNewTicketResponse;
        }

        public static DataSet GetSalesTicketByAgentId(int userId, int status, int type)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesTicketByAgentId", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@userId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@status", SqlDbType.Int).Value = status;
                cmd.Parameters.Add("@Type", SqlDbType.SmallInt).Value = type;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {
                // Log exception
            }
            return ds;
        }

        public static DataSet GetSalesTicketDetailsByID(int ticketId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesTicketDetailsByID", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = ticketId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {
                // Log exception
            }
            return ds;
        }

        public static Result UpdateSalesTicketByID(SalesTicketModel obj)
        {
            Result objResponse = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.LivesqlConnection());
                SqlCommand cmd = new("CARE.UpdateSalesTicketByID", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = Convert.ToInt32(obj.TicketID);
                cmd.Parameters.Add("@fileurl", SqlDbType.VarChar).Value = obj.FileURL ?? "";
                cmd.Parameters.Add("@fileName", SqlDbType.VarChar).Value = obj.FileName ?? "";
                cmd.Parameters.Add("@ReplyType", SqlDbType.SmallInt).Value = obj.ReplyType;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.BigInt).Value = obj.CreatedBy;
                cmd.Parameters.Add("@comments", SqlDbType.VarChar).Value = obj.Comments ?? "";
                cmd.Parameters.Add("@StatusID", SqlDbType.Int).Value = obj.StatusID;
                cmd.Parameters.Add("@IsSatisfied", SqlDbType.TinyInt).Value = obj.IsSatisfied;
                cmd.Parameters.Add("@RefId", SqlDbType.VarChar).Value = obj.RefId ?? "";

                SqlParameter outputStatus = new("@result", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputStatus);

                cmd.ExecuteNonQuery();

                objResponse.Status = Convert.ToInt32(outputStatus.Value);
            }
            catch
            {

            }
            return objResponse;
        }

        public static DataSet SalesTicketDashboardSearch(SearchTicket obj)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("[CARE].[SalesTicketDashboardSearch]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@fromDate", SqlDbType.DateTime).Value = obj.FromDate;
                cmd.Parameters.Add("@toDate", SqlDbType.DateTime).Value = obj.ToDate;
                cmd.Parameters.Add("@processId", SqlDbType.Int).Value = obj.ProcessID;
                cmd.Parameters.Add("@IssueId", SqlDbType.Int).Value = obj.IssueID;
                cmd.Parameters.Add("@Staus", SqlDbType.Int).Value = obj.StatusID;
                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = obj.TicketID;
                cmd.Parameters.Add("@ticketDisplayId", SqlDbType.VarChar).Value = obj.TicketDisplayID;
                cmd.Parameters.Add("@AssignTo", SqlDbType.Int).Value = obj.AssignTo;
                cmd.Parameters.Add("@productId", SqlDbType.Int).Value = obj.ProductID;
                cmd.Parameters.Add("@UserID", SqlDbType.Int).Value = obj.EmpID;
                cmd.Parameters.Add("@FeedBackTypeID", SqlDbType.Int).Value = obj.FeedBackTypeID;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {
                // Log exception
            }
            return ds;
        }

        public static DataSet GetSalesTicketStatusMaster()
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesTicketStatusMaster", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {
                // Log exception
            }
            return ds;
        }

        public static DataSet GetSalesProcessMaster(long userId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesProcessMaster", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {

            }
            return ds;
        }

        public static DataSet GetSalesIssueMaster()
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesIssueMaster", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {

            }
            return ds;
        }

        public static Result AssignSalesTicket(SalesTicketAssignment obj)
        {
            Result objResponse = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.LivesqlConnection());
                SqlCommand cmd = new("CARE.AssignSalesTicket", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = obj.TicketID;
                cmd.Parameters.Add("@ProcessId", SqlDbType.Int).Value = obj.ProcessId;
                cmd.Parameters.Add("@AssignToUserID", SqlDbType.Int).Value = obj.AsssignToUserID;
                cmd.Parameters.Add("@AssignByUserID", SqlDbType.Int).Value = obj.AsssignByUserID;

                SqlParameter outputStatus = new SqlParameter("@result", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputStatus);

                cmd.ExecuteNonQuery();

                objResponse.Status = Convert.ToInt32(outputStatus.Value);
            }
            catch (Exception)
            {

            }
            return objResponse;
        }

        public static DataSet GetSalesDetailsForCommunication(int ticketId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesDetailsForCommunication", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = ticketId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch
            {

            }
            return ds;
        }

        public static DataSet GetSalesTicketUserRole(long userId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("CARE.GetSalesTicketUserRole", con)
                {
                    CommandType = CommandType.StoredProcedure
                };
                cmd.Parameters.Add("@userId", SqlDbType.Int).Value = userId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch (Exception)
            {

            }
            return ds;
        }

        public static DataSet GetSalesTicketProcessUser(int ticketId, int ProcessId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("[CARE].[GetSalesTicketUserListByProcess]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = ticketId;
                cmd.Parameters.Add("@ProcessId", SqlDbType.Int).Value = ProcessId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch
            {

            }
            return ds;
        }

        public static DataSet GetSalesTicketLog(int ticketId, int logtype, int userId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("[CARE].[GetSalesTicketLog]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@ticketId", SqlDbType.Int).Value = ticketId;

                using SqlDataAdapter adapter = new(cmd);
                adapter.Fill(ds);
            }
            catch
            {

            }
            return ds;
        }

        public static Result ReAssignSalesTicket(SalesTicketAssignment obj)
        {
            Result objResponse = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.LivesqlConnection());
                SqlCommand cmd = new("[CARE].[SalesTicketAutoAssign]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@TicketID", SqlDbType.Int).Value = obj.TicketID;
                cmd.Parameters.Add("@PrevAssignedToSupervisor", SqlDbType.Bit).Value = true;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.BigInt).Value = obj.AsssignByUserID;

                SqlParameter outputStatus = new("@result", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputStatus);

                cmd.ExecuteNonQuery();

                objResponse.Status = Convert.ToInt32(outputStatus.Value);
            }
            catch (Exception)
            {

            }
            return objResponse;
        }

        public static DataSet GetSalesTicketUserDetails(long userId)
        {
            DataSet ds = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.ReplicasqlConnection());
                SqlCommand cmd = new("[CARE].[GetSalesTicketUserDetails]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@userId", SqlDbType.Int).Value = userId;

                using SqlDataAdapter adp = new(cmd);
                adp.Fill(ds);
            }
            catch (Exception ex)
            {

            }
            return ds;
        }

        public static Result UpdateSalesUserProfile(Empdata obj)
        {

            Result objNewTicketResponse = new();
            try
            {
                using SqlConnection con = new(ConnectionClass.LivesqlConnection());
                SqlCommand cmd = new("[CARE].[UpdateSalesUserProfile]", con)
                {
                    CommandType = CommandType.StoredProcedure
                };

                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = obj.EmpID;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = obj.BU;

                SqlParameter outputStatus = new("@result", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputStatus);

                cmd.ExecuteNonQuery();

                objNewTicketResponse.Status = Convert.ToInt32(outputStatus.Value);
            }
            catch (Exception ex)
            {

            }

            return objNewTicketResponse;
        }
    }
} 