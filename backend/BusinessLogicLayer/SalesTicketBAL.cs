using System.Data;
using DataAccessLayer;
using PropertyLayers;
using System.Text;
using MongoConfigProject;
using Newtonsoft.Json;

namespace BusinessLogicLayer
{
    public class SalesTicketBAL : ISalesTicketBAL
    {
        public Result CreateTicket(SalesTicketModel obj)
        {
            Result result = SalesTicketDAL.CreateTicket(obj);
            if (result.Status > 0)
            {
                SendInternalFeedbackCommunication(Convert.ToInt32(result.TicketDisplayId), obj.Comments, obj.CreatedBy, SalesCommon.CommType.Creation);
            }
            return result;
        }

        public List<SalesTicketStatus> GetSalesTicketCount(int userId, int status, int type)
        {
            List<SalesTicketStatus> objlist = [];

            if (userId > 0 && CheckUserAccesslevel(userId, type))
            {
                DataSet ds = SalesTicketDAL.GetSalesTicketByAgentId(userId, status, type);
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
                {
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {
                        SalesTicketStatus details = new()
                        {
                            StatusID = Convert.ToInt32(dr["TicketStatusId"]),
                            StatusName = Convert.ToString(dr["StatusName"]) ?? string.Empty,
                            Count = Convert.ToInt32(dr["TicketCount"])
                        };
                        objlist.Add(details);
                    }
                }
            }
            else
            {
                objlist.Add(new SalesTicketStatus { StatusCode = -1 });
            }

            return objlist;
        }

        public List<SalesTicketModel> GetSalesTicketByAgentId(int userId, int status, int type)
        {
            List<SalesTicketModel> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesTicketByAgentId(userId, status, type);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    SalesTicketModel details = new SalesTicketModel();
                    details.TicketDisplayID = Convert.ToString(dr["TicketDisplayID"]) ?? string.Empty;
                    details.TicketID = Convert.ToString(dr["TicketId"]) ?? string.Empty;
                    details.Process = Convert.ToString(dr["Source"]) ?? string.Empty;
                    details.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    details.IssueStatus = Convert.ToString(dr["IssueStatus"]) ?? string.Empty;
                    details.TicketStatus = Convert.ToString(dr["TicketStatus"]) ?? string.Empty;
                    details.UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]);
                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public SalesTicketModel GetSalesTicketDetailsByID(int ticketId)
        {
            SalesTicketModel details = new();
            DataSet ds = SalesTicketDAL.GetSalesTicketDetailsByID(ticketId);

            if (ds != null && ds?.Tables?.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    details = new()
                    {
                        TicketDisplayID = Convert.ToString(dr["TicketDisplayID"]) ?? string.Empty,
                        TicketID = Convert.ToString(dr["TicketId"]) ?? string.Empty,
                        ProcessID = Convert.ToInt32(dr["SourceId"]),
                        Process = Convert.ToString(dr["Source"]) ?? string.Empty,
                        CreatedOn = Convert.ToDateTime(dr["CreatedOn"]),
                        IssueStatus = Convert.ToString(dr["IssueStatus"]) ?? string.Empty,
                        TicketStatus = Convert.ToString(dr["TicketStatus"]) ?? string.Empty,
                        UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]),
                        StatusID = Convert.ToInt32(dr["Status"]),
                        IssueID = Convert.ToInt32(dr["IssueId"]),
                        ProductName = Convert.ToString(dr["ProductName"]) ?? string.Empty,
                        MatrixRole = Convert.ToString(dr["MatrixRole"]) ?? string.Empty,
                        BU = Convert.ToString(dr["BUName"]) ?? string.Empty,
                        LeadID = Convert.ToString(dr["LeadId"]) ?? string.Empty,
                        ParentID = Convert.ToString(dr["ParentId"]) ?? string.Empty,
                        PayID = Convert.ToString(dr["PayID"]) ?? string.Empty,
                        OrderID = Convert.ToString(dr["OrderID"]) ?? string.Empty,
                        RefTicketID = Convert.ToString(dr["RefTicketID"]) ?? string.Empty,

                        AssignToDetails = new Empdata
                        {
                            EmpID = Convert.ToInt32(dr["AssignTO"]),
                            EmployeeID = Convert.ToString(dr["AssignToEmployeeId"]) ?? string.Empty,
                            Name = Convert.ToString(dr["AssignToUserName"]) ?? string.Empty
                        },

                        CreatedByDetails = new Empdata
                        {
                            EmpID = Convert.ToInt32(dr["CreatedBy"]),
                            EmployeeID = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                            Name = Convert.ToString(dr["UserName"]) ?? string.Empty,
                            MatrixRoleId = Convert.ToInt32(dr["MatrixRoleId"]),
                            ContactNo = dr["UserContactNo"].ToString() == "" ? 0 : Convert.ToInt64(dr["UserContactNo"])
                        },

                        ManagerDetails = new Empdata
                        {
                            EmployeeID = Convert.ToString(dr["ManagerEcode"]) ?? string.Empty,
                            Name = Convert.ToString(dr["ManagerUserName"]) ?? string.Empty,
                            ManagerNo = dr["ManagerNo"].ToString() == "" ? 0 : Convert.ToInt64(dr["ManagerNo"])
                        }
                    };
                }

                List<TicketComments> commentList = new();
                if (ds.Tables.Count > 1)
                {
                    foreach (DataRow dr in ds.Tables[1].Rows)
                    {
                        TicketComments comment = new()
                        {
                            Comment = Convert.ToString(dr["Comment"]) ?? string.Empty,
                            ReplyType = 1,
                            FileURL = Convert.ToString(dr["FileUrl"]) ?? string.Empty,
                            FileName = Convert.ToString(dr["FileName"]) ?? string.Empty,
                            RefId = Convert.ToString(dr["RefId"]) ?? "0",
                            User = new SalesTicketUser
                            {
                                EmployeeId = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                                UserName = Convert.ToString(dr["UserName"]) ?? string.Empty
                            },
                            CreatedOn = Convert.ToDateTime(dr["CreatedOn"])
                        };
                        commentList.Add(comment);
                    }
                }

                if (commentList != null && commentList.Count > 0)
                {
                    details.Commentlist = commentList.OrderBy(e => e.CreatedOn).ToList();
                }
            }

            return details;
        }

        public Result UpdateSalesTicketByID(SalesTicketModel obj)
        {
            Result resultobj = SalesTicketDAL.UpdateSalesTicketByID(obj);
            if (resultobj.Status > 0)
            {
                PostTicketNotification(obj);
                if (!string.IsNullOrEmpty(obj.Comments) && !string.IsNullOrEmpty(obj.TicketID))
                {
                    SendInternalFeedbackCommunication(Convert.ToInt32(obj.TicketID), obj.Comments, obj.CreatedBy, SalesCommon.CommType.Update);
                }
            }
            return resultobj;
        }

        public List<SalesTicketModel> SalesTicketDashboardSearch(SearchTicket obj)
        {
            List<SalesTicketModel> objlist = [];
            DataSet ds = SalesTicketDAL.SalesTicketDashboardSearch(obj);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    SalesTicketModel details = new()
                    {
                        TicketDisplayID = Convert.ToString(dr["TicketDisplayID"]) ?? string.Empty,
                        TicketID = Convert.ToString(dr["TicketId"]) ?? string.Empty,
                        ProcessID = Convert.ToInt32(dr["SourceId"]),
                        Process = Convert.ToString(dr["Source"]) ?? string.Empty,
                        CreatedOn = Convert.ToDateTime(dr["CreatedOn"]),
                        IssueStatus = Convert.ToString(dr["IssueStatus"]) ?? string.Empty,
                        TicketStatus = Convert.ToString(dr["TicketStatus"]) ?? string.Empty,
                        UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]),

                        AssignToDetails = new Empdata
                        {
                            EmpID = Convert.ToInt32(dr["AssignTO"]),
                            EmployeeID = Convert.ToString(dr["AssignToEmployeeId"]) ?? string.Empty,
                            Name = Convert.ToString(dr["AssignToUserName"]) ?? string.Empty
                        },

                        CreatedByDetails = new Empdata
                        {
                            EmpID = Convert.ToInt32(dr["CreatedBy"]),
                            EmployeeID = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                            Name = Convert.ToString(dr["UserName"]) ?? string.Empty,
                        },

                        MatrixRole = Convert.ToString(dr["MatrixRole"]) ?? string.Empty,
                        BU = Convert.ToString(dr["BUName"]) ?? string.Empty
                    };
                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public List<Empdata> GetSalesTicketProcessUser(int ticketId, int ProcessId)
        {
            List<Empdata> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesTicketProcessUser(ticketId, ProcessId);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    Empdata details = new()
                    {
                        EmpID = Convert.ToInt32(dr["MatrixUserID"]),
                        EmployeeID = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                        Name = Convert.ToString(dr["UserName"]) ?? string.Empty,
                        MatrixUserID = Convert.ToInt32(dr["UserID"]),
                        UserDisplayName = Convert.ToString(dr["UserName"]) + "-" + Convert.ToString(dr["EmployeeId"])
                    };

                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public List<SalesTicketStatus> GetSalesTicketStatusMaster()
        {
            List<SalesTicketStatus> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesTicketStatusMaster();
            if (ds?.Tables?.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    SalesTicketStatus status = new()
                    {
                        StatusID = Convert.ToInt32(dr["StatusID"]),
                        StatusName = Convert.ToString(dr["StatusName"]) ?? string.Empty,
                        Sequence = 1
                    };
                    objlist.Add(status);
                }
            }
            return objlist;
        }

        public List<Process> GetSalesProcessMaster(long userId)
        {
            List<Process> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesProcessMaster(userId);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    Process details = new()
                    {
                        SourceID = Convert.ToInt32(dr["ProcessID"]),
                        Name = Convert.ToString(dr["ProcessName"]) ?? string.Empty
                    };
                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public List<Issue> GetSalesIssueMaster()
        {
            List<Issue> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesIssueMaster();
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    Issue details = new()
                    {
                        IssueID = Convert.ToInt32(dr["IssueID"]),
                        ISSUENAME = Convert.ToString(dr["IssueName"]) ?? string.Empty,
                        SourceID = Convert.ToInt32(dr["ProcessID"])
                    };
                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public Result AssignSalesTicket(SalesTicketAssignment obj)
        {
            Result result = SalesTicketDAL.AssignSalesTicket(obj);
            if (result.Status > 0)
            {
                SendInternalFeedbackCommunication(obj.TicketID, "Ticket assigned", obj.AsssignByUserID, SalesCommon.CommType.Assignment);
            }
            return result;
        }

        public List<SalesTicketlog> GetSalesTicketLog(int ticketId, int logtype, int userId)
        {
            List<SalesTicketlog> objlist = [];
            DataSet ds = SalesTicketDAL.GetSalesTicketLog(ticketId, logtype, userId);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    SalesTicketlog details = new()
                    {
                        FieldName = Convert.ToString(dr["FieldName"]) ?? string.Empty,
                        OldValue = Convert.ToString(dr["OldValue"]) ?? string.Empty,
                        NewValue = Convert.ToString(dr["NewValue"]) ?? string.Empty,
                        CreatedOn = Convert.ToDateTime(dr["CreatedOn"]),
                        CreatedByName = Convert.ToString(dr["UserName"]) + "-" + Convert.ToString(dr["EmployeeId"])
                    };

                    objlist.Add(details);
                }
            }
            return objlist;
        }

        public Empdata[] GetSalesTicketUserDetails(long agentId)
        {
            DataSet ds = SalesTicketDAL.GetSalesTicketUserDetails(agentId);
            Empdata[] EMPData = null;
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {

                DataRow dr = ds.Tables[0].Rows[0];
                EMPData = new Empdata[1] {
                    new() {
                        EmpID = Convert.ToInt32(dr["UserID"]),
                        EmployeeID = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                        Name = Convert.ToString(dr["UserName"]) ?? string.Empty,
                        RoleName = Convert.ToString(dr["RoleName"]) ?? string.Empty,
                        RoleID = Convert.ToInt32(dr["RoleId"]),
                        ProcessID=Convert.ToInt32(dr["ProcessID"]),
                        BU=Convert.ToInt32(dr["BU"]),
                        Userlevel=Convert.ToByte(dr["Userlevel"])

                    }};
            }
            return EMPData;
        }

        public Result UpdateSalesUserProfile(Empdata obj)
        {
            return SalesTicketDAL.UpdateSalesUserProfile(obj);
        }

        private static bool CheckUserAccesslevel(int userId, int type)
        {
            bool IsAllowed = true;
            if (userId > 0 && type != 1)
            {
                DataSet ds = SalesTicketDAL.GetSalesTicketUserRole(userId);
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    int TicketRoleID = Convert.ToInt32(ds.Tables[0].Rows[0]["TicketRoleID"]);
                    if (
                        (type == 2 || type == 3 || type == 4)
                        && TicketRoleID != 1)
                    {
                        IsAllowed = false;
                    }
                }
            }

            return IsAllowed;
        }

        private static async Task<int> SendInternalFeedbackCommunication(int ticketId, string message, long createdBy, SalesCommon.CommType type)
        {
            await Task.Run(() =>
            {
                sendcommunicationResponse obj = new();
                SalesTicketModel TicketDetails = GetSalesDetailsForCommunication(ticketId);
                if (TicketDetails != null &&
                    TicketDetails.AssignToDetails != null &&
                    !string.IsNullOrEmpty(TicketDetails.AssignToDetails.EmployeeID)
                    )
                {
                    bool isAllowed = false;
                    string text = GetEmailXslt(type);



                    if (createdBy == TicketDetails.CreatedByDetails.EmpID
                        && (type == SalesCommon.CommType.Creation))
                    {
                        isAllowed = true;
                    }
                    else if (createdBy == TicketDetails.CreatedByDetails.EmpID
                       && (type == SalesCommon.CommType.Update))
                    {
                        isAllowed = true;
                    }
                    else if (type == SalesCommon.CommType.Assignment &&
                        createdBy != TicketDetails.AssignToDetails.EmpID)
                    {
                        isAllowed = true;
                    }
                    if (isAllowed)
                    {
                        StringBuilder Content = new(GetEmailXslt(type));
                        if (TicketDetails.Commentlist != null
                           && TicketDetails.Commentlist.Count > 0
                          && string.IsNullOrEmpty(message))
                        {
                            message = TicketDetails.Commentlist[0].Comment;
                        }

                        string ticketurl = "feedbackurl".AppSettings() + "/LandingPage/" + ticketId + "/ticketview";
                        if (!string.IsNullOrEmpty(text))
                        {
                            Content.Replace("[Name]", TicketDetails.CreatedByDetails.Name);
                            Content.Replace("[Ecode]", TicketDetails.CreatedByDetails.EmployeeID);
                            Content.Replace("[message]", message);
                            Content.Replace("[url]", ticketurl);
                            Content.Replace("[url]", ticketurl);
                            Content.Replace("[Process]", TicketDetails.Process);
                            Content.Replace("[subProcess]", TicketDetails.IssueStatus);

                        }
                        obj = new sendcommunicationResponse
                        {
                            TriggerName = "ALL_MTX_Em_InternalFeedback",
                            CommunicationType = 1,
                            ProductId = 0,
                            To = new string[] { TicketDetails.AssignToDetails.AssignToEmailId },
                            //Bcc= new string[1] { "<EMAIL>"},
                            InputData = new Inputdata
                            {
                                Subject = "Feedback from " + TicketDetails.ProductName + " Sales -" + TicketDetails.Process + " - " + TicketDetails.TicketDisplayID,
                                Content = Content.ToString(),
                            }
                        };
                        string Json = JsonConvert.SerializeObject(obj);
                        string url = "sendcommunicationurl".AppSettings();

                        var headers = new Dictionary<object, object>
                        {
                            {"Requestingsystem", "Matrix"},
                            {"token", "commtoken".AppSettings()}
                        };

                        var response = CommonAPICall.CallAPI(url, Json, "POST", 3000, "application/json", headers);
                    }
                }
            });
            return 0;
        }


        public static SalesTicketModel GetSalesDetailsForCommunication(int ticketId)
        {
            SalesTicketModel details = new();
            DataSet ds = SalesTicketDAL.GetSalesDetailsForCommunication(ticketId);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {

                    details.TicketDisplayID = Convert.ToString(dr["TicketDisplayID"]) ?? string.Empty;
                    details.TicketID = Convert.ToString(dr["TicketId"]) ?? string.Empty;
                    details.ProcessID = Convert.ToInt32(dr["SourceId"]);
                    details.Process = Convert.ToString(dr["Source"]) ?? string.Empty;
                    details.CreatedOn = Convert.ToDateTime(dr["CreatedOn"]);
                    details.IssueStatus = Convert.ToString(dr["IssueStatus"]) ?? string.Empty;
                    details.TicketStatus = Convert.ToString(dr["TicketStatus"]) ?? string.Empty;
                    details.UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]);
                    details.StatusID = Convert.ToInt32(dr["Status"]);
                    details.IssueID = Convert.ToInt32(dr["IssueId"]);

                    details.UpdatedOn = Convert.ToDateTime(dr["UpdatedOn"]);
                    details.ProductName = Convert.ToString(dr["ProductName"]) ?? string.Empty;
                    details.MatrixRole = Convert.ToString(dr["MatrixRole"]) ?? string.Empty;
                    details.BU = Convert.ToString(dr["BUName"]) ?? string.Empty;
                    details.AssignToDetails = new Empdata
                    {
                        EmpID = Convert.ToInt32(dr["AssignTO"]),
                        EmployeeID = Convert.ToString(dr["AssignToEmployeeId"]) ?? string.Empty,
                        Name = Convert.ToString(dr["AssignToUserName"]) ?? string.Empty,
                        AssignToEmailId = Convert.ToString(dr["AssignToEmailId"]) ?? string.Empty
                    };


                    details.CreatedByDetails = new Empdata
                    {
                        EmpID = Convert.ToInt32(dr["CreatedBy"]),
                        EmployeeID = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                        Name = Convert.ToString(dr["UserName"]) ?? string.Empty,
                        MatrixRoleId = Convert.ToInt32(dr["MatrixRoleId"]),
                        ContactNo = dr["UserContactNo"].ToString() == "" ? 0 : Convert.ToInt64(dr["UserContactNo"])

                    };



                    details.ManagerDetails = new Empdata
                    {

                        EmployeeID = Convert.ToString(dr["ManagerEcode"]) ?? string.Empty,
                        Name = Convert.ToString(dr["ManagerUserName"]) ?? string.Empty,
                        ManagerNo = dr["ManagerNo"].ToString() == "" ? 0 : Convert.ToInt64(dr["ManagerNo"])
                    };

                }

                List<TicketComments> list = new List<TicketComments>();
                foreach (DataRow dr in ds.Tables[1].Rows)
                {
                    TicketComments Comments = new()
                    {
                        Comment = Convert.ToString(dr["Comment"]) ?? string.Empty,
                        ReplyType = 1,
                        FileURL = Convert.ToString(dr["FileUrl"]) ?? string.Empty,
                        FileName = Convert.ToString(dr["FileName"]) ?? string.Empty,
                        RefId = Convert.ToString(dr["RefId"]) ?? "0",
                        User = new SalesTicketUser
                        {
                            EmployeeId = Convert.ToString(dr["EmployeeId"]) ?? string.Empty,
                            UserName = Convert.ToString(dr["UserName"]) ?? string.Empty
                        },
                        CreatedOn = Convert.ToDateTime(dr["CreatedOn"])
                    };
                    list.Add(Comments);
                }
                if (list != null && list.Count > 0)
                {
                    details.Commentlist = list.OrderBy(e => e.CreatedOn).ToList();
                }
            }
            return details;
        }

        public Dictionary<string, TicketGroup> GetSpanCreatedTickets(int userId, int type)
        {
            Dictionary<string, TicketGroup> objres = new();
            try
            {
                if (userId > 0 && CheckUserAccesslevel(userId, type))
                {
                    string URL = "MRSCoreURL".AppSettings().ToString();
                    URL += "Feedback/GetUserHierarchyTicket";

                    Dictionary<object, object> ObjHeaders = new()
                    {
                        { "AgentId", userId.ToString() },
                        { "clientKey", "MRSCoreclientKey".AppSettings().ToString() },
                        { "AuthKey", "MRSCoreAuthKey".AppSettings().ToString() },
                        { "source", "MRSCoresource".AppSettings().ToString() }
                    };

                    var result = CommonAPICall.CallAPI(URL, "", "GET", Convert.ToInt32("MRSCoreTimeout".AppSettings()), "application/json", ObjHeaders);
                    objres = JsonConvert.DeserializeObject<Dictionary<string, TicketGroup>>(result);
                }
            }
            catch(Exception ex)
            {
                throw new Exception("Error getting span tickets: " + ex.Message);
            }  
            return objres;
        }

        private async void PostTicketNotification(SalesTicketModel obj)
        {
            if (obj.StatusID > 0)
            {
                SalesTicketModel salesTicket = GetSalesTicketDetailsByID(Convert.ToInt32(obj.TicketID));

                List<Notification> Notificationlist = new();
                Notification noti = new();

                string api = "matrixNotification".AppSettings();
                noti.id = Guid.NewGuid().ToString();
                noti.type = "genie_mtx_notification";
                noti._event = salesTicket.TicketDisplayID;
                noti.text = "Remarks have been changed.Please review";
                if (obj.IsStatusChanged == true)
                {
                    noti.text = "Status has been changed.Please review";
                }
                noti.empID = salesTicket.CreatedByDetails.EmployeeID.ToString();

                noti.link = "matrixticket".AppSettings() + "/LandingPage/" + obj.TicketID + "/notification";
                Notificationlist.Add(noti);

                var json = JsonConvert.SerializeObject(Notificationlist);
                json = json.Replace("_event", "event");
                string result = CommonAPICall.CallAPI(api, json, "POST", 9000, "application/json", null);
            }
        }
        
        public Result ReAssignSalesTicket(SalesTicketAssignment obj)
        {
            Result result = SalesTicketDAL.ReAssignSalesTicket(obj);
            return result;
        }

        public List<MailAttachments> UploadFile(List<MailAttachments> objMailAttachments)
        {
            foreach (MailAttachments objData in objMailAttachments)
            {
                MiscDocUploadRequest request = new()
                {
                    LeadId = 0,
                    CustomerId = 0,
                    EnquiryId = "0",
                    Type = "MatrixMisc",
                    RefId = Guid.NewGuid().ToString()
                };

                MiscDocUploadResponse obj = FileUpload.UploadMiscDocument(request, objData);
                objData.AttachmentURL = obj.DocId;
                objData.RefId = request.RefId;
            }

            return objMailAttachments;
        }

        public static string GetEmailXslt(SalesCommon.CommType type)
        {

            if (type == SalesCommon.CommType.Creation)
            {
                return File.ReadAllText(Convert.ToString(AppDomain.CurrentDomain.BaseDirectory + "\\email.html"));
            }
            else
            {
                return File.ReadAllText(Convert.ToString(AppDomain.CurrentDomain.BaseDirectory + "\\emailUpdate.html"));
            }
        }

    }

    #region 
    public class SalesCommon
    {
        public enum CommType
        {
            Creation = 0,
            Assignment = 1,
            Update = 2
        }

        public static string Encrypt(string clearText)
        {
            try
            {

                var plainTextBytes = Encoding.UTF8.GetBytes(clearText);
                return Convert.ToBase64String(plainTextBytes);
            }
            catch (Exception ex)
            {

                return null;
            }
        }


        public static string Decrypt(string cipherText)
        {
            try
            {
                var base64EncodedBytes = Convert.FromBase64String(cipherText);
                return Encoding.UTF8.GetString(base64EncodedBytes);
            }
            catch (Exception ex)
            {

                return null;
            }
        }

        public static User GetUserDetailsByCookie(string cname)
        {
            User user = new();
            byte[] data = Convert.FromBase64String(cname);
            string decodedString = Encoding.UTF8.GetString(data);
            if (decodedString != null)
            {
                user = JsonConvert.DeserializeObject<User>(decodedString);
            }
            return user;
        }
    }
    #endregion
} 