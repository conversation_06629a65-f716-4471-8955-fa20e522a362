using System.Collections.Generic;
using System.Threading.Tasks;
using PropertyLayers;

namespace BusinessLogicLayer
{
    public interface ISalesTicketBAL
    {
        Result CreateTicket(SalesTicketModel obj);
        List<SalesTicketStatus> GetSalesTicketCount(int userId, int status, int type);
        SalesTicketModel GetSalesTicketDetailsByID(int ticketId);
        Result UpdateSalesTicketByID(SalesTicketModel obj);
        List<SalesTicketModel> SalesTicketDashboardSearch(SearchTicket obj);
        List<SalesTicketStatus> GetSalesTicketStatusMaster();
        Result AssignSalesTicket(SalesTicketAssignment obj);
        List<Process> GetSalesProcessMaster(long userId);

        List<Issue> GetSalesIssueMaster();

        List<SalesTicketModel> GetSalesTicketByAgentId(int userId, int status, int type);

        List<Empdata> GetSalesTicketProcessUser(int ticketId, int ProcessId);

        List<SalesTicketlog> GetSalesTicketLog(int ticketId, int logtype, int userId);

        List<MailAttachments> UploadFile(List<MailAttachments> objMailAttachments);

        Dictionary<string, TicketGroup> GetSpanCreatedTickets(int userId, int type);

        Result ReAssignSalesTicket(SalesTicketAssignment obj);

        Empdata[] GetSalesTicketUserDetails(long agentId);

        Result UpdateSalesUserProfile(Empdata obj);

    }
} 